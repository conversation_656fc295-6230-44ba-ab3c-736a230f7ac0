<template>
  <div class="work-report-container">
    <!-- 顶部标签页导航 -->
    <div class="tab-nav">
      <back-button />
      <div class="tab-item" @click="goToReport">汇报</div>
      <div class="tab-item" @click="goToApproval">审批</div>
      <div class="tab-item active">统计</div>
    </div>

    <!-- 统计区域 -->
    <div class="statistics-container">
      <!-- 筛选条件 -->
      <div class="filter-bar">
        <!-- 第一行：数据范围选择和时间范围选择 -->
        <div class="filter-row">
          <div class="filter-item date-filter">
            <span class="filter-label">时间范围</span>
            <div class="time-mode-selector">
              <span 
                @click="switchTimeMode('day')" 
                :class="{ active: timeMode === 'day' }"
              >日</span>
              <span 
                @click="switchTimeMode('week')" 
                :class="{ active: timeMode === 'week' }"
              >周</span>
              <span 
                @click="switchTimeMode('month')" 
                :class="{ active: timeMode === 'month' }"
              >月</span>
            </div>
          </div>
          
          <div class="filter-item data-scope-filter">
            <div class="data-scope-selector" ref="dataScopeDropdown">
              <div class="data-scope-trigger" @click="toggleDataScopeDropdown">
                <span class="data-scope-label">{{ currentDataScopeLabel }}</span>
                <span class="dropdown-arrow" :class="{ 'arrow-up': showDataScopeDropdown }">▼</span>
              </div>
              <div v-show="showDataScopeDropdown" class="data-scope-dropdown">
                <div 
                  v-for="option in dataScopeOptions" 
                  :key="option.value"
                  class="dropdown-option"
                  :class="{ 'selected': option.value === dataScope }"
                  @click="selectDataScope(option.value)"
                >
                  {{ option.label }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 第二行：时间导航和筛选按钮 -->
        <div class="filter-row">
          <div class="filter-item time-navigation">
            <div class="time-nav-container">
              <div class="nav-btn nav-btn-left" @click="navigateTime('prev')">
                <span class="arrow-icon">‹</span>
              </div>
              <div class="current-time-display">{{ currentTimeDisplay }}</div>
              <div class="nav-btn nav-btn-right" @click="navigateTime('next')">
                <span class="arrow-icon">›</span>
              </div>
            </div>
          </div>
          
          <van-button type="primary" size="small" class="filter-btn" @click="applyFilter">查询</van-button>
        </div>
      </div>

      <!-- 总计信息 -->
      <div class="total-info">
        <div class="info-item">
          <div class="info-value">{{ reportHours }}</div>
          <div class="info-label">汇报工时</div>
        </div>
        <div class="info-item">
          <div class="info-value">{{ overtimeHours }}</div>
          <div class="info-label">加班工时</div>
        </div>
        <div class="info-item">
          <div class="info-value">{{ confirmedHours }}</div>
          <div class="info-label">确认工时</div>
        </div>
        <div class="info-item clickable-card" @click="showUnauditedDetails">
          <div class="info-value" style="color: #ff976a;">{{ unauditedHours }}</div>
          <div class="info-label" >未审核工时</div>
        </div>
      </div>

      <!-- 工时汇总表格 -->
      <div class="project-summary-table">
        <div class="section-title">
          工时汇总
          <span class="project-count">({{ sortedProjectSummaries.length }}个项目)</span>
          <van-icon 
            :name="isFullscreen ? 'shrink' : 'expand-o'" 
            class="fullscreen-btn"
            @click="toggleFullscreen" 
          />
        </div>
        
        <!-- 修改这里：将表格包装在一个专门的全屏容器中 -->
        <div class="fullscreen-wrapper" ref="fullscreenWrapper">
          <!-- 退出全屏按钮 -->
          <div v-show="isFullscreen" class="exit-fullscreen-btn" @click="toggleFullscreen">
            <van-icon name="shrink" />
            <span>退出全屏</span>
          </div>
          
          <div class="table-container">
            <table class="summary-table">
              <thead>
                <tr>
                  <th class="project-name-col">项目名称</th>
                  <th class="task-count-col">任务数</th>
                  <th class="planned-hours-col">计划<br>工时</th>
                  <th class="report-hours-col">汇报<br>工时</th>
                  <th class="overtime-hours-col">加班<br>工时</th>
                  <th class="confirm-hours-col">确认<br>工时</th>
                  <th class="unaudited-hours-col">未审核<br>工时</th>
                  <th class="cost-hours-col">工时<br>耗费</th>
                  <th class="progress-col">项目<br>进度</th>
                  <th class="delay-status-col">是否<br>延期</th>
                  <th class="total-hours-col">总计<br>工时</th>
                  <th class="action-col">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(project, index) in sortedProjectSummaries" :key="index" class="summary-row">
                  <td class="project-name">
                    <div class="project-name-text">{{ project.projectName }}</div>
                  </td>
                  <td class="task-count">
                    <span class="count-badge">{{ project.taskCount }}</span>
                  </td>
                  <td class="planned-hours">
                    <span class="hours-value">{{ project.FPlanWorkHours }}</span>
                  </td>
                  <td class="report-hours">
                    <span class="hours-value">{{ project.reportHours }}</span>
                  </td>
                  <td class="overtime-hours">
                    <span class="hours-value">{{ project.overtimeHours }}</span>
                  </td>
                  <td class="confirm-hours">
                    <span class="hours-value">{{ project.confirmHours }}</span>
                  </td>
                  <td class="unaudited-hours">
                    <span class="hours-value">{{ getProjectUnauditedHours(project.projectName) }}</span>
                  </td>
                  <td class="cost-hours">
                    <span class="hours-value">--</span>
                  </td>
                  <td class="progress">
                    <span class="progress-value">{{ getProjectProgress(project.projectName) }}%</span>
                  </td>
                  <td class="delay-status">
                    <span class="delay-value">--</span>
                  </td>
                  <td class="total-hours">
                    <span class="total-value">{{ project.totalHours }}</span>
                  </td>
                  <td class="action">
                    <van-button 
                      type="primary" 
                      size="mini" 
                      class="detail-btn"
                      @click="showProjectDetails(project)"
                    >
                      详情
                    </van-button>
                  </td>
                </tr>
              </tbody>
            </table>
            
            <!-- 空状态 -->
            <div v-if="sortedProjectSummaries.length === 0" class="empty-state">
              <div class="empty-text">暂无项目数据</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务明细弹窗 -->
      <van-popup 
        v-model="showTaskDetails" 
        position="bottom" 
        :style="{ height: '70%' }"
        class="task-details-popup"
      >
        <div class="popup-header">
          <div class="popup-title">
            {{ popupTitle }}
          </div>
          <van-icon name="cross" @click="showTaskDetails = false" class="close-icon" />
        </div>
        
        <div class="popup-content" v-if="selectedProject || detailMode === 'status'">
          <!-- 项目汇总信息 -->
          <div class="project-summary-info" v-if="selectedProject">
            <div class="summary-metrics">
              <div class="metric-item">
                <div class="metric-label">计划工时</div>
                <div class="metric-value">{{ selectedProject.FPlanWorkHours }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">汇报工时</div>
                <div class="metric-value">{{ selectedProject.reportHours }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">加班工时</div>
                <div class="metric-value">{{ selectedProject.overtimeHours }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">已报工时</div>
                <div class="metric-value">
                  <span v-if="isCurrentTasksReportedHoursLoading">加载中...</span>
                  <span v-else>{{ currentTasksReportedHoursSum }}</span>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">确认工时</div>
                <div class="metric-value">{{ selectedProject.confirmHours }}</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">任务达成率</div>
                <div class="metric-value">--<!-- 占位，后续补数据 --></div>
              </div>
            </div>
          </div>
          
          <!-- 任务明细列表 -->
          <div class="task-details-list">
            <div class="task-detail-item" v-for="(task, index) in getDisplayTasks(selectedProject ? selectedProject.projectName : null)" :key="index">
              <div class="task-header">
                <div class="task-name">
                  {{ (task.originalTaskName && task.originalTaskName.trim()) || (task.originalProjectName && task.originalProjectName.trim()) || '未知任务' }}
                </div>
                <div class="task-status" :class="getStatusClass(task.status)">
                  {{ formatTaskStatus(task.status) }}
                </div>
              </div>
              
              <div class="task-info">
                <div class="info-row">
                  <span class="info-label">汇报日期：</span>
                  <span class="info-value">{{ task.reportDate || task.workDate }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">提交日期：</span>
                  <span class="info-value">{{ task.submitDate || '--' }}</span>
                </div>
              </div>
              
              <div class="task-metrics">
                <div class="metric-grid">
                  <!-- 第一行：计划工时、已报工时、岗位 -->
                  <div class="metric">
                    <span class="label">计划工时</span>
                    <span class="value">{{ task.planHours || '--' }}</span>
                  </div>
                  <div class="metric">
                    <span class="label">已报工时</span>
                    <span class="value">
                      <span v-if="reportedHoursLoading && task.realReportedHours === undefined">加载中...</span>
                      <span v-else>{{ task.realReportedHours || task.reportedHours || '--' }}</span>
                    </span>
                  </div>
                  <div class="metric">
                    <span class="label">岗位</span>
                    <span class="value">{{ task.position || '--' }}</span>
                  </div>
                  
                  <!-- 第二行：汇报工时、加班工时、进度 -->
                  <div class="metric">
                    <span class="label">汇报工时</span>
                    <span class="value">{{ task.reportHours }}</span>
                  </div>
                  <div class="metric">
                    <span class="label">加班工时</span>
                    <span class="value">{{ task.overtimeHours }}</span>
                  </div>
                  <div class="metric">
                    <span class="label">进度</span>
                    <span class="value">{{ task.progress }}%</span>
                  </div>
                  
                  <!-- 第三行：确认工时 -->
                  <div class="metric">
                    <span class="label">确认工时</span>
                    <span class="value">{{ task.confirmHours || 0 }}</span>
                  </div>
                </div>
              </div>
              
              <div class="task-content" v-if="task.content">
                <div class="content-value">{{ task.content }}</div>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="getDisplayTasks(selectedProject ? selectedProject.projectName : null).length === 0" class="empty-tasks">
              <div class="empty-text">{{ emptyStateText }}</div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
  </div>
</template>

<script>
import BackButton from '../components/BackButton.vue';
import { getStatisticsData, getUserDueProjectTasks } from '../api/report';

export default {
  name: 'WorkReportStatistics',
  components: {
    BackButton
  },
  data() {
    return {
      // 金蝶企业蓝
      kingdeeBlue: '#276ff5',
      
      // 数据范围选择
      dataScope: 'my_hours', // 'my_hours', 'my_department', 'my_projects'
      dataScopeOptions: [
        { value: 'my_hours', label: '我的工时' },
        { value: 'my_department', label: '我管理的部门' },
        { value: 'my_projects', label: '我管理的项目' }
      ],
      showDataScopeDropdown: false,
      
      // 统计数据
      reportHours: 0,
      overtimeHours: 0,
      confirmedHours: 0,
      unauditedHours: 0,
      FPlanWorkHours: 0,
      FSUMTIME: 0,

      // 已报工时数据（使用新的API接口）
      reportedHours: 0,
      reportedTasksData: [],

      // 时间模式选择
      timeMode: 'week', // 'day', 'week', 'month'
      
      // 时间导航状态
      currentDate: new Date(), // 基准日期
      dayOffset: 0, // 日偏移量: -1=昨天, 0=今天
      weekOffset: 0, // 周偏移量: -1=上周, 0=本周, 1=下周
      monthOffset: 0, // 月偏移量: -1=上月, 0=本月, 1=下月
      
      // API相关
      loading: false,
      error: null,
      lastUpdated: null,

      // 任务明细卡片数据
      taskDetailCards: [],

      // 工时汇总卡片数据
      projectSummaries: [],

      // 导航状态
      navigating: false,

      // 任务详情弹窗控制
      showTaskDetails: false,
      selectedProject: null,
      
      // 弹窗显示模式控制
      detailMode: 'project', // 'project' 或 'status'
      selectedStatus: null, // 当前选中的状态值
      isFullscreen: false, // 添加全屏状态标记
      
      // 新增：已报工时数据缓存
      reportedHoursCache: {},
      reportedHoursLoading: false
    };
  },
  computed: {
    // 根据当前时间模式显示的时间范围描述
    timeRangeDisplay() {
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      const day = today.getDate();
      
      if (this.timeMode === 'day') {
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      } else if (this.timeMode === 'week') {
        return '本周';
      } else {
        return `${year}年${month}月`;
      }
    },
    
    // 当前数据范围显示文本
    currentDataScopeLabel() {
      const option = this.dataScopeOptions.find(item => item.value === this.dataScope);
      return option ? option.label : '我的工时';
    },
    
    // 当前时间显示文本
    currentTimeDisplay() {
      if (this.timeMode === 'day') {
        const targetDate = new Date(this.currentDate);
        targetDate.setDate(targetDate.getDate() + this.dayOffset);
        
        if (this.dayOffset === -1) {
          return `昨天 (${this.formatDate(targetDate)})`;
        } else if (this.dayOffset === 0) {
          return `今天 (${this.formatDate(targetDate)})`;
        } else {
          return this.formatDate(targetDate);
        }
      } else if (this.timeMode === 'week') {
        const weekInfo = this.getWeekInfo(this.weekOffset);
        return `${weekInfo.year}年第${weekInfo.weekNumber}周 (${weekInfo.startDate}-${weekInfo.endDate})`;
      } else { // month
        const targetDate = new Date(this.currentDate);
        targetDate.setMonth(targetDate.getMonth() + this.monthOffset);
        
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        
        return `${year}年${month.toString().padStart(2, '0')}月`;
      }
    },

    // 弹窗标题
    popupTitle() {
      if (this.detailMode === 'project' && this.selectedProject) {
        return `${this.selectedProject.projectName} - 工时明细`;
      } else if (this.detailMode === 'status') {
        return '未审核工时详情';
      } else {
        return '我汇报的工时';
      }
    },

    // 空状态文本
    emptyStateText() {
      if (this.detailMode === 'project') {
        return '该项目暂无任务明细';
      } else if (this.detailMode === 'status') {
        return '暂无未审核的工时记录';
      }
      return '暂无数据';
    },

    // 排序后的项目汇总数据：普通项目按工时降序，临时任务和耗费项目固定在最后
    sortedProjectSummaries() {
      if (!this.projectSummaries || this.projectSummaries.length === 0) {
        return [];
      }

      // 分离普通项目和特殊项目
      const normalProjects = [];
      const tempProject = [];
      const timecostProject = [];

      this.projectSummaries.forEach(project => {
        if (project.projectName === '临时任务') {
          tempProject.push(project);
        } else if (project.projectName === '耗费') {
          timecostProject.push(project);
        } else {
          normalProjects.push(project);
        }
      });

      // 普通项目按总工时降序排列
      normalProjects.sort((a, b) => b.totalHours - a.totalHours);

      // 组合结果：普通项目 + 临时任务 + 耗费
      const result = [...normalProjects, ...tempProject, ...timecostProject];
      
      return result;
    },

    // 计算当前显示任务列表的已报工时总和
    currentTasksReportedHoursSum() {
      if (!this.showTaskDetails) {
        return 0;
      }

      let tasks = [];
      if (this.detailMode === 'project' && this.selectedProject) {
        tasks = this.taskDetailCards.filter(task => task.projectName === this.selectedProject.projectName);
      } else if (this.detailMode === 'status') {
        tasks = this.taskDetailCards.filter(task => task.status === this.selectedStatus);
      }

      if (tasks.length === 0) {
        return 0;
      }

      // 计算所有任务的已报工时总和
      const totalReportedHours = tasks.reduce((sum, task) => {
        // 优先使用realReportedHours（从API获取的真实数据），其次使用reportedHours
        const reportedHours = task.realReportedHours !== undefined ? task.realReportedHours : (task.reportedHours || 0);
        return sum + (parseFloat(reportedHours) || 0);
      }, 0);

      return totalReportedHours.toFixed(1);
    },

    // 检查当前任务列表是否正在加载已报工时数据
    isCurrentTasksReportedHoursLoading() {
      if (!this.showTaskDetails) {
        return false;
      }

      let tasks = [];
      if (this.detailMode === 'project' && this.selectedProject) {
        tasks = this.taskDetailCards.filter(task => task.projectName === this.selectedProject.projectName);
      } else if (this.detailMode === 'status') {
        tasks = this.taskDetailCards.filter(task => task.status === this.selectedStatus);
      }

      // 检查是否有任务正在加载已报工时数据
      return this.reportedHoursLoading && tasks.some(task => task.realReportedHours === undefined);
    }
  },
  methods: {
    goToReport() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/report') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/report')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到汇报页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    
    // 数据范围选择相关方法
    toggleDataScopeDropdown() {
      this.showDataScopeDropdown = !this.showDataScopeDropdown;
    },
    
    selectDataScope(value) {
      this.dataScope = value;
      this.showDataScopeDropdown = false;
      
      // 选择后重新获取数据
      this.fetchStatisticsData();
    },
    
    handleClickOutside(event) {
      // 检查点击是否在下拉组件外部
      const dropdown = this.$refs.dataScopeDropdown;
      if (dropdown && !dropdown.contains(event.target)) {
        this.showDataScopeDropdown = false;
      }
    },
    
    goToApproval() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/approval') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/approval')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到审批页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    
    // 获取统计数据
    fetchStatisticsData() {
      // 构建筛选参数
      const params = this.buildFilterParams();
      
      // 显示加载提示
      this.loading = true;
      this.$toast.loading({
        message: '加载统计数据...',
        forbidClick: true,
        duration: 0
      });
      
      // 调用API
      getStatisticsData(params, { forceRefresh: true })
        .then(response => {
          // 处理成功响应
          this.processApiResponse(response);
        })
        .catch(error => {
          // 处理错误
          this.error = error.message || '加载统计数据失败';
          this.$toast.fail(this.error);
          console.error('[统计] 获取数据失败:', error);
        })
        .finally(() => {
          this.loading = false;
          this.lastUpdated = new Date();
          this.$toast.clear();
        });
    },
    
    // 构建API筛选参数
    buildFilterParams() {
      const params = {
        // 不设置documentStatus，使用API默认查询条件（已审核和已提交）
      };
      
      // 根据数据范围设置查询参数
      if (this.dataScope === 'my_hours') {
        // 查询我的工时 - 使用当前用户账号和用户实体ID
        const userAccount = localStorage.getItem('UserAccount');
        const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
        const userEntityId = userInfo.userEntityId || userInfo.userId;
        
        if (userAccount) {
          params.reportEmpNumber = userAccount;
        }
        if (userEntityId) {
          params.userEntityId = userEntityId;
        }
      } else if (this.dataScope === 'my_department') {
        // 查询我管理的部门 - 需要添加部门管理者参数
        const userAccount = localStorage.getItem('UserAccount');
        if (userAccount) {
          params.departmentManager = userAccount;
        }
      } else if (this.dataScope === 'my_projects') {
        // 查询我管理的项目 - 需要添加项目管理者参数
        const userAccount = localStorage.getItem('UserAccount');
        if (userAccount) {
          params.projectManager = userAccount;
        }
      }
      
      // 根据时间模式设置日期参数
      if (this.timeMode === 'day') {
        // 日模式 - 单天查询
        const targetDate = new Date(this.currentDate);
        targetDate.setDate(targetDate.getDate() + this.dayOffset);
        params.reportDate = this.formatDate(targetDate);
      } else if (this.timeMode === 'week') {
        // 周模式 - 日期范围查询
        const weekInfo = this.getWeekInfo(this.weekOffset);
        const startParts = weekInfo.startDate.split('/');
        const endParts = weekInfo.endDate.split('/');
        
        params.startDate = `${weekInfo.year}-${startParts[0]}-${startParts[1]}`;
        params.endDate = `${weekInfo.year}-${endParts[0]}-${endParts[1]}`;
      } else {
        // 月模式 - 月份范围查询
        const targetDate = new Date(this.currentDate);
        targetDate.setMonth(targetDate.getMonth() + this.monthOffset);
        
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        
        // 获取月份的第一天和最后一天
        const firstDay = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0);
        
        params.startDate = this.formatDate(firstDay);
        params.endDate = this.formatDate(lastDay);
      }
      
      return params;
    },
    
    // 处理API响应数据
    processApiResponse(response) {
      if (response && response.success) {
        // 更新统计数据
        this.reportHours = response.data.summary.reportHours || 0;
        this.overtimeHours = response.data.summary.overtimeHours || 0;
        this.confirmedHours = response.data.summary.confirmedHours || 0;
        this.unauditedHours = response.data.summary.unauditedHours || 0;
        this.FPlanWorkHours = response.data.summary.FPlanWorkHours || 0;
        this.FSUMTIME = response.data.summary.FSUMTIME || 0;
        
        // 更新任务明细
        this.taskDetailCards = response.data.taskDetails || [];
        
        // 更新工时汇总卡片数据
        this.projectSummaries = response.data.projectSummaries || [];
        
        // 延迟加载项目汇总的已报工时数据，避免阻塞UI渲染
        this.$nextTick(() => {
          this.loadReportedHoursForItems(this.projectSummaries);
        });
        
        // 显示成功提示
        this.$toast.success('数据加载完成');
      } else {
        // 处理失败响应
        this.error = response.message || '获取统计数据失败';
        this.$toast.fail(this.error);
        console.warn('[统计] API返回错误:', response);
      }
    },
    
    switchTimeMode(mode) {
      this.timeMode = mode;
      
      // 切换模式时重置偏移量
      this.dayOffset = 0;
      this.weekOffset = 0;
      this.monthOffset = 0;
      
      // 获取新的统计数据
      this.fetchStatisticsData();
    },
    applyFilter() {
      // 调用API获取统计数据
      this.fetchStatisticsData();
    },

    // 获取已报工时数据（使用新的API接口）
    async fetchReportedHoursData(taskOrProjectId) {
      if (!taskOrProjectId) {
        console.warn('[统计] 获取已报工时数据: 缺少任务或项目ID');
        return;
      }

      try {
        console.log(`[统计] 开始获取已报工时数据，任务/项目ID: ${taskOrProjectId}`);

        // 显示加载提示
        this.$toast.loading({
          message: '获取已报工时...',
          forbidClick: true,
          duration: 0
        });

        // 调用新的已报工时API
        const response = await getUserDueProjectTasks(taskOrProjectId, {
          forceRefresh: true,
          timeout: 30000
        });

        this.$toast.clear();

        if (response && response.success) {
          // 更新已报工时数据
          this.reportedHours = response.data.reportedHours || 0;
          this.reportedTasksData = response.data.tasks || [];

          console.log(`[统计] 已报工时数据获取成功: ${this.reportedHours}h, 任务数量: ${this.reportedTasksData.length}`);

          // 显示成功提示
          this.$toast.success(`已报工时: ${this.reportedHours}h`);
        } else {
          // 处理失败响应
          const errorMsg = response?.message || '获取已报工时数据失败';
          this.$toast.fail(errorMsg);
          console.warn('[统计] 获取已报工时数据失败:', response);
        }
      } catch (error) {
        this.$toast.clear();
        const errorMsg = error.message || '获取已报工时数据失败';
        this.$toast.fail(errorMsg);
        console.error('[统计] 获取已报工时数据异常:', error);
      }
    },

    // 示例：获取特定任务的已报工时（可以在需要的地方调用）
    async getTaskReportedHours(taskId) {
      if (!taskId) {
        console.warn('[统计] 获取任务已报工时: 缺少任务ID');
        return 0;
      }

      try {
        const response = await getUserDueProjectTasks(taskId);
        if (response && response.success) {
          return response.data.reportedHours || 0;
        }
        return 0;
      } catch (error) {
        console.error('[统计] 获取任务已报工时失败:', error);
        return 0;
      }
    },

    // 示例：获取特定项目的已报工时（可以在需要的地方调用）
    async getProjectReportedHours(projectId) {
      if (!projectId) {
        console.warn('[统计] 获取项目已报工时: 缺少项目ID');
        return 0;
      }

      try {
        const response = await getUserDueProjectTasks(projectId);
        if (response && response.success) {
          return response.data.reportedHours || 0;
        }
        return 0;
      } catch (error) {
        console.error('[统计] 获取项目已报工时失败:', error);
        return 0;
      }
    },
    // 时间导航
    navigateTime(direction) {
      if (this.timeMode === 'day') {
        if (direction === 'prev') {
          this.dayOffset = Math.max(this.dayOffset - 1, -1); // 最多到昨天
        } else {
          this.dayOffset = Math.min(this.dayOffset + 1, 0); // 最多到今天
        }
      } else if (this.timeMode === 'week') {
        if (direction === 'prev') {
          this.weekOffset--;
        } else {
          this.weekOffset++;
        }
      } else { // month
        if (direction === 'prev') {
          this.monthOffset--;
        } else {
          this.monthOffset++;
        }
      }
      
      // 获取新的统计数据
      this.fetchStatisticsData();
    },
    // 获取任务状态样式类
    getStatusClass(status) {
      const statusMap = {
        'A': 'draft',
        'B': 'submitted', 
        'C': 'approved',
        'D': 'reaudit'
      };
      return statusMap[status] || 'draft';
    },
    
    // 格式化任务状态文本
    formatTaskStatus(status) {
      const statusMap = {
        'A': '暂存',
        'B': '已提交',
        'C': '已审核',
        'D': '重新审核'
      };
      return statusMap[status] || '未知';
    },
    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },
    getWeekInfo(weekOffset) {
      const today = new Date();
      
      // 获取当前周的起始日期（周一）
      const dayOfWeek = today.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 周日是0，需要特殊处理
      const currentMonday = new Date(today);
      currentMonday.setDate(today.getDate() + mondayOffset);
      
      // 计算目标周的周一
      const targetMonday = new Date(currentMonday);
      targetMonday.setDate(currentMonday.getDate() + (weekOffset * 7));
      
      // 计算目标周的周日
      const targetSunday = new Date(targetMonday);
      targetSunday.setDate(targetMonday.getDate() + 6);
      
      // 计算是一年中的第几周
      const yearStart = new Date(targetMonday.getFullYear(), 0, 1);
      const yearStartDayOfWeek = yearStart.getDay();
      const firstMonday = new Date(yearStart);
      firstMonday.setDate(yearStart.getDate() + (yearStartDayOfWeek === 0 ? 1 : 8 - yearStartDayOfWeek));
      
      const weekNumber = Math.floor((targetMonday.getTime() - firstMonday.getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
      
      return {
        year: targetMonday.getFullYear(),
        weekNumber: Math.max(1, weekNumber),
        startDate: this.formatDateShort(targetMonday),
        endDate: this.formatDateShort(targetSunday)
      };
    },
    
    // 格式化日期为 MM/DD 格式
    formatDateShort(date) {
      if (!date) return '';
      
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${month}/${day}`;
    },
    showProjectDetails(project) {
      this.selectedProject = project;
      this.showTaskDetails = true;
      
      // 只在项目没有已报工时数据时才加载
      if (project && project.realReportedHours === undefined) {
        this.$nextTick(() => {
          this.loadReportedHoursForItems([project]);
        });
      }
    },
    getDisplayTasks(projectName) {
      let tasks = [];
      if (this.detailMode === 'project') {
        tasks = this.taskDetailCards.filter(task => task.projectName === projectName);
      } else if (this.detailMode === 'status') {
        tasks = this.taskDetailCards.filter(task => task.status === this.selectedStatus);
      }
      
      // 延迟加载：只在弹窗显示时才获取已报工时数据
      if (tasks.length > 0 && this.showTaskDetails) {
        // 使用setTimeout避免阻塞UI渲染
        this.$nextTick(() => {
          this.loadReportedHoursForItems(tasks);
        });
      }
      
      return tasks;
    },
    showUnauditedDetails() {
      this.detailMode = 'status';
      this.selectedStatus = 'B'; // 设置为已提交状态
      this.showTaskDetails = true;
    },
    
    // 修改全屏切换方法
    async toggleFullscreen() {
      try {
        const wrapper = this.$refs.fullscreenWrapper;
        if (!wrapper) return;
        
        if (!this.isFullscreen) {
          // 进入全屏
          if (wrapper.requestFullscreen) {
            await wrapper.requestFullscreen();
          } else if (wrapper.webkitRequestFullscreen) {
            await wrapper.webkitRequestFullscreen();
          } else if (wrapper.mozRequestFullScreen) {
            await wrapper.mozRequestFullScreen();
          } else if (wrapper.msRequestFullscreen) {
            await wrapper.msRequestFullscreen();
          }
        } else {
          // 退出全屏
          if (document.exitFullscreen) {
            await document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            await document.webkitExitFullscreen();
          } else if (document.mozCancelFullScreen) {
            await document.mozCancelFullScreen();
          } else if (document.msExitFullscreen) {
            await document.msExitFullscreen();
          }
        }
      } catch (error) {
        console.error('切换全屏失败:', error);
        this.$toast('切换全屏显示失败');
      }
    },
    
    // 获取项目未审核工时（状态为'B'的任务工时总和）
    getProjectUnauditedHours(projectName) {
      if (!this.taskDetailCards || this.taskDetailCards.length === 0) {
        return 0;
      }
      
      const unauditedTasks = this.taskDetailCards.filter(task => 
        task.projectName === projectName && task.status === 'B'
      );
      
      const totalUnauditedHours = unauditedTasks.reduce((sum, task) => {
        const reportHours = parseFloat(task.reportHours) || 0;
        const overtimeHours = parseFloat(task.overtimeHours) || 0;
        return sum + reportHours + overtimeHours;
      }, 0);
      
      return totalUnauditedHours.toFixed(1);
    },
    
    // 获取项目进度（项目下所有任务的平均进度）
    getProjectProgress(projectName) {
      if (!this.taskDetailCards || this.taskDetailCards.length === 0) {
        return 0;
      }
      
      const projectTasks = this.taskDetailCards.filter(task => 
        task.projectName === projectName
      );
      
      if (projectTasks.length === 0) {
        return 0;
      }
      
      const totalProgress = projectTasks.reduce((sum, task) => {
        return sum + (parseFloat(task.progress) || 0);
      }, 0);
      
      const averageProgress = totalProgress / projectTasks.length;
      return averageProgress.toFixed(1);
    },
    
    // 处理全屏状态变化
    handleFullscreenChange() {
      const isFullscreenNow = document.fullscreenElement || 
                             document.webkitFullscreenElement || 
                             document.mozFullScreenElement || 
                             document.msFullscreenElement;
      
      this.isFullscreen = !!isFullscreenNow;
      
      // 如果进入全屏，尝试切换到横屏
      if (this.isFullscreen && screen.orientation && screen.orientation.lock) {
        screen.orientation.lock('landscape').catch(err => {
          console.warn('无法锁定屏幕方向:', err);
        });
      }
      
      // 如果退出全屏，解除屏幕方向锁定
      if (!this.isFullscreen && screen.orientation && screen.orientation.unlock) {
        try {
          screen.orientation.unlock();
        } catch (err) {
          console.warn('无法解除屏幕方向锁定:', err);
        }
      }
    },

    // 获取缓存键
    getCacheKey(item) {
      if (!item) return '';
      
      // 统一ID获取策略：优先使用taskId，其次使用projectId，最后使用项目名称
      const id = item.taskId || item.projectId || item.projectName || '';
      
      // 使用稳定的缓存键，不包含时间戳，确保同一个任务始终使用相同的缓存键
      return `statistics_reported_${id}`;
    },

    // 获取任务的真实已报工时
    async getTaskRealReportedHours(task) {
      if (!task) {
        return 0;
      }

      // 统一ID获取策略：与getCacheKey方法保持一致
      let taskOrProjectId = task.taskId || task.projectId || task.projectName;
      
      if (!taskOrProjectId) {
        console.warn('[统计] 无法获取任务或项目ID，任务对象:', task);
        return 0;
      }

      // 使用统一的缓存键策略
      const cacheKey = this.getCacheKey(task);
      
      // 检查缓存是否存在且未过期
      if (this.reportedHoursCache[cacheKey] !== undefined) {
        const cachedData = this.reportedHoursCache[cacheKey];
        
        // 检查缓存数据是否包含时间戳
        if (cachedData && typeof cachedData === 'object' && cachedData.timestamp) {
          const now = Date.now();
          const age = now - cachedData.timestamp;
          const maxAge = 2 * 60 * 60 * 1000; // 2小时过期
          
          if (age < maxAge) {
            console.log(`[统计] 使用缓存的已报工时: ${cachedData.value}, 缓存键: ${cacheKey}, 年龄: ${(age / 1000).toFixed(2)}秒`);
            this.performanceMonitor.cacheHitCount++;
            return cachedData.value;
          } else {
            console.log(`[统计] 缓存已过期，重新获取数据，缓存键: ${cacheKey}, 年龄: ${(age / 1000).toFixed(2)}秒`);
            delete this.reportedHoursCache[cacheKey];
          }
        } else {
          // 兼容旧格式的缓存数据（直接存储数值）
          console.log(`[统计] 使用缓存的已报工时: ${cachedData}, 缓存键: ${cacheKey}`);
          this.performanceMonitor.cacheHitCount++;
          return cachedData;
        }
      }

      try {
        console.log(`[统计] 调用API获取已报工时，ID: ${taskOrProjectId}, 缓存键: ${cacheKey}`);
        const response = await getUserDueProjectTasks(taskOrProjectId, {
          forceRefresh: false,
          timeout: 10000
        });

        if (response && response.success) {
          const reportedHours = response.data.reportedHours || 0;
          
          // 缓存结果，包含时间戳用于过期检查
          this.reportedHoursCache[cacheKey] = {
            value: reportedHours,
            timestamp: Date.now()
          };
          console.log(`[统计] 已报工时获取成功: ${reportedHours}, 已缓存到键: ${cacheKey}`);
          
          // 记录成功的API调用
          this.logApiCall(true);
          
          return reportedHours;
        } else {
          console.warn(`[统计] API返回失败，任务ID: ${taskOrProjectId}, 响应:`, response);
          this.logApiCall(false);
          return 0;
        }
      } catch (error) {
        console.error(`[统计] 获取已报工时异常，任务ID: ${taskOrProjectId}, 错误:`, error);
        this.logApiCall(false);
        return 0;
      }
    },

    // 批量获取已报工时数据
    async loadReportedHoursForItems(items) {
      if (!items || items.length === 0) {
        return;
      }

      // 过滤掉已经有已报工时数据的项目，避免重复获取
      const itemsToLoad = items.filter(item => {
        const cacheKey = this.getCacheKey(item);
        return this.reportedHoursCache[cacheKey] === undefined;
      });

      if (itemsToLoad.length === 0) {
        console.log('[统计] 所有项目已有缓存数据，跳过API调用');
        return;
      }

      console.log(`[统计] 开始批量获取已报工时数据，需要获取: ${itemsToLoad.length}/${items.length} 个项目`);
      
      this.reportedHoursLoading = true;

      try {
        // 优化并发控制：减少批次大小，增加延迟，避免服务器压力过大
        const batchSize = 5; // 从10减少到5
        const results = [];
        let successCount = 0;
        let failCount = 0;
        
        for (let i = 0; i < itemsToLoad.length; i += batchSize) {
          const batch = itemsToLoad.slice(i, i + batchSize);
          console.log(`[统计] 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(itemsToLoad.length / batchSize)}, 项目数: ${batch.length}`);
          
          // 并行获取当前批次的已报工时，但增加错误处理
          const batchPromises = batch.map(async (item) => {
            try {
              const reportedHours = await this.getTaskRealReportedHours(item);
              successCount++;
              return { item, reportedHours, success: true };
            } catch (error) {
              failCount++;
              console.error(`[统计] 获取项目已报工时失败，项目: ${item.projectName || item.taskId}, 错误:`, error);
              return { item, reportedHours: 0, success: false, error: error.message };
            }
          });

          const batchResults = await Promise.allSettled(batchPromises);
          
          // 处理批次结果
          batchResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              results.push(result.value);
            } else {
              // 处理被拒绝的Promise
              const item = batch[index];
              failCount++;
              console.error(`[统计] 批次请求失败，项目: ${item?.projectName || item?.taskId}, 错误:`, result.reason);
              results.push({ 
                item: item, 
                reportedHours: 0, 
                success: false, 
                error: result.reason?.message || '未知错误' 
              });
            }
          });
          
          // 增加批次间延迟，避免服务器压力过大
          if (i + batchSize < itemsToLoad.length) {
            const delay = itemsToLoad.length > 10 ? 200 : 100; // 根据总数调整延迟
            console.log(`[统计] 批次间延迟 ${delay}ms`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
        
        // 更新项目数据
        results.forEach(({ item, reportedHours, success, error }) => {
          if (item) {
            this.$set(item, 'realReportedHours', reportedHours);
            if (!success) {
              // 记录失败的项目
              console.warn(`[统计] 项目 ${item.projectName || item.taskId} 已报工时获取失败: ${error}`);
            }
          }
        });

        console.log(`[统计] 批量获取已报工时完成，成功: ${successCount}, 失败: ${failCount}, 总计: ${results.length} 个项目`);
        
        // 如果有失败的项目，显示提示
        if (failCount > 0) {
          this.$toast({
            message: `已报工时获取完成，${successCount}个成功，${failCount}个失败`,
            type: 'warning',
            duration: 3000
          });
        }
      } catch (error) {
        console.error('[统计] 批量获取已报工时异常:', error);
        this.$toast.fail('批量获取已报工时失败，请稍后重试');
      } finally {
        this.reportedHoursLoading = false;
      }
    },

    // 清理已报工时缓存
    clearReportedHoursCache() {
      this.reportedHoursCache = {};
      console.log('[统计] 已清理已报工时缓存');
    },

    // 刷新已报工时数据
    async refreshReportedHours() {
      console.log('[统计] 开始刷新已报工时数据');
      this.clearReportedHoursCache();
      await this.loadReportedHoursForItems(this.projectSummaries);
    },

    // 新增：智能缓存清理方法
    cleanExpiredCache() {
      const now = Date.now();
      const maxAge = 2 * 60 * 60 * 1000; // 2小时过期
      let cleanedCount = 0;
      let validCount = 0;
      
      Object.keys(this.reportedHoursCache).forEach(key => {
        const cachedData = this.reportedHoursCache[key];
        
        // 检查新格式的缓存数据（包含时间戳）
        if (cachedData && typeof cachedData === 'object' && cachedData.timestamp) {
          const age = now - cachedData.timestamp;
          if (age > maxAge) {
            delete this.reportedHoursCache[key];
            cleanedCount++;
            console.log(`[统计] 清理过期缓存: ${key}, 年龄: ${(age / 1000).toFixed(2)}秒`);
          } else {
            validCount++;
          }
        } else {
          // 兼容旧格式的缓存数据（直接存储数值），直接清理
          delete this.reportedHoursCache[key];
          cleanedCount++;
          console.log(`[统计] 清理旧格式缓存: ${key}`);
        }
      });
      
      if (cleanedCount > 0) {
        console.log(`[统计] 缓存清理完成，清理了 ${cleanedCount} 个过期缓存项，保留 ${validCount} 个有效缓存项`);
      } else {
        console.log(`[统计] 缓存清理完成，无过期缓存，当前有效缓存项: ${validCount} 个`);
      }
    },

    // 新增：获取缓存统计信息
    getCacheStats() {
      const keys = Object.keys(this.reportedHoursCache);
      const now = Date.now();
      let totalItems = keys.length;
      let validItems = 0;
      let expiredItems = 0;
      let oldFormatItems = 0;
      
      // 分析缓存数据
      keys.forEach(key => {
        const cachedData = this.reportedHoursCache[key];
        if (cachedData && typeof cachedData === 'object' && cachedData.timestamp) {
          const age = now - cachedData.timestamp;
          if (age < 2 * 60 * 60 * 1000) { // 2小时过期
            validItems++;
          } else {
            expiredItems++;
          }
        } else {
          oldFormatItems++;
        }
      });
      
      return {
        totalItems: totalItems,
        validItems: validItems,
        expiredItems: expiredItems,
        oldFormatItems: oldFormatItems,
        cacheKeys: keys.slice(0, 5), // 只显示前5个键
        cacheDetails: keys.map(key => {
          const data = this.reportedHoursCache[key];
          if (data && typeof data === 'object' && data.timestamp) {
            const age = now - data.timestamp;
            return {
              key: key,
              value: data.value,
              age: `${(age / 1000).toFixed(2)}秒`,
              status: age < 2 * 60 * 60 * 1000 ? '有效' : '过期'
            };
          } else {
            return {
              key: key,
              value: data,
              age: '未知',
              status: '旧格式'
            };
          }
        }).slice(0, 3) // 只显示前3个详细信息
      };
    },

    // 新增：初始化调试和监控机制
    initializeDebugAndMonitoring() {
      // 定期清理过期缓存
      this.cacheCleanupInterval = setInterval(() => {
        this.cleanExpiredCache();
      }, 30 * 60 * 1000); // 每30分钟清理一次
      
      // 添加性能监控
      this.performanceMonitor = {
        apiCallCount: 0,
        cacheHitCount: 0,
        errorCount: 0,
        startTime: Date.now()
      };
      
      console.log('[统计] 调试和监控机制已初始化');
      
      // 在开发环境下添加调试信息
      if (process.env.NODE_ENV === 'development') {
        this.logDebugInfo();
      }
    },

    // 新增：记录调试信息
    logDebugInfo() {
      const cacheStats = this.getCacheStats();
      console.log('[统计] 调试信息:', {
        dataScope: this.dataScope,
        timeMode: this.timeMode,
        currentTimeDisplay: this.currentTimeDisplay,
        projectCount: this.projectSummaries.length,
        cacheStats: {
          totalItems: cacheStats.totalItems,
          validItems: cacheStats.validItems,
          expiredItems: cacheStats.expiredItems,
          oldFormatItems: cacheStats.oldFormatItems,
          cacheKeys: cacheStats.cacheKeys,
          cacheDetails: cacheStats.cacheDetails
        },
        performanceMonitor: this.performanceMonitor
      });
    },

    // 新增：记录API调用统计
    logApiCall(success = true) {
      this.performanceMonitor.apiCallCount++;
      if (!success) {
        this.performanceMonitor.errorCount++;
      }
      
      // 每10次API调用记录一次统计信息
      if (this.performanceMonitor.apiCallCount % 10 === 0) {
        console.log('[统计] API调用统计:', {
          totalCalls: this.performanceMonitor.apiCallCount,
          errorRate: (this.performanceMonitor.errorCount / this.performanceMonitor.apiCallCount * 100).toFixed(2) + '%',
          uptime: Math.floor((Date.now() - this.performanceMonitor.startTime) / 1000) + '秒'
        });
      }
    },
  },
  mounted() {
    // 在组件挂载时初始化统计数据
    this.fetchStatisticsData();
    
    // 添加全局点击事件监听器，用于关闭下拉
    document.addEventListener('click', this.handleClickOutside);
    
    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
    
    // 新增：调试和监控机制
    this.initializeDebugAndMonitoring();
  },
  
  beforeDestroy() {
    // 移除全局点击事件监听器，避免内存泄漏
    document.removeEventListener('click', this.handleClickOutside);
    
    // 移除全屏变化事件监听
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
    
    // 清理已报工时缓存
    this.clearReportedHoursCache();
    
    // 清理调试和监控机制
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
    }
    
    // 记录最终统计信息
    if (this.performanceMonitor) {
      console.log('[统计] 组件销毁，最终统计:', {
        totalApiCalls: this.performanceMonitor.apiCallCount,
        cacheHits: this.performanceMonitor.cacheHitCount,
        errors: this.performanceMonitor.errorCount,
        uptime: Math.floor((Date.now() - this.performanceMonitor.startTime) / 1000) + '秒'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
// 定义金蝶企业蓝色变量
$kingdee-blue: #276ff5;
$kingdee-blue-light: rgba(39, 111, 245, 0.1);
$kingdee-blue-bright: #276ff5;

.work-report-container {
  min-height: 100vh;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
  
  .tab-nav {
    display: flex;
    background-color: #fff;
    height: 44px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    
    .tab-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      color: #323233;
      position: relative;
      cursor: pointer;
      
      &.active {
        color: $kingdee-blue;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 2px;
          background-color: $kingdee-blue;
        }
      }
    }
  }
  
  .statistics-container {
    padding: 15px;
    margin-top: 44px;
    flex: 1;
    
    .filter-bar {
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 12px;
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      
      .filter-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        &:first-child {
          justify-content: space-between;
        }
      }
      
      .filter-item {
        display: flex;
        align-items: center;
        
        &.date-filter {
          gap: 8px;
          order: 2; // 显示在右侧
          margin-left: auto; // 右对齐
        }
        
        &.data-scope-filter {
          margin-right: 15px;
          order: 1; // 显示在左侧
          
          .data-scope-selector {
            position: relative;
            display: inline-block;
            
            .data-scope-trigger {
              display: flex;
              align-items: center;
              gap: 6px;
              padding: 0;
              background-color: transparent;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              user-select: none;
              transition: all 0.2s ease;
              min-width: 120px;
              justify-content: space-between;
              
              &:hover {
                color: $kingdee-blue;
              }
              
              .data-scope-label {
                font-size: 14px;
                color: #323233;
                font-weight: 500;
              }
              
              .dropdown-arrow {
                font-size: 10px;
                color: #969799;
                transition: transform 0.2s ease;
                
                &.arrow-up {
                  transform: rotate(180deg);
                }
              }
            }
            
            .data-scope-dropdown {
              position: absolute;
              top: 100%;
              left: 0;
              right: 0;
              background-color: #fff;
              border: 1px solid #ebedf0;
              border-radius: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              z-index: 1000;
              margin-top: 2px;
              overflow: hidden;
              
              .dropdown-option {
                padding: 8px 12px;
                font-size: 14px;
                color: #323233;
                cursor: pointer;
                transition: all 0.2s ease;
                border-bottom: 1px solid #f7f8fa;
                
                &:last-child {
                  border-bottom: none;
                }
                
                &:hover {
                  background-color: $kingdee-blue-light;
                  color: $kingdee-blue;
                }
                
                &.selected {
                  background-color: $kingdee-blue;
                  color: #fff;
                  font-weight: 500;
                  
                  &:hover {
                    background-color: darken($kingdee-blue, 5%);
                  }
                }
              }
            }
          }
        }
        
        &.time-navigation {
          flex: 1;
        }
        
        .filter-label {
          font-size: 14px;
          color: #646566;
          white-space: nowrap;
        }
        
        .time-mode-selector {
          display: flex;
          border: 1px solid #ebedf0;
          border-radius: 4px;
          overflow: hidden;
          
          span {
            padding: 4px 10px;
            font-size: 12px;
            background-color: #f7f8fa;
            border-right: 1px solid #ebedf0;
            cursor: pointer;
            
            &:last-child {
              border-right: none;
            }
            
            &.active {
              background-color: $kingdee-blue;
              color: white;
            }
          }
        }
      }
      
      .filter-item.time-navigation {
        .time-nav-container {
          display: flex;
          align-items: center;
          background-color: #f7f8fa;
          border-radius: 4px;
          padding: 4px 8px;
          width: 100%;
          max-width: 300px;
          border: 1px solid #ebedf0;
          
          .nav-btn {
            font-size: 16px;
            color: $kingdee-blue;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background-color: #fff;
            border: 1px solid #d4d6d8;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            
            .arrow-icon {
              font-size: 20px;
              font-weight: bold;
              color: #333;
              line-height: 1;
              font-family: 'Arial', sans-serif;
            }
            
            &:hover {
              background-color: $kingdee-blue;
              border-color: $kingdee-blue;
              box-shadow: 0 2px 4px rgba(0, 102, 179, 0.3);
              transform: scale(1.05);
              
              .arrow-icon {
                color: #fff;
              }
            }
            
            &:active {
              background-color: #004a8a;
              border-color: #004a8a;
              transform: scale(0.95);
            }
          }
          
          .current-time-display {
            flex: 1;
            margin: 0 12px;
            font-size: 14px;
            color: #323233;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      
      .filter-btn {
        background-color: $kingdee-blue;
        border-color: $kingdee-blue;
        min-width: 60px;
      }
    }
    
    .total-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      
      .info-item {
        flex: 1;
        height: 80px;
        background-color: #fff;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        margin: 0 5px;
        
        &:first-child {
          margin-left: 0;
        }
        
        &:last-child {
          margin-right: 0;
        }
        
        .info-value {
          font-size: 20px;
          font-weight: 700;
          color: $kingdee-blue;
          margin-bottom: 5px;
        }
        
        .info-label {
          font-size: 12px;
          color: #969799;
        }
      }
      .clickable-card {
        cursor: pointer;
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
    
    .project-summary-table {
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 15px;
        position: relative;
        
        .project-count {
          font-size: 14px;
          color: #969799;
          font-weight: normal;
        }
        
        .fullscreen-btn {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          font-size: 20px;
          color: $kingdee-blue;
          cursor: pointer;
          padding: 8px;
          transition: all 0.2s ease;
          
          &:hover {
            color: darken($kingdee-blue, 10%);
            transform: translateY(-50%) scale(1.1);
          }
          
          &:active {
            transform: translateY(-50%) scale(0.95);
          }
        }
      }
      
      .table-container {
        overflow-x: auto;
        
        .summary-table {
          width: 100%;
          border-collapse: collapse;
          background-color: #fff;
          
          th, td {
            padding: 8px 4px;
            font-size: 12px;
          }
          
          th {
            background-color: #f8f9fa;
            font-size: 13px;
            font-weight: 600;
            color: #646566;
            position: sticky;
            top: 0;
            z-index: 1;
          }
          
          .project-name-col {
            width: 20%;
            min-width: 100px;
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 2;
            border-right: 1px solid #ebedf0;
          }
          
          .task-count-col, .report-hours-col, .overtime-hours-col, 
          .confirm-hours-col, .planned-hours-col, .cumulative-hours-col, .total-hours-col, .action-col {
            width: 10%;
            min-width: 70px;
            text-align: center;
          }
          
          .unaudited-hours-col, .cost-hours-col, .progress-col, .delay-status-col {
            width: 8%;
            min-width: 65px;
            text-align: center;
          }
          
          .summary-row {
            &:hover {
              background-color: #f5f7fa;
              
              .project-name {
              background-color: #f5f7fa;
              }
            }
            
            &:nth-child(even) {
              background-color: #fafafa;
              
              .project-name {
              background-color: #fafafa;
              }
            }
          }
          
          .project-name {
            position: sticky;
            left: 0;
            background-color: #fff;
            z-index: 1;
            border-right: 1px solid #ebedf0;
            
            .project-name-text {
              max-width: 80px;
              font-size: 12px;
            }
          }
          
          .task-count {
            text-align: center;
            
            .count-badge {
              display: inline-block;
              padding: 2px 8px;
              background-color: #e8f4ff;
              color: $kingdee-blue;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
              min-width: 20px;
            }
          }
          
          .report-hours, .overtime-hours, .confirm-hours, .planned-hours, .cumulative-hours {
            text-align: center;
            
            .hours-value {
              font-size: 14px;
              color: #323233;
              font-weight: 500;
            }
          }
          
          .unaudited-hours, .cost-hours {
            text-align: center;
            
            .hours-value {
              font-size: 14px;
              color: #323233;
              font-weight: 500;
            }
          }
          
          .progress {
            text-align: center;
            
            .progress-value {
              font-size: 14px;
              color: $kingdee-blue;
              font-weight: 500;
            }
          }
          
          .delay-status {
            text-align: center;
            
            .delay-value {
              font-size: 14px;
              color: #969799;
              font-weight: 500;
            }
          }
          
          .total-hours {
            text-align: center;
            
            .total-value {
              font-size: 14px;
              color: $kingdee-blue;
              font-weight: 600;
            }
          }
          
          .action {
            text-align: center;
            
            .detail-btn {
              background-color: $kingdee-blue;
              border-color: $kingdee-blue;
              padding: 4px 12px;
              font-size: 12px;
              border-radius: 4px;
              
              &:hover {
                background-color: darken($kingdee-blue, 10%);
                border-color: darken($kingdee-blue, 10%);
              }
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: 40px 20px;
          color: #969799;
          
          .empty-text {
            font-size: 14px;
          }
        }
      }
    }
    
    .task-details-popup {
      .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #fff;
        border-bottom: 1px solid #ebedf0;
        position: sticky;
        top: 0;
        z-index: 10;
        
        .popup-title {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
        }
        
        .close-icon {
          font-size: 18px;
          color: #969799;
          cursor: pointer;
          
          &:hover {
            color: #323233;
          }
        }
      }
      
      .popup-content {
        padding: 0;
        background-color: #f8f9fa;
        height: calc(100% - 65px);
        overflow-y: auto;
        
        .project-summary-info {
          background-color: #fff;
          margin-bottom: 8px;
          padding: 15px 20px;
          
          .summary-metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            
            .metric-item {
              text-align: center;
              flex: 1;
              min-width: calc(33.333% - 10px);
              
              .metric-label {
                font-size: 12px;
                color: #969799;
                margin-bottom: 4px;
              }
              
              .metric-value {
                font-size: 16px;
                font-weight: 600;
                color: #323233;
                
                &.total {
                  color: $kingdee-blue;
                }
              }
            }
          }
        }
        
        .task-details-list {
          .task-detail-item {
            background-color: #fff;
            margin-bottom: 8px;
            padding: 15px 20px;
            
            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              
              .task-name {
                font-size: 14px;
                font-weight: 500;
                color: #323233;
                flex: 1;
                margin-right: 10px;
              }
              
              .task-status {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                
                &.draft {
                  background-color: #f7f8fa;
                  color: #969799;
                }
                
                &.submitted {
                  background-color: #fff3e5;
                  color: #f59d56;
                }
                
                &.approved {
                  background-color: #e1f5e1;
                  color: #21ba45;
                }
              }
            }
            
            .task-info {
              margin-bottom: 12px;
              
              .info-row {
                margin-bottom: 6px;
                font-size: 13px;
                
                .info-label {
                  color: #969799;
                  margin-right: 8px;
                }
                
                .info-value {
                  color: #323233;
                }
              }
            }
            
            .task-metrics {
              .metric-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 8px;
                padding: 8px;
                
                .metric {
                  .label {
                    font-size: 11px;
                  }
                  
                  .value {
                    font-size: 13px;
                  }
                }
              }
            }
            
            .task-content {
              margin-top: 8px;
              padding: 8px;
              background-color: #f8f9fa;
              border-radius: 4px;
              width: 100%;
              box-sizing: border-box;
              
              .content-label {
                font-size: 11px;
                font-weight: 500;
                color: #646566;
                margin-bottom: 4px;
              }
              
              .content-value {
                font-size: 12px;
                color: #323233;
                line-height: 1.4;
                word-wrap: break-word;
                word-break: break-word;
                overflow-wrap: break-word;
                white-space: pre-wrap;
                max-width: 100%;
              }
            }
          }
          
          .empty-tasks {
            background-color: #fff;
            padding: 40px 20px;
            text-align: center;
            
            .empty-text {
              font-size: 14px;
              color: #969799;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media screen and (min-width: 768px) {
    .statistics-container {
      max-width: 768px;
      margin: 0 auto;
    }
  }

  @media screen and (max-width: 768px) {
    .statistics-container {
      .filter-bar {
        .filter-row {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
          
          &:first-child {
            justify-content: flex-start;
            flex-direction: row;
            align-items: center;
            
            .filter-item.data-scope-filter {
              margin-right: 10px;
              
              .data-scope-selector {
                .data-scope-trigger {
                  min-width: 100px;
                  padding: 4px 8px;
                  
                  .data-scope-label {
                    font-size: 13px;
                  }
                  
                  .dropdown-arrow {
                    font-size: 9px;
                  }
                }
                
                .data-scope-dropdown {
                  .dropdown-option {
                    padding: 6px 8px;
                    font-size: 13px;
                  }
                }
              }
            }
            
            .filter-item.date-filter {
              .filter-label {
                font-size: 13px;
              }
            }
          }
          
          .filter-item.time-navigation {
            .time-nav-container {
              max-width: 100%;
            }
          }
          
          .filter-btn {
            align-self: flex-end;
          }
        }
      }
      
      .project-summary-table {
        .table-container {
          .summary-table {
            th, td {
              padding: 8px 4px;
              font-size: 12px;
            }
            
            .project-name-col {
              min-width: 100px;
            }
            
            .task-count-col, .report-hours-col, .overtime-hours-col, 
            .confirm-hours-col, .planned-hours-col, .cumulative-hours-col, .total-hours-col, .action-col {
              min-width: 55px;
            }
            
            .unaudited-hours-col, .cost-hours-col, .progress-col, .delay-status-col {
              min-width: 50px;
            }
            
            .project-name {
              .project-name-text {
                max-width: 80px;
                font-size: 12px;
              }
            }
            
            .count-badge {
              font-size: 11px !important;
              padding: 1px 6px !important;
            }
            
            .hours-value, .total-value {
              font-size: 12px !important;
            }
            
            .detail-btn {
              padding: 2px 8px !important;
              font-size: 11px !important;
            }
          }
        }
      }
      
      // 移动端弹窗样式调整
      .task-details-popup {
        .popup-header {
          padding: 12px 15px;
          
          .popup-title {
            font-size: 14px;
          }
        }
        
        .popup-content {
          .project-summary-info {
            padding: 12px 15px;
            
            .summary-metrics {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              
              .metric-item {
                text-align: center;
                flex: 1;
                min-width: calc(33.333% - 10px);
                
                .metric-label {
                  font-size: 12px;
                  color: #969799;
                  margin-bottom: 4px;
                }
                
                .metric-value {
                  font-size: 16px;
                  font-weight: 600;
                  color: #323233;
                  
                  &.total {
                    color: $kingdee-blue;
                  }
                }
              }
            }
          }
          
          .task-details-list {
            .task-detail-item {
              padding: 12px 15px;
              
              .task-header {
                margin-bottom: 8px;
                
                .task-name {
                  font-size: 13px;
                }
                
                .task-status {
                  font-size: 11px;
                  padding: 3px 6px;
                }
              }
              
              .task-info {
                margin-bottom: 8px;
                
                .info-row {
                  font-size: 12px;
                  margin-bottom: 4px;
                }
              }
              
              .task-metrics {
                .metric-grid {
                  grid-template-columns: repeat(3, 1fr);
                  gap: 8px;
                  padding: 8px;
                  
                  .metric {
                    .label {
                      font-size: 11px;
                    }
                    
                    .value {
                      font-size: 13px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 移动端横屏样式优化
  .project-summary-table {
    .fullscreen-wrapper {
      position: relative;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      
      // 基础样式
      .table-container {
        overflow-x: auto;
      }
      
      // 全屏样式
      &:fullscreen, &:-webkit-full-screen, &:-moz-full-screen, &:-ms-fullscreen {
        padding: 20px;
        width: 100vw;
        height: 100vh;
        box-sizing: border-box;
        background-color: #f5f6fa;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .exit-fullscreen-btn {
          position: fixed;
          top: 16px;
          right: 16px;
          display: flex;
          align-items: center;
          gap: 4px;
          background-color: #fff;
          color: $kingdee-blue;
          padding: 8px 16px;
          border-radius: 20px;
          cursor: pointer;
          z-index: 9999;
          font-size: 13px;
          border: 1px solid rgba(39, 111, 245, 0.2);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;
          
          .van-icon {
            font-size: 16px;
            margin-right: 4px;
          }
          
          &:hover {
            background-color: $kingdee-blue;
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(39, 111, 245, 0.2);
          }
        }
        
        .table-container {
          margin: 20px auto 0;
          padding: 24px;
          background: #fff;
          border-radius: 12px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
          width: fit-content;
          max-width: 95%;
          overflow: visible;
          
          .summary-table {
            border-collapse: separate;
            border-spacing: 0;
            margin: 0 auto;
            
            // 表头样式
            thead {
              background-color: #f8f9fa;
              
              th {
                padding: 12px 16px;
                font-size: 13px;
                font-weight: 600;
                color: #1a1a1a;
                text-align: center;
                border-bottom: 2px solid #ebedf0;
                white-space: nowrap;
                
                // 项目名称列
                &.project-name-col {
                  min-width: 160px;
                  text-align: left;
                  padding-left: 20px;
                }
                
                // 任务数列
                &.task-count-col {
                  min-width: 80px;
                }
                
                // 工时相关列
                &.report-hours-col,
                &.overtime-hours-col,
                &.confirm-hours-col,
                &.planned-hours-col,
                &.cumulative-hours-col,
                &.total-hours-col {
                  min-width: 90px;
                }
                
                // 新增的工时相关列
                &.unaudited-hours-col,
                &.cost-hours-col,
                &.progress-col,
                &.delay-status-col {
                  min-width: 80px;
                }
                
                // 操作列
                &.action-col {
                  min-width: 70px;
                }
              }
            }
            
            // 表格内容
            tbody {
              tr {
                transition: background-color 0.2s ease;
                
                &:nth-child(even) {
                  background-color: #fafbfc;
                }
                
                &:hover {
                  background-color: #f0f7ff;
                }
                
                td {
                  padding: 12px 16px;
                  font-size: 13px;
                  color: #333;
                  border-bottom: 1px solid #ebedf0;
                  text-align: center;
                  
                  // 项目名称单元格
                  &.project-name {
                    text-align: left;
                    padding-left: 20px;
                    font-weight: 500;
                    
                    .project-name-text {
                      max-width: 160px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      display: block;
                      color: #1a1a1a;
                    }
                  }
                  
                  // 任务数徽章
                  .count-badge {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 28px;
                    height: 20px;
                    padding: 0 8px;
                    font-size: 12px;
                    font-weight: 500;
                    color: $kingdee-blue;
                    background-color: rgba(39, 111, 245, 0.1);
                    border-radius: 10px;
                  }
                  
                  // 工时数值
                  .hours-value {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    font-weight: 500;
                  }
                  
                  // 总计工时
                  .total-value {
                    color: $kingdee-blue;
                    font-weight: 600;
                  }
                  
                  // 详情按钮
                  .detail-btn {
                    min-width: 60px;
                    height: 24px;
                    line-height: 24px;
                    padding: 0 12px;
                    font-size: 12px;
                    font-weight: 500;
                    background: $kingdee-blue;
                    border-color: $kingdee-blue;
                    border-radius: 12px;
                    transition: all 0.2s ease;
                    
                    &:hover {
                      background: darken($kingdee-blue, 5%);
                      transform: translateY(-1px);
                      box-shadow: 0 2px 6px rgba(39, 111, 245, 0.2);
                    }
                    
                    &:active {
                      transform: translateY(0);
                    }
                  }
                }
              }
              
              // 最后一行去除底部边框
              tr:last-child td {
                border-bottom: none;
              }
            }
          }
        }
      }
    }
  }

  // 横屏模式下的响应式调整
  @media screen and (orientation: landscape) {
    .project-summary-table {
      .fullscreen-wrapper:fullscreen {
        .table-container {
          // 超小屏幕
          @media (max-height: 450px) {
            padding: 16px;
            margin-top: 12px;
            
            .summary-table {
              thead th,
              tbody td {
                padding: 8px 12px;
                font-size: 12px;
                
                &.project-name-col,
                .project-name-text {
                  min-width: 140px;
                  max-width: 140px;
                }
                
                &.task-count-col { min-width: 70px; }
                &.report-hours-col,
                &.overtime-hours-col,
                &.confirm-hours-col,
                &.planned-hours-col,
                &.cumulative-hours-col,
                &.total-hours-col { min-width: 80px; }
                &.unaudited-hours-col,
                &.cost-hours-col,
                &.progress-col,
                &.delay-status-col { min-width: 70px; }
                &.action-col { min-width: 60px; }
              }
              
              .count-badge {
                min-width: 24px;
                height: 18px;
                padding: 0 6px;
                font-size: 11px;
              }
              
              .detail-btn {
                min-width: 50px;
                height: 22px;
                line-height: 22px;
                padding: 0 8px;
              }
            }
          }
          
          // 大屏幕
          @media (min-height: 601px) {
            .summary-table {
              thead th,
              tbody td {
                padding: 14px 18px;
                font-size: 14px;
                
                &.project-name-col,
                .project-name-text {
                  min-width: 180px;
                  max-width: 180px;
                }
              }
            }
          }
        }
      }
    }
  }

  .task-details-popup .task-content {
    border-left: none !important;
  }
}
</style> 