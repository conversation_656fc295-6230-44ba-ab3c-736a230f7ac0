import axios from 'axios';

/**
 * 获取当前用户有权限审批的工单号列表
 * @param {Object} params 查询参数
 * @param {string} params.userEntityId 用户实体ID
 * @returns {Promise} 返回包含工单号列表的Promise
 */
export function getApprovalBillNumbers(params = {}) {
  const { userEntityId } = params;
  
  if (!userEntityId) {
    return Promise.reject(new Error('用户实体ID不能为空'));
  }
  
  // 构建请求数据
  const requestData = {
    data: {
      "FormId": "WF_AssignmentBill",
      "FieldKeys": "FBillNumber",
      "FilterString": `FReceiverId='${userEntityId}' AND FObjectTypeId='PBEW_GSHB'`,
      "OrderString": "",
      "TopRowCount": 0,
      "StartRow": 0,
      "Limit": 2000,
      "SubSystemId": ""
    }
  };
  
  console.log('[API] 获取审批权限工单 - 请求参数:', {
    userEntityId,
    requestData
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 获取审批权限工单 - 原始响应:', response.data);
    
    if (Array.isArray(response.data)) {
      // 提取FBillNumber列表
      const billNumbers = response.data.map(item => {
        // 处理可能的数组或对象格式
        return Array.isArray(item) ? item[0] : item.FBillNumber;
      }).filter(billNo => billNo); // 过滤掉空值
      
      console.log('[API] 获取审批权限工单 - 提取的工单号:', billNumbers);
      
      return {
        success: true,
        message: '获取审批权限工单成功',
        data: billNumbers
      };
    } else {
      console.warn('[API] 获取审批权限工单 - 响应格式不符合预期:', response.data);
      return {
        success: false,
        message: '返回数据格式不符合预期',
        data: []
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 获取审批权限工单失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 获取审批权限工单超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 获取审批权限工单未收到响应，错误: ${error.message}`);
      }
    } else {
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 获取审批权限工单请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: []
    };
  });
}

/**
 * 获取待审批的工时报告数据
 * @param {Object} params 查询参数
 * @param {string} params.username 用户名（从登录信息中获取，用于记录但不作为筛选条件）
 * @param {string} params.userEntityId 用户实体ID（用于获取审批权限）
 * @param {string} params.startDate 开始日期 (可选)
 * @param {string} params.endDate 结束日期 (可选)
 * @param {string} params.status 状态，默认为'B,C'(待审批和已审核)
 * @returns {Promise} 返回包含审批数据的Promise
 */
export function getApprovalData(params = {}) {
  const { username, userEntityId, startDate, endDate, status = 'B,C' } = params;
  
  if (!username) {
    return Promise.reject(new Error('用户账号不能为空'));
  }
  
  if (!userEntityId) {
    return Promise.reject(new Error('用户实体ID不能为空'));
  }
  
  // 首先获取用户有权限审批的工单号列表
  return getApprovalBillNumbers({ userEntityId })
    .then(billNumbersResult => {
      if (!billNumbersResult.success) {
        return {
          success: false,
          message: `获取审批权限失败: ${billNumbersResult.message}`,
          data: []
        };
      }
      
      const approvalBillNumbers = billNumbersResult.data;
      
      // 如果用户没有任何审批权限，直接返回空数据
      if (!approvalBillNumbers || approvalBillNumbers.length === 0) {
        console.log('[API] 获取工时审批数据 - 用户无审批权限');
        return {
          success: true,
          message: '用户无审批权限',
          data: []
        };
      }
      
      console.log(`[API] 获取工时审批数据 - 用户有权限的工单数量: ${approvalBillNumbers.length}`);
      
      // 构建筛选条件 - 支持多状态查询
      let filterString = '';
      
      // 处理状态筛选，支持逗号分隔的多个状态
      if (status.includes(',')) {
        // 多状态查询，如 'B,C'
        const statusList = status.split(',').map(s => s.trim());
        const statusConditions = statusList.map(s => `FDocumentStatus='${s}'`).join(' or ');
        filterString = `(${statusConditions})`;
      } else {
        // 单状态查询
        filterString = `FDocumentStatus='${status}'`;
      }
      
      // 添加工单号权限筛选
      const billNumbersList = approvalBillNumbers.map(billNo => `'${billNo}'`).join(',');
      filterString += ` and FBillno in (${billNumbersList})`;
      
      // 如果设置了日期筛选，添加到筛选条件
      if (startDate && endDate) {
        filterString += ` and FHBDate>='${startDate}' and FHBDate<='${endDate}'`;
      } else if (startDate) {
        filterString += ` and FHBDate>='${startDate}'`;
      } else if (endDate) {
        filterString += ` and FHBDate<='${endDate}'`;
      }
      
      // 构建请求数据
      const requestData = {
        data: {
          "FormId": "PBEW_GSHB",
          "FieldKeys": "FBillno,FProj.FName,FTask.FName,FDocumentStatus,FPlanStartDate,FPlanEndDate,FPlanWorkHours,FManHour,FOverTime,FFinishRate,FReport,FisFinish,FPOST.FName,FHBDate,FReportEmp.FName,FID,FConfirmTime,FOtherTaskName,FOtherTaskType,FTmpTaskTypeid.Fname,FBurnOffTypeid.Fname,FSUMTIME,FSubmitDate,FProj,FTask ",
          "FilterString": filterString,
          "OrderString": "",
          "TopRowCount": 0,
          "StartRow": 0,
          "Limit": 2000,
          "SubSystemId": ""
        } 
      };
      
      console.log('[API] 获取工时审批数据 - 请求参数:', {
        requestData,
        userEntityId,
        approvalBillCount: approvalBillNumbers.length,
        dateFilter: { startDate, endDate },
        filterString,
        hasDateFilter: !!(startDate || endDate),
        note: '修改后：基于用户审批权限查询工时记录'
      });
      
      return axios({
        method: 'post',
        url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
        data: requestData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: 30000 // 30秒超时
      })
      .then(response => {
        console.log('[API] 获取工时审批数据 - 原始响应:', response.data);
        
        if (Array.isArray(response.data)) {
          return {
            success: true,
            message: '获取工时审批数据成功',
            data: response.data
          };
        } else {
          console.warn('[API] 获取工时审批数据 - 响应格式不符合预期:', response.data);
          return {
            success: false,
            message: '返回数据格式不符合预期',
            data: []
          };
        }
      })
      .catch(error => {
        let errorMessage = '';
        
        if (error.response) {
          // 服务器返回了错误响应
          errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取工时审批数据失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
        } else if (error.request) {
          // 请求已发送但没有收到响应
          if (error.code === 'ECONNABORTED') {
            errorMessage = `请求超时(30000ms)`;
            console.error('[API] 获取工时审批数据超时');
          } else {
            errorMessage = `无响应: ${error.message}`;
            console.error(`[API] 获取工时审批数据未收到响应，错误: ${error.message}`);
          }
        } else {
          // 请求设置出错
          errorMessage = `请求错误: ${error.message}`;
          console.error(`[API] 获取工时审批数据请求错误，消息: ${error.message}`);
        }
        
        return {
          success: false,
          message: errorMessage,
          data: []
        };
      });
    })
    .catch(error => {
      console.error('[API] 获取工时审批数据失败 - 获取审批权限时出错:', error);
      return {
        success: false,
        message: `获取审批权限失败: ${error.message}`,
        data: []
      };
    });
}

/**
 * 更新审批状态
 * @param {Object} params 更新参数
 * @param {string} params.id 项目ID (FBillno)
 * @param {string} params.status 新状态 ('C'=通过, 'D'=驳回)
 * @param {string} params.comment 审批意见
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function updateApprovalStatus(params = {}) {
  const { id, status, comment } = params;
  
  if (!id || !status) {
    return Promise.reject(new Error('工时报告ID和状态不能为空'));
  }
  
  // 构建状态更新请求数据
  const requestData = {
    data: {
      "FormId": "PBEW_GSHB",
      "Numbers": [id], // 单据编号数组
      "ChangeStatus": {
        "PKIds": [], // 可为空，使用Numbers
        "Numbers": [id],
        "Status": status,
        "VerifyProcInst": false
      }
    }
  };
  
  console.log('[API] 更新工时报告状态 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SetBillStatus.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SetBillStatus.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 更新工时报告状态 - 原始响应:', response.data);
    
    // 解析响应
    if (response.data && !response.data.Result?.ResponseStatus?.IsError) {
      return {
        success: true,
        message: '状态更新成功',
        data: {
          id,
          status,
          comment,
          response: response.data
        }
      };
    } else {
      const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '未知错误';
      console.error('[API] 更新工时报告状态失败 - 服务器返回错误:', errorMsg);
      return {
        success: false,
        message: `状态更新失败: ${errorMsg}`,
        data: response.data
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 更新工时报告状态失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 更新工时报告状态超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 更新工时报告状态未收到响应，错误: ${error.message}`);
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 更新工时报告状态请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: null
    };
  });
}

/**
 * 确认工时并审批通过
 * @param {Object} params 确认参数
 * @param {string} params.id 工时报告ID
 * @param {number} params.hours 确认的工时
 * @param {string} params.comment 审批意见
 * @param {string} params.fid 工时报告FID - 必须从工时报告对象中获取
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function confirmHours(params = {}) {
  const { id, hours, comment, fid } = params;
  
  if (!id || hours === undefined) {
    return Promise.reject(new Error('工时报告ID和确认工时不能为空'));
  }

  if (!fid) {
    console.error('[API] 确认工时失败 - FID不能为空:', params);
    return Promise.reject(new Error('工时报告FID不能为空'));
  }
  
  // 构建实际API请求数据
  const requestData = {
    "FormId": "PBEW_GSHB",
    "data": {
      "NeedUpDateFields": [],
      "NeedReturnFields": [],
      "IsDeleteEntry": "false",
      "SubSystemId": "",
      "IsVerifyBaseDataField": "false",
      "IsEntryBatchFill": "true",
      "ValidateFlag": "true",
      "NumberSearch": "true",
      "IsAutoAdjustField": "true",
      "InterationFlags": "",
      "IgnoreInterationFlag": "",
      "IsControlPrecision": "false",
      "ValidateRepeatJson": "false",
      "Model": {
        "FID": fid,
        "FConfirmTime": hours.toString()
      }
    }
  };
  
  console.log('[API] 确认工时并审批通过 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 确认工时并审批通过 - 原始响应:', response.data);
    
    // 解析响应
    if (response.data && !response.data.Result?.ResponseStatus?.IsError) {
      return {
        success: true,
        message: '工时确认成功',
        data: {
          id,
          hours,
          comment,
          fid,
          response: response.data
        }
      };
    } else {
      const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '未知错误';
      console.error('[API] 确认工时失败 - 服务器返回错误:', errorMsg);
      return {
        success: false,
        message: `工时确认失败: ${errorMsg}`,
        data: response.data
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 确认工时失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 确认工时请求超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 确认工时未收到响应，错误: ${error.message}`);
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 确认工时请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: null
    };
  });
}

/**
 * 格式化API返回的审批数据为前端使用格式
 * @param {Array} apiData API返回的原始数据
 * @returns {Array} 格式化后的审批数据数组
 */
export function formatApprovalData(apiData) {
  if (!Array.isArray(apiData)) {
    console.warn('[API] 格式化审批数据 - 输入不是数组');
    return [];
  }
  
  return apiData.map((item) => {
    // 处理API返回的数据，转换为组件需要的格式
    // 可能是数组或对象，根据API返回格式调整
    const rowData = Array.isArray(item) ? {
      id: item[0] || '',                 // FBillno
      project: item[1] || '',            // FProj.FName
      taskName: item[2] || '',           // FTask.FName
      documentStatus: item[3] || '',     // FDocumentStatus
      planStartDate: item[4] || '',      // FPlanStartDate
      planEndDate: item[5] || '',        // FPlanEndDate
      planHours: item[6] || 0,           // FPlanWorkHours
      hours: item[7] || 0,               // FManHour (汇报工时)
      overtimeHours: item[8] || 0,       // FOverTime
      progress: item[9] || 0,            // FFinishRate
      content: item[10] || '',           // FReport
      isFinish: item[11] || '',          // FisFinish
      position: item[12] || '',          // FPOST.FName
      workDate: item[13] || '',          // FHBDate
      submitter: item[14] || '',         // FReportEmp.FName
      fid: item[15] || '',               // FID
      confirmTime: item[16] || '',       // FConfirmTime
      otherTaskName: item[17] || '',     // FOtherTaskName
      otherTaskType: item[18] || '',     // FOtherTaskType
      tmpTaskTypeName: item[19] || '',   // FTmpTaskTypeid.Fname
      burnOffTypeName: item[20] || '',   // FBurnOffTypeid.Fname
      sumTime: item[21] || 0,            // FSUMTIME (已报工时)
      submitDate: item[22] || '',        // FSubmitDate
      projectId: item[23] || '',         // FProj (项目ID)
      taskId: item[24] || ''             // FTask (任务ID)
    } : {
      id: item.FBillno || '',
      project: item['FProj.FName'] || '',
      taskName: item['FTask.FName'] || '',
      documentStatus: item.FDocumentStatus || '',
      planStartDate: item.FPlanStartDate || '',
      planEndDate: item.FPlanEndDate || '',
      planHours: item.FPlanWorkHours || 0,
      hours: item.FManHour || 0,         // 汇报工时
      overtimeHours: item.FOverTime || 0,
      progress: item.FFinishRate || 0,
      content: item.FReport || '',
      isFinish: item.FisFinish || '',
      position: item['FPOST.FName'] || '',
      workDate: item.FHBDate || '',
      submitter: item['FReportEmp.FName'] || '',
      fid: item.FID || '',
      confirmTime: item.FConfirmTime || '',
      otherTaskName: item.FOtherTaskName || '',
      otherTaskType: item.FOtherTaskType || '',
      tmpTaskTypeName: item['FTmpTaskTypeid.Fname'] || '',
      burnOffTypeName: item['FBurnOffTypeid.Fname'] || '',
      sumTime: item.FSUMTIME || 0,        // 已报工时
      submitDate: item.FSubmitDate || '',  // FSubmitDate
      projectId: item.FProj || '',        // FProj (项目ID)
      taskId: item.FTask || ''            // FTask (任务ID)
    };
    
    // 打印项目ID和任务ID到控制台（按用户要求）
    console.log(`[API] 审批数据 - 项目ID (FProj): ${rowData.projectId || '无'}`);
    console.log(`[API] 审批数据 - 任务ID (FTask): ${rowData.taskId || '无'}`);

    // 将字段信息打印到控制台，包括新字段
    console.log(`[API] 工时报告 ID: ${rowData.id}, FID: ${rowData.fid}, 汇报工时: ${rowData.hours}, 已报工时: ${rowData.sumTime}, 确认工时: ${rowData.confirmTime}, 其他任务名称: ${rowData.otherTaskName}, 任务类型: ${rowData.otherTaskType}, 临时任务类型: ${rowData.tmpTaskTypeName}, 耗费类型: ${rowData.burnOffTypeName}`);
    
    // 使用已报工时作为累计工时，如果没有则使用汇报工时+加班工时
    const cumulativeHours = rowData.sumTime || (rowData.hours + rowData.overtimeHours);
    
    // 构建工时公式（这里简化处理，实际可能需要从API获取或构建）
    const formula = ` ${rowData.hours}h`;
    
    // 转换状态编码为前端使用的状态
    // 'B' = pending(待审批), 'C' = approved(已通过), 'D' = rejected(已驳回), 'E' = audited(已审核)
    let status = 'pending';
    if (rowData.documentStatus === 'C') {
      status = 'approved';
    } else if (rowData.documentStatus === 'D') {
      status = 'rejected';
    } else if (rowData.documentStatus === 'E') {
      status = 'audited';
    }
    
    // 确定任务类型和显示名称
    let taskType = 'normal';
    let displayTaskName = rowData.taskName;
    let displayProject = rowData.project;
    
    if (rowData.otherTaskType) {
      const otherTypeNum = parseInt(rowData.otherTaskType);
      if (otherTypeNum === 1) {
        // 临时任务
        taskType = 'temp';
        displayTaskName = rowData.otherTaskName || rowData.taskName || '临时任务';
        displayProject = '临时任务';
      } else if (otherTypeNum === 2) {
        // 耗费任务
        taskType = 'timecost';
        displayTaskName = rowData.otherTaskName || rowData.taskName || '耗费';
        displayProject = '耗费';
      }
    }
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      
      // 处理可能的日期格式，根据API返回格式调整
      try {
        // 如果是"YYYY-MM-DD"格式
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
          return dateStr;
        }
        
        // 如果是时间戳或其他格式
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateStr;
      }
    };
    
    const submitDate = formatDate(rowData.submitDate);
    const workDate = formatDate(rowData.workDate);
    
    return {
      id: rowData.id,
      submitter: rowData.submitter,
      submitDate: submitDate,
      workDate: workDate,
      project: displayProject,
      taskName: displayTaskName,
      content: rowData.content,
      hours: rowData.hours,              // 汇报工时
      planHours: rowData.planHours,
      cumulativeHours: cumulativeHours,  // 已报工时（优先使用FSUMTIME）
      sumTime: rowData.sumTime,          // 已报工时（新增字段）
      overtimeHours: rowData.overtimeHours,
      formula: formula,
      progress: rowData.progress,
      status: status,
      expanded: false,
      isSelected: false,
      fid: rowData.fid,
      confirmTime: rowData.confirmTime,
      // 岗位信息
      position: rowData.position,
      positionName: rowData.position,    // 兼容性字段，与position保持一致
      // 新增字段
      taskType: taskType,
      otherTaskName: rowData.otherTaskName,
      otherTaskType: rowData.otherTaskType,
      tmpTaskTypeName: rowData.tmpTaskTypeName,
      burnOffTypeName: rowData.burnOffTypeName,
      // 添加项目ID和任务ID字段（按用户要求存储但不展示）
      projectId: rowData.projectId,
      taskId: rowData.taskId
    };
  });
}

/**
 * 工作流审核通过
 * @param {Object} params 审核参数
 * @param {Array<string>} params.numbers 单据编号数组 (FBillno)
 * @param {string} params.userId 用户ID
 * @param {string} params.userName 用户名称
 * @param {string} params.disposition 审批意见
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function workflowAudit(params = {}) {
  const { numbers, userId, userName, disposition } = params;
  
  if (!numbers || !Array.isArray(numbers) || numbers.length === 0) {
    return Promise.reject(new Error('单据编号不能为空'));
  }
  
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  if (!userName) {
    return Promise.reject(new Error('用户名称不能为空'));
  }
  
  if (!disposition) {
    return Promise.reject(new Error('审批意见不能为空'));
  }
  
  // 构建审核通过请求数据
  const requestData = {
    data: {
      "FormId": "PBEW_GSHB",
      "Numbers": numbers,
      "ApprovalType": 1,
      "UserId": userId,
      "UserName": userName,
      "PostId": 0,
      "Disposition": disposition
    }
  };
  
  console.log('[API] 工作流审核通过 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 工作流审核通过 - 原始响应:', response.data);
    
    // 解析响应
    if (response.data && !response.data.Result?.ResponseStatus?.IsError) {
      return {
        success: true,
        message: '审核通过成功',
        data: {
          numbers,
          userId,
          userName,
          disposition,
          response: response.data
        }
      };
    } else {
      const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '未知错误';
      console.error('[API] 工作流审核通过失败 - 服务器返回错误:', errorMsg);
      return {
        success: false,
        message: `审核通过失败: ${errorMsg}`,
        data: response.data
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 工作流审核通过失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 工作流审核通过超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 工作流审核通过未收到响应，错误: ${error.message}`);
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 工作流审核通过请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: null
    };
  });
}

/**
 * 批量更新审批状态
 * @param {Object} params 批量更新参数
 * @param {Array<string>} params.ids 项目ID数组 (FBillno数组)
 * @param {string} params.status 新状态 ('C'=通过, 'D'=驳回)
 * @param {string} params.comment 审批意见
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function batchUpdateApprovalStatus(params = {}) {
  const { ids, status, comment } = params;
  
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return Promise.reject(new Error('工时报告ID数组不能为空'));
  }
  
  if (!status) {
    return Promise.reject(new Error('状态不能为空'));
  }
  
  // 构建批量状态更新请求数据
  const requestData = {
    data: {
      "FormId": "PBEW_GSHB",
      "Numbers": ids, // 单据编号数组
      "ChangeStatus": {
        "PKIds": [], // 可为空，使用Numbers
        "Numbers": ids,
        "Status": status,
        "VerifyProcInst": false
      }
    }
  };
  
  console.log('[API] 批量更新工时报告状态 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SetBillStatus.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SetBillStatus.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 批量更新工时报告状态 - 原始响应:', response.data);
    
    // 解析响应
    if (response.data && !response.data.Result?.ResponseStatus?.IsError) {
      return {
        success: true,
        message: `批量状态更新成功，共处理${ids.length}个项目`,
        data: {
          ids,
          status,
          comment,
          count: ids.length,
          response: response.data
        }
      };
    } else {
      const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '未知错误';
      console.error('[API] 批量更新工时报告状态失败 - 服务器返回错误:', errorMsg);
      return {
        success: false,
        message: `批量状态更新失败: ${errorMsg}`,
        data: response.data
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 批量更新工时报告状态失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 批量更新工时报告状态超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 批量更新工时报告状态未收到响应，错误: ${error.message}`);
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 批量更新工时报告状态请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: null
    };
  });
}

/**
 * 驳回审批 - 使用WorkflowAudit接口
 * @param {Object} params 驳回参数
 * @param {Array<string>} params.numbers 单据编号数组 (FBillno数组)
 * @param {string} params.userId 用户ID
 * @param {string} params.disposition 驳回建议，可选，默认为"不同意"
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function rejectApproval(params = {}) {
  const { numbers, userId, disposition = "不同意" } = params;
  
  if (!numbers || !Array.isArray(numbers) || numbers.length === 0) {
    return Promise.reject(new Error('单据编号不能为空'));
  }
  
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  // 构建驳回请求数据 - 使用新的WorkflowAudit接口格式
  const requestData = {
    data: {
      "FormId": "PBEW_GSHB",
      "Numbers": numbers,
      "ApprovalType": 2,
      "UserId": userId,
      "Disposition": disposition
    }
  };
  
  console.log('[API] 驳回审批 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 驳回审批 - 原始响应:', response.data);
    
    // 解析响应
    if (response.data && !response.data.Result?.ResponseStatus?.IsError) {
      return {
        success: true,
        message: '驳回成功',
        data: {
          numbers,
          userId,
          disposition,
          response: response.data
        }
      };
    } else {
      const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '未知错误';
      console.error('[API] 驳回审批失败 - 服务器返回错误:', errorMsg);
      return {
        success: false,
        message: `驳回失败: ${errorMsg}`,
        data: response.data
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 驳回审批失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      // 请求已发送但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 驳回审批超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 驳回审批未收到响应，错误: ${error.message}`);
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 驳回审批请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: null
    };
  });
}

/**
 * 获取已审核的工时记录数据
 * @param {Object} params 查询参数
 * @param {string} params.userEntityId 用户实体ID（用于获取审批权限）
 * @param {string} params.startDate 开始日期 (可选)
 * @param {string} params.endDate 结束日期 (可选)
 * @param {Array<string>} params.billNumbers 已审核的工单号列表 (可选，如果提供则直接查询这些工单的详细数据)
 * @returns {Promise} 返回包含已审核数据的Promise
 */
export function getAuditedData(params = {}) {
  const { userEntityId, startDate, endDate, billNumbers } = params;
  
  if (!userEntityId) {
    return Promise.reject(new Error('用户实体ID不能为空'));
  }
  
  console.log('========== 开始获取已审核数据 ==========');
  console.log('[API] getAuditedData - 请求参数:', params);
  
  // 如果没有提供工单号列表，先获取已审核的工单号
  if (!billNumbers || billNumbers.length === 0) {
    console.log('[API] getAuditedData - 步骤1: 获取已审核工单号列表');
    // 注意：不传递日期参数给getAuditedBillNumbers，因为工作流历史表(WF_AssignmentHisBill)
    // 使用的是FCreateDate，而我们需要根据FHBDate（工作日期）进行筛选
    // 所以先获取所有该用户的已审核工单号，再在详细数据查询时进行日期筛选
    return getAuditedBillNumbers({ userEntityId })
      .then(billNumbersResult => {
        console.log('[API] getAuditedData - 工单号查询结果:', billNumbersResult);
        
        if (!billNumbersResult.success || !billNumbersResult.data.length) {
          console.log('[API] getAuditedData - 没有找到已审核的工单号');
          return {
            success: true,
            message: '没有已审核的工时记录',
            data: []
          };
        }
        
        console.log('[API] getAuditedData - 找到的工单号:', billNumbersResult.data);
        console.log('[API] getAuditedData - 工单号数量:', billNumbersResult.data.length);
        
        // 使用获取到的工单号查询详细数据，并在此处应用日期筛选
        console.log('[API] getAuditedData - 步骤2: 查询工单详细数据并应用日期筛选');
        return getAuditedData({ ...params, billNumbers: billNumbersResult.data });
      });
  }
  
  console.log('[API] getAuditedData - 直接查询详细数据，工单号:', billNumbers);
  
  // 构建筛选条件 - 使用工单号列表查询详细数据
  let filterString = `FBillno in ('${billNumbers.join("','")}')`;
  
  // 如果设置了日期筛选，添加到筛选条件（使用FHBDate工作日期）
  if (startDate && endDate) {
    filterString += ` AND FHBDate>='${startDate}' AND FHBDate<='${endDate}'`;
    console.log('[API] getAuditedData - 添加日期筛选:', `${startDate} 至 ${endDate}`);
  } else if (startDate) {
    filterString += ` AND FHBDate>='${startDate}'`;
    console.log('[API] getAuditedData - 添加开始日期筛选:', startDate);
  } else if (endDate) {
    filterString += ` AND FHBDate<='${endDate}'`;
    console.log('[API] getAuditedData - 添加结束日期筛选:', endDate);
  }
  
  console.log('[API] getAuditedData - 最终筛选条件:', filterString);
  
  const requestData = {
    data: {
      FormId: "PBEW_GSHB",
      FieldKeys: "FBillno,FProj.FName,FTask.FName,FDocumentStatus,FPlanStartDate,FPlanEndDate,FPlanWorkHours,FManHour,FOverTime,FFinishRate,FReport,FisFinish,FPOST.FName,FHBDate,FReportEmp.FName,FID,FConfirmTime,FOtherTaskName,FOtherTaskType,FTmpTaskTypeid.Fname,FBurnOffTypeid.Fname,FSUMTIME,FProj,FTask",
      FilterString: filterString,
      OrderString: "",
      TopRowCount: 0,
      StartRow: 0,
      Limit: 2000,
      SubSystemId: ""
    }
  };
  
  console.log('[API] getAuditedData - 请求数据:', requestData);
  
  return axios.post('http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc', requestData, {
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
    .then(response => {
      console.log('[API] getAuditedData - HTTP响应状态:', response.status);
      console.log('[API] getAuditedData - 响应头:', response.headers);
      console.log('[API] getAuditedData - 原始响应数据:', response.data);
      
      // 检查响应结构
      if (response.data && typeof response.data === 'object') {
        console.log('[API] getAuditedData - 响应数据结构分析:');
        console.log('  - 数据类型:', typeof response.data);
        console.log('  - 数据键:', Object.keys(response.data));
        console.log('  - 是否为数组:', Array.isArray(response.data));
        
        if (response.data.Result && response.data.Result.ResponseStatus) {
          console.log('  - ResponseStatus:', response.data.Result.ResponseStatus);
        }
        
        if (response.data.Result && response.data.Result.Result) {
          const resultData = response.data.Result.Result;
          console.log('  - 实际数据类型:', typeof resultData);
          console.log('  - 是否为数组:', Array.isArray(resultData));
          if (Array.isArray(resultData)) {
            console.log('  - 数据条数:', resultData.length);
            if (resultData.length > 0) {
              console.log('  - 第一条数据示例:', resultData[0]);
            }
          }
        }
      }
      
      // 处理不同的响应格式
      let auditedData = [];
      let isSuccess = false;
      
      // 情况1：直接返回数组（如控制台显示的格式）
      if (Array.isArray(response.data)) {
        console.log('[API] getAuditedData - 检测到直接数组响应格式');
        auditedData = response.data;
        isSuccess = true;
      }
      // 情况2：标准的嵌套对象格式
      else if (response.data && response.data.Result && response.data.Result.ResponseStatus && response.data.Result.ResponseStatus.IsSuccess) {
        console.log('[API] getAuditedData - 检测到标准嵌套对象响应格式');
        auditedData = response.data.Result.Result || [];
        isSuccess = true;
      }
      // 情况3：其他可能的格式
      else if (response.data && response.data.Result && Array.isArray(response.data.Result)) {
        console.log('[API] getAuditedData - 检测到Result直接为数组的响应格式');
        auditedData = response.data.Result;
        isSuccess = true;
      }
      // 情况4：错误响应
      else {
        console.log('[API] getAuditedData - 检测到错误响应或未知格式');
        isSuccess = false;
      }
      
      if (isSuccess) {
        console.log('[API] getAuditedData - 解析成功');
        console.log('[API] getAuditedData - 已审核数据条数:', auditedData.length);
        console.log('[API] getAuditedData - 已审核数据详情:', auditedData);
        console.log('=========================================');
        
        return {
          success: true,
          message: `成功获取${auditedData.length}条已审核记录`,
          data: auditedData,
          response: response.data
        };
      } else {
        const errorMsg = response.data?.Result?.ResponseStatus?.Errors?.[0]?.Message || '获取已审核数据失败：响应格式不符合预期';
        console.error('[API] getAuditedData - API返回错误:', errorMsg);
        console.error('[API] getAuditedData - 完整响应信息:', response.data);
        console.log('=========================================');
        
        return {
          success: false,
          message: errorMsg,
          data: [],
          response: response.data
        };
      }
    })
    .catch(error => {
      console.error('[API] getAuditedData - 请求失败:', error);
      console.error('[API] getAuditedData - 错误详情:', {
        message: error.message,
        code: error.code,
        config: error.config,
        response: error.response
      });
      console.log('=========================================');
      
      return {
        success: false,
        message: `网络请求失败: ${error.message}`,
        data: [],
        error: error
      };
    });
}

/**
 * 获取已审核的工单号列表
 * @param {Object} params 查询参数
 * @param {string} params.userEntityId 用户实体ID（用于获取审批权限）
 * @param {string} params.startDate 开始日期 (可选)
 * @param {string} params.endDate 结束日期 (可选)
 * @returns {Promise} 返回包含已审核工单号列表的Promise
 */
export function getAuditedBillNumbers(params = {}) {
  const { userEntityId, startDate, endDate } = params;
  
  if (!userEntityId) {
    return Promise.reject(new Error('用户实体ID不能为空'));
  }
  
  // 构建筛选条件
  let filterString = `FReceiverId='${userEntityId}' AND FObjectTypeId='PBEW_GSHB'`;
  
  // 如果设置了日期筛选，添加到筛选条件
  if (startDate && endDate) {
    filterString += ` AND FCreateDate>='${startDate}' AND FCreateDate<='${endDate}'`;
  } else if (startDate) {
    filterString += ` AND FCreateDate>='${startDate}'`;
  } else if (endDate) {
    filterString += ` AND FCreateDate<='${endDate}'`;
  }
  
  // 构建请求数据，使用提供的接口规范
  const requestData = {
    data: {
      "FormId": "WF_AssignmentHisBill",
      "FieldKeys": "FBILLNO",
      "FilterString": filterString,
      "OrderString": "",
      "TopRowCount": 0,
      "StartRow": 0,
      "Limit": 2000,
      "SubSystemId": ""
    }
  };
  
  console.log('[API] 获取已审核工单号 - 请求参数:', {
    requestData,
    userEntityId,
    dateFilter: { startDate, endDate },
    filterString,
    hasDateFilter: !!(startDate || endDate)
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 获取已审核工单号 - 原始响应:', response.data);
    
    // 处理不同的响应格式
    let billNumbers = [];
    let isSuccess = false;
    
    // 情况1：直接返回数组格式
    if (Array.isArray(response.data)) {
      console.log('[API] 获取已审核工单号 - 检测到直接数组响应格式');
      // 提取工单号列表
      billNumbers = response.data.map(item => {
        // 处理可能的数组或对象格式
        return Array.isArray(item) ? item[0] : item.FBILLNO;
      }).filter(billNo => billNo); // 过滤掉空值
      isSuccess = true;
    }
    // 情况2：标准的嵌套对象格式
    else if (response.data && response.data.Result && response.data.Result.ResponseStatus && response.data.Result.ResponseStatus.IsSuccess) {
      console.log('[API] 获取已审核工单号 - 检测到标准嵌套对象响应格式');
      const resultData = response.data.Result.Result || [];
      if (Array.isArray(resultData)) {
        billNumbers = resultData.map(item => {
          return Array.isArray(item) ? item[0] : item.FBILLNO;
        }).filter(billNo => billNo);
        isSuccess = true;
      }
    }
    // 情况3：其他可能的格式
    else if (response.data && response.data.Result && Array.isArray(response.data.Result)) {
      console.log('[API] 获取已审核工单号 - 检测到Result直接为数组的响应格式');
      billNumbers = response.data.Result.map(item => {
        return Array.isArray(item) ? item[0] : item.FBILLNO;
      }).filter(billNo => billNo);
      isSuccess = true;
    }
    
    if (isSuccess) {
      console.log('[API] 获取已审核工单号 - 提取的工单号:', billNumbers);
      
      return {
        success: true,
        message: '获取已审核工单号成功',
        data: billNumbers
      };
    } else {
      console.warn('[API] 获取已审核工单号 - 响应格式不符合预期:', response.data);
      return {
        success: false,
        message: '返回数据格式不符合预期',
        data: []
      };
    }
  })
  .catch(error => {
    let errorMessage = '';
    
    if (error.response) {
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
      console.error(`[API] 获取已审核工单号失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}`);
    } else if (error.request) {
      if (error.code === 'ECONNABORTED') {
        errorMessage = `请求超时(30000ms)`;
        console.error('[API] 获取已审核工单号超时');
      } else {
        errorMessage = `无响应: ${error.message}`;
        console.error(`[API] 获取已审核工单号未收到响应，错误: ${error.message}`);
      }
    } else {
      errorMessage = `请求错误: ${error.message}`;
      console.error(`[API] 获取已审核工单号请求错误，消息: ${error.message}`);
    }
    
    return {
      success: false,
      message: errorMessage,
      data: []
    };
  });
}

/**
 * 格式化API返回的已审核数据为前端使用格式
 * @param {Array} apiData API返回的原始已审核数据
 * @returns {Array} 格式化后的已审核数据数组
 */
export function formatAuditedData(apiData) {
  console.log('========== 开始格式化已审核数据 ==========');
  console.log('[API] formatAuditedData - 输入数据:', apiData);
  console.log('[API] formatAuditedData - 输入数据类型:', typeof apiData);
  console.log('[API] formatAuditedData - 是否为数组:', Array.isArray(apiData));
  
  if (!Array.isArray(apiData)) {
    console.warn('[API] 格式化已审核数据 - 输入不是数组');
    console.log('=========================================');
    return [];
  }
  
  console.log('[API] formatAuditedData - 原始数据条数:', apiData.length);
  
  // 辅助函数：格式化日期显示
  const formatDateForDisplay = (dateStr) => {
    if (!dateStr) return '';
    
    try {
      // 如果是"YYYY-MM-DD"格式
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        return dateStr;
      }
      
      // 如果是时间戳或其他格式
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return dateStr;
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('日期格式化错误:', error);
      return dateStr;
    }
  };
  
  // 辅助函数：解析数值
  const parseNumber = (value) => {
    if (value === null || value === undefined || value === '') {
      return 0;
    }
    const num = parseFloat(value);
    return isNaN(num) ? 0 : num;
  };
  
  const formattedData = apiData.map((item, index) => {
    console.log(`[API] formatAuditedData - 处理第${index + 1}条数据:`, item);
    
    // 处理API返回的数据，转换为组件需要的格式
    // 可能是数组或对象，根据API返回格式调整
    const rowData = Array.isArray(item) ? {
      id: item[0] || '',                 // FBillno
      project: item[1] || '',            // FProj.FName
      taskName: item[2] || '',           // FTask.FName
      documentStatus: item[3] || '',     // FDocumentStatus
      planStartDate: item[4] || '',      // FPlanStartDate
      planEndDate: item[5] || '',        // FPlanEndDate
      planHours: item[6] || 0,           // FPlanWorkHours
      hours: item[7] || 0,               // FManHour (汇报工时)
      overtimeHours: item[8] || 0,       // FOverTime
      progress: item[9] || 0,            // FFinishRate
      content: item[10] || '',           // FReport
      isFinished: item[11] || false,     // FisFinish
      position: item[12] || '',          // FPOST.FName
      workDate: item[13] || '',          // FHBDate
      submitter: item[14] || '',         // FReportEmp.FName
      fid: item[15] || '',               // FID
      confirmTime: item[16] || '',       // FConfirmTime
      otherTaskName: item[17] || '',     // FOtherTaskName
      otherTaskType: item[18] || '',     // FOtherTaskType
      tmpTaskTypeName: item[19] || '',   // FTmpTaskTypeid.Fname
      burnOffTypeName: item[20] || '',   // FBurnOffTypeid.Fname
      sumTime: item[21] || 0,             // FSUMTIME (已报工时)
      submitDate: item[22] || '',        // FSubmitDate
      projectId: item[23] || '',         // FProj (项目ID)
      taskId: item[24] || ''             // FTask (任务ID)
    } : {
      id: item.FBillno || '',
      project: item['FProj.FName'] || '',
      taskName: item['FTask.FName'] || '',
      documentStatus: item.FDocumentStatus || '',
      planStartDate: item.FPlanStartDate || '',
      planEndDate: item.FPlanEndDate || '',
      planHours: item.FPlanWorkHours || 0,
      hours: item.FManHour || 0,         // 汇报工时
      overtimeHours: item.FOverTime || 0,
      progress: item.FFinishRate || 0,
      content: item.FReport || '',
      isFinished: item.FisFinish || false,
      position: item['FPOST.FName'] || '',
      workDate: item.FHBDate || '',
      submitter: item['FReportEmp.FName'] || '',
      fid: item.FID || '',
      confirmTime: item.FConfirmTime || '',
      otherTaskName: item.FOtherTaskName || '',
      otherTaskType: item.FOtherTaskType || '',
      tmpTaskTypeName: item['FTmpTaskTypeid.Fname'] || '',
      burnOffTypeName: item['FBurnOffTypeid.Fname'] || '',
      sumTime: item.FSUMTIME || 0,        // 已报工时
      submitDate: item.FSubmitDate || '',  // FSubmitDate
      projectId: item.FProj || '',        // FProj (项目ID)
      taskId: item.FTask || ''            // FTask (任务ID)
    };
    
    // 打印项目ID和任务ID到控制台（按用户要求）
    console.log(`[API] 已审核数据 - 项目ID (FProj): ${rowData.projectId || '无'}`);
    console.log(`[API] 已审核数据 - 任务ID (FTask): ${rowData.taskId || '无'}`);

    console.log(`[API] formatAuditedData - 第${index + 1}条原始数据解析:`, {
      '是否为数组': Array.isArray(item),
      '原始数据长度': Array.isArray(item) ? item.length : Object.keys(item).length,
      '原始数据字段': Array.isArray(item) ? item : Object.keys(item),
      '工时字段': {
        汇报工时: rowData.hours,
        已报工时: rowData.sumTime,
        确认工时: rowData.confirmTime,
        加班工时: rowData.overtimeHours
      },
      '任务类型字段': {
        otherTaskName: rowData.otherTaskName,
        otherTaskType: rowData.otherTaskType,
        tmpTaskTypeName: rowData.tmpTaskTypeName,
        burnOffTypeName: rowData.burnOffTypeName
      }
    });
    
    // 确定任务类型和显示名称
    let taskType = 'normal';
    let displayTaskName = rowData.taskName;
    let displayProject = rowData.project;
    
    if (rowData.otherTaskType) {
      const otherTypeNum = parseInt(rowData.otherTaskType);
      if (otherTypeNum === 1) {
        // 临时任务
        taskType = 'temp';
        displayTaskName = rowData.otherTaskName || rowData.taskName || '临时任务';
        displayProject = '临时任务';
      } else if (otherTypeNum === 2) {
        // 耗费任务
        taskType = 'timecost';
        displayTaskName = rowData.otherTaskName || rowData.taskName || '耗费';
        displayProject = '耗费';
      }
    }
    
    // 格式化数据并添加一些额外的字段
    const formattedItem = {
      ...rowData,
      // 统一日期格式
      submitDate: formatDateForDisplay(rowData.submitDate), // 提交日期绑定FSubmitDate
      workDate: formatDateForDisplay(rowData.workDate),
      
      // 使用处理后的项目和任务名称
      project: displayProject,
      taskName: displayTaskName,
      
      // 标记这是已审核数据
      status: 'audited',
      isAudited: true,
      
      // 处理数值字段
      planHours: parseNumber(rowData.planHours),
      hours: parseNumber(rowData.hours),           // 汇报工时
      overtimeHours: parseNumber(rowData.overtimeHours),
      progress: parseNumber(rowData.progress),
      
      // 使用已报工时作为累计工时，如果没有则使用汇报工时+加班工时
      cumulativeHours: parseNumber(rowData.sumTime) || (parseNumber(rowData.hours) + parseNumber(rowData.overtimeHours)),
      sumTime: parseNumber(rowData.sumTime),       // 已报工时（新增字段）
      
      // 构建工时公式
      formula: `${parseNumber(rowData.hours)}h`,
      
      // 初始化展开状态
      expanded: false,
      isSelected: false,
      
      // 新增任务类型相关字段
      taskType: taskType,
      otherTaskName: rowData.otherTaskName,
      otherTaskType: rowData.otherTaskType,
      tmpTaskTypeName: rowData.tmpTaskTypeName,
      burnOffTypeName: rowData.burnOffTypeName,
      // 添加项目ID和任务ID字段（按用户要求存储但不展示）
      projectId: rowData.projectId,
      taskId: rowData.taskId
    };
    
    console.log(`[API] formatAuditedData - 第${index + 1}条格式化后数据:`, formattedItem);
    
    return formattedItem;
  });
  
  console.log('[API] formatAuditedData - 格式化完成');
  console.log('[API] formatAuditedData - 格式化后数据条数:', formattedData.length);
  console.log('[API] formatAuditedData - 格式化后完整数据:', formattedData);
  
  // 数据质量分析
  const qualityReport = {
    总条数: formattedData.length,
    有项目名称: formattedData.filter(item => item.project).length,
    有任务名称: formattedData.filter(item => item.taskName).length,
    有工作内容: formattedData.filter(item => item.content).length,
    有工时记录: formattedData.filter(item => item.hours > 0).length,
    有加班记录: formattedData.filter(item => item.overtimeHours > 0).length,
    有进度记录: formattedData.filter(item => item.progress > 0).length
  };
  
  console.log('[API] formatAuditedData - 数据质量报告:', qualityReport);
  console.log('=========================================');
  
  return formattedData;
} 