import axios from 'axios';
import { saveSessionId, clearSessionId } from '../utils/session';

/**
 * 金蝶ERP系统登录API
 * 使用预设凭证登录金蝶系统
 * @param {string} [username] 可选用户名，如不提供则使用预设值
 * @param {string} [password] 可选密码，如不提供则使用预设值
 * @param {number} [lcid=2052] 语言ID，默认为2052（简体中文）
 * @param {string} [acctID="6737ef9b8ca10d"] 账套ID
 * @returns {Promise} 返回包含登录结果的Promise
 */
export function apiLogin(username = "迟君", password = "88888888", lcid = 2052, acctID = "6858f5746a5d61") {
  return new Promise((resolve, reject) => {
    // 构建登录参数
    const loginJson = {
      "parameters": JSON.stringify([acctID, username, password, lcid])
    };
    
    axios({
      method: 'post',
      // 使用相对路径，通过代理访问API
      url: 'http://140.249.162.74:81/k3cloudxm/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc',
      data: loginJson,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    })
    .then(response => {
      const data = response.data;
      const isSuccess = data.IsSuccessByAPI;
      
      if (isSuccess) {
        // 保存token到本地存储
        localStorage.setItem('UserToken', data.Context.UserToken);
        
        // 保存会话ID使用session工具类
        const sessionId = data.KDSVCSessionId;
        saveSessionId(sessionId);
        
        // 返回会话ID
        resolve(sessionId);
      } else {
        reject(new Error(data.Message || "登录失败"));
      }
    })
    .catch(error => {
      const errorMsg = error.response ? 
        `请求失败(${error.response.status}): ${error.response.data?.Message || error.message}` : 
        `API登录请求失败: ${error.message}`;
      
      reject(new Error(errorMsg));
    });
  });
}

/**
 * 验证用户登录
 * 通过查询接口验证用户账号和密码
 * @param {string} username 用户账号
 * @param {string} password 用户密码 
 * @returns {Promise} 返回包含验证结果的Promise
 */
export function verifyLogin(username, password) {
  return new Promise((resolve, reject) => {
    if (!username || !password) {
      reject(new Error("用户名和密码不能为空"));
      return;
    }
    
    // 构建查询参数
    const queryData = {
      data: {          
        "FormId": "PBEW_GSHBYG",
        "FieldKeys": "FEmpName,Fid,FUserId.FName,FUserId",
        "FilterString": `FEmpAccount='${username}' and FPassword='${password}'`,
        "OrderString": "",
        "TopRowCount": 0,
        "StartRow": 0,
        "Limit": 2000,
        "SubSystemId": ""
      }
    };
    
    axios({
      method: 'post',
      url: 'http://140.249.162.74:81/k3cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
      data: queryData,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    })
    .then(response => {
      const data = response.data;
      console.log('API返回数据:', data);
      
      // 检查是否有返回数据
      if (data && Array.isArray(data) && data.length > 0) {
        // 有返回数据，表示验证成功
        // 解析返回数据，包括 FEmpName 和 Fid
        let userData = {};
        
        if (Array.isArray(data[0])) {
          // 如果返回的是数组，按顺序提取数据
          userData = {
            FEmpName: data[0][0] || username,
            Fid: data[0][1] || '',
            FUserIdName: data[0][2] || '',  // 提取FUserId.FName
            FUserId: data[0][3] || ''       // 提取FUserId
          };
        } else if (typeof data[0] === 'object') {
          // 如果返回的是对象，直接获取属性
          userData = {
            FEmpName: data[0].FEmpName || username,
            Fid: data[0].Fid || '',
            FUserIdName: data[0]['FUserId.FName'] || '',  // 提取FUserId.FName
            FUserId: data[0].FUserId || ''                // 提取FUserId
          };
        } else {
          // 兜底处理，只设置用户名
          userData = {
            FEmpName: typeof data[0] === 'string' ? data[0] : username,
            Fid: '',
            FUserIdName: '',
            FUserId: ''
          };
        }
        
        resolve({
          success: true,
          userData: userData
        });
      } else {
        // 无返回数据，表示验证失败
        resolve({
          success: false,
          message: "用户名或密码错误"
        });
      }
    })
    .catch(error => {
      const errorMsg = error.response ? 
        `查询失败(${error.response.status}): ${error.response.data?.Message || error.message}` : 
        `验证用户请求失败: ${error.message}`;
      
      reject(new Error(errorMsg));
    });
  });
}

/**
 * 修改用户密码
 * @param {string} userId 用户ID (Fid)
 * @param {string} newPassword 新密码
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function changePassword(userId, newPassword) {
  return new Promise((resolve, reject) => {
    if (!userId || !newPassword) {
      reject(new Error("用户ID和新密码不能为空"));
      return;
    }
    
    // 构建请求参数
    const requestData = {
      "FormId": "PBEW_GSHBYG",
      "data": {
        "NeedUpDateFields": ["FPassword"],
        "NeedReturnFields": ["FPassword"],
        "IsDeleteEntry": "false",
        "SubSystemId": "",
        "IsVerifyBaseDataField": "false",
        "IsEntryBatchFill": "true",
        "ValidateFlag": "true",
        "NumberSearch": "true",
        "IsAutoAdjustField": "true",
        "InterationFlags": "",
        "IgnoreInterationFlag": "",
        "IsControlPrecision": "false",
        "ValidateRepeatJson": "false",
        "Model": {
          "FID": userId,
          "FPassword": newPassword
        }
      }
    };
    
    axios({
      method: 'post',
      url: 'http://140.249.162.74:81/k3cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc',
      data: requestData,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      }
    })
    .then(response => {
      const data = response.data;
      console.log('修改密码API返回数据:', data);
      
      // 假设API返回成功状态为IsSuccess字段
      if (data && data.Result && data.Result.ResponseStatus && data.Result.ResponseStatus.IsSuccess) {
        resolve({
          success: true,
          message: "密码修改成功"
        });
      } else {
        // 处理失败情况
        const errorMsg = data && data.Result && data.Result.ResponseStatus && 
                        data.Result.ResponseStatus.Errors && 
                        data.Result.ResponseStatus.Errors.length > 0 ? 
                        data.Result.ResponseStatus.Errors[0].Message : 
                        "密码修改失败";
        
        resolve({
          success: false,
          message: errorMsg
        });
      }
    })
    .catch(error => {
      const errorMsg = error.response ? 
        `请求失败(${error.response.status}): ${error.response.data?.Message || error.message}` : 
        `修改密码请求失败: ${error.message}`;
      
      reject(new Error(errorMsg));
    });
  });
}

/**
 * 登出系统
 */
export function logout() {
  localStorage.removeItem('UserToken');
  clearSessionId(); // 使用会话管理工具清除SessionId
  // 如果有其他需要清除的数据，可以在这里添加
} 