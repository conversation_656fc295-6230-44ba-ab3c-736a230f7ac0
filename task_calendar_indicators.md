# 上下文
文件名：task_calendar_indicators.md
创建于：2024-12-19
创建者：AI Assistant
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
修改汇报页面的日期控件，如果某一天的日期有审核数据的话，那么这个日期的下方为对号，如果没有这个日期的下方为红色小点。

# 项目概述
这是一个基于 Vue.js 的工作汇报系统，包含日历控件用于选择工作日期。当前日历中每一天都会显示一个蓝色小圆点来表示是否有任务，但现在需要根据审核状态来区分显示不同的指示器。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)

## 当前实现分析
1. **日历结构**：
   - 位于 `src/view/WorkReport.vue` 文件
   - 支持周视图、月视图和年视图三种模式
   - 每个日期单元格通过 `day.hasTask` 属性控制是否显示指示器

2. **现有指示器逻辑**：
   - 第42行和第60行：`<div v-if="day.hasTask" class="day-indicator"></div>`
   - `hasTaskForDate()` 方法（第1310行）：目前是模拟逻辑，返回随机值
   - 样式第3132行：`.day-indicator` 显示为蓝色小圆点

3. **任务状态系统**：
   - 状态代码：'a'(暂存)、'b'(已提交)、'c'(已审核)、'd'(驳回)
   - `formatTaskStatus()` 方法处理状态显示文本
   - `getStatusClass()` 方法处理状态样式类

4. **数据结构**：
   - `tasks` 数组存储当前选中日期的任务
   - 每个任务包含 `status` 字段表示审核状态
   - 任务按日期存储，需要检查特定日期的任务状态

## 需求分析
- 需要将单一的蓝色圆点替换为两种状态指示器：
  1. 对号图标：该日期有已审核的任务（状态为 'c'）
  2. 红色小点：该日期有任务但未审核（状态为 'a' 或 'b'）
- 需要修改 `hasTaskForDate()` 方法来检查实际的任务审核状态
- 需要在日历日期生成逻辑中添加审核状态检查

## 技术约束
- 需要保持现有的日历功能不受影响
- 需要考虑性能，避免在日历渲染时进行过多的数据查询
- 需要适配周视图和月视图两种模式

# 提议的解决方案 (由 INNOVATE 模式填充)

## 选定方案：混合方案（推荐）
结合计算属性预处理和模板条件渲染的优势：

### 核心技术方案
1. **状态检查方法**：创建 `getDateTaskStatus(dateStr)` 方法来检查特定日期的任务审核状态
2. **日期数据增强**：在 `calendarDays` 计算属性中为每个日期添加 `taskStatus` 字段
3. **模板条件渲染**：根据 `taskStatus` 字段显示不同的指示器
4. **图标方案**：使用 van-icon 的 'success' 图标（对号）和自定义红色圆点

### 状态逻辑设计
- `approved`: 该日期有已审核任务（状态为 'c'）→ 显示绿色对号
- `pending`: 该日期有任务但未审核（状态为 'a'、'b'、'd'）→ 显示红色小点
- `none`: 该日期无任务 → 不显示指示器

### 技术优势
- **性能优化**：计算属性有缓存机制，避免重复计算
- **扩展性**：易于添加更多状态类型
- **维护性**：逻辑集中，便于调试和修改
- **兼容性**：保持现有日历功能完全不变

# 实施计划 (由 PLAN 模式生成)

## 详细修改规范

### 1. 新增状态检查方法
- 文件：src/view/WorkReport.vue
- 位置：methods 部分，建议在 hasTaskForDate 方法附近
- 方法签名：`getDateTaskStatus(dateStr) -> String`
- 返回值：'approved' | 'pending' | 'none'
- 逻辑：需要访问存储在本地或远程的任务数据

### 2. 修改日历数据生成方法
- 文件：src/view/WorkReport.vue
- 修改方法：`getWeekDays()` 和 `getMonthDays()`
- 添加字段：为每个 day 对象添加 `taskStatus` 字段
- 调用：在生成日期时调用 `getDateTaskStatus()` 方法

### 3. 更新模板指示器渲染
- 文件：src/view/WorkReport.vue
- 位置：第42行（周视图）和第60行（月视图）
- 替换：现有的 `day-indicator` div
- 新增：条件渲染的对号图标和红色小点

### 4. 添加样式定义
- 文件：src/view/WorkReport.vue
- 位置：<style> 部分，在现有 .day-indicator 样式附近
- 新增样式：`.day-approved-indicator` 和 `.day-pending-indicator`

### 5. 重构 hasTaskForDate 方法
- 文件：src/view/WorkReport.vue
- 位置：第1310行
- 修改：从模拟逻辑改为实际的任务数据检查
- 依赖：新的 getDateTaskStatus 方法

## 实施检查清单：
1. 新增 getDateTaskStatus 方法，实现日期任务状态检查逻辑
2. 修改 getWeekDays 方法，为每个日期添加 taskStatus 字段
3. 修改 getMonthDays 方法，为每个日期添加 taskStatus 字段
4. 更新周视图模板，替换指示器为条件渲染的状态指示器
5. 更新月视图模板，替换指示器为条件渲染的状态指示器
6. 添加对号图标样式 (.day-approved-indicator)
7. 添加红色小点样式 (.day-pending-indicator)
8. 重构 hasTaskForDate 方法，使用真实的任务数据检查
9. 测试周视图和月视图的指示器显示
10. 测试不同任务状态下的指示器变化

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "4. [修改日历组件方法getDateTaskStatus以使用新的API数据]"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*   [2024-12-28 21:28:10]
    *   步骤：1. [添加 getDateTaskStatus 方法]
    *   修改：[src/view/WorkReport.vue - 添加 getDateTaskStatus 方法]
    *   更改摘要：[成功添加状态检查方法]
    *   原因：[执行计划步骤 1]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:35:42]
    *   步骤：2. [修改 getWeekDays 方法，为每个日期添加 taskStatus 字段]
    *   修改：[src/view/WorkReport.vue - 在 getWeekDays 方法中为每个日期添加 taskStatus 字段]
    *   更改摘要：[成功为周视图日期对象添加任务状态字段]
    *   原因：[执行计划步骤 2]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:38:45]
    *   步骤：3. [修改 getMonthDays 方法添加 taskStatus 字段]
    *   修改：[src/view/WorkReport.vue - 在 getMonthDays 方法中为每个日期添加 taskStatus 字段]
    *   更改摘要：[成功为月视图日期对象添加任务状态字段]
    *   原因：[执行计划步骤 3]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:42:30]
    *   步骤：4-5. [更新模板指示器逻辑]
    *   修改：[src/view/WorkReport.vue - 在周视图和月视图模板中替换指示器逻辑，基于 taskStatus 条件渲染]
    *   更改摘要：[成功更新模板以根据任务状态显示不同指示器]
    *   原因：[执行计划步骤 4-5]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:45:15]
    *   步骤：6-7. [添加样式定义]
    *   修改：[src/view/WorkReport.vue - 添加 .task-approved-icon 和 .task-pending-indicator 样式类]
    *   更改摘要：[成功添加新的任务状态指示器样式]
    *   原因：[执行计划步骤 6-7]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:47:30]
    *   步骤：8. [修正模板类名和条件逻辑]
    *   修改：[src/view/WorkReport.vue - 修正指示器类名从 day-approved-indicator 为 task-approved-icon，修正条件从 pending 为 none，添加调试日志]
    *   更改摘要：[修正了类名不匹配和条件逻辑错误的问题，添加调试输出帮助诊断]
    *   原因：[解决用户反馈的对勾不显示问题]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:50:15]
    *   步骤：9. [增强调试输出]
    *   修改：[src/view/WorkReport.vue - 在selectDate和getDateTaskStatus方法中添加详细的调试输出]
    *   更改摘要：[添加了localStorage数据检查、任务状态详情和日期格式的调试日志]
    *   原因：[诊断为什么红点显示但对勾不显示的问题]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2024-12-28 21:52:45]
    *   步骤：10. [修正状态判断逻辑]
    *   修改：[src/view/WorkReport.vue - 改进状态匹配逻辑，支持trim去空格和多种状态格式]
    *   更改摘要：[修正状态判断以支持大写C和带空格的状态值，增加更宽容的匹配逻辑]
    *   原因：[用户反馈状态值是大写C，需要更宽容的状态匹配]
    *   阻碍：[无]
    *   用户确认状态：[待确认]
*   [2025-01-09 20:30]
    *   步骤：[检查清单项目1] 在 WorkReport.vue 中添加 getDateTaskStatus 方法
    *   修改：[src/view/WorkReport.vue] 添加了新的方法用于检查日期任务审核状态
    *   更改摘要：[添加了 getDateTaskStatus 方法来判断指定日期的任务状态]
    *   原因：[执行计划步骤 1]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2025-01-09 20:45]
    *   步骤：[检查清单项目2] 修改 getWeekDays 方法添加 taskStatus 字段
    *   修改：[src/view/WorkReport.vue] 在 getWeekDays 方法中为每个日期对象添加了 taskStatus 字段
    *   更改摘要：[周视图日期现在包含任务状态信息]
    *   原因：[执行计划步骤 2]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2025-01-09 21:00]
    *   步骤：[检查清单项目3] 修改 getMonthDays 方法添加 taskStatus 字段
    *   修改：[src/view/WorkReport.vue] 在 getMonthDays 方法中为每个日期对象添加了 taskStatus 字段
    *   更改摘要：[月视图日期现在包含任务状态信息]
    *   原因：[执行计划步骤 3]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2025-01-09 21:15]
    *   步骤：[检查清单项目4] 更新模板以根据 taskStatus 条件渲染指示器
    *   修改：[src/view/WorkReport.vue] 修改了周视图和月视图的模板，使用条件渲染根据 taskStatus 显示不同指示器
    *   更改摘要：[现在根据任务状态显示绿色对勾（已审核）或红点（无任务）]
    *   原因：[执行计划步骤 4]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2025-01-09 21:30]
    *   步骤：[检查清单项目9] 增强调试输出以诊断数据获取问题
    *   修改：[src/view/WorkReport.vue] 在 getDateTaskStatus 方法中添加了详细的调试日志
    *   更改摘要：[添加了localStorage数据检查、任务状态详情和日期格式调试]
    *   原因：[诊断红点显示而绿色对勾不显示的问题]
    *   用户确认状态：[成功]
*   [2025-01-09 21:45]
    *   步骤：[检查清单项目10] 改进状态判断逻辑以支持多种格式
    *   修改：[src/view/WorkReport.vue] 优化了状态匹配逻辑，支持去除空格和多种状态格式
    *   更改摘要：[改进了状态判断逻辑，支持 'c'、'已审核'、'approved' 等格式，并处理空格]
    *   原因：[用户反馈状态值为大写 'C'，需要更宽容的匹配方式]
    *   用户确认状态：[成功]
*   [2025-01-09 22:00]
    *   步骤：[检查清单项目11] 简化getDateTaskStatus方法为模拟数据
    *   修改：[src/view/WorkReport.vue] 移除了复杂的数据获取逻辑，改为使用模拟数据来测试指示器显示
    *   更改摘要：[使用预定义的模拟数据来测试不同日期的状态显示效果]
    *   原因：[用户要求移除复杂的数据获取，先使用模拟数据测试]
    *   阻碍：[无]
    *   用户确认状态：[待确认]
*   [2025-01-09 22:30]
    *   步骤：1. [在 src/api/report.js 中添加 getCalendarWorkHourStatus 方法]
    *   修改：[src/api/report.js - 添加新的API方法用于获取日历工时状态数据]
    *   更改摘要：[成功添加了getCalendarWorkHourStatus方法，支持按日期聚合工时数据并计算状态]
    *   原因：[执行计划步骤 1]
    *   阻碍：[无]
    *   状态：[待确认]
*   [2025-01-09 22:35]
    *   步骤：2. [在 src/view/WorkReport.vue 中添加 calculateDateWorkHourStatus 方法]
    *   修改：[src/view/WorkReport.vue - 添加calculateDateWorkHourStatus和refreshCalendarStatus方法]
    *   更改摘要：[成功添加了工时状态计算方法和日历状态刷新方法，支持根据8小时标准判断显示对勾或红点]
    *   原因：[执行计划步骤 2]
    *   阻碍：[无]
    *   状态：[待确认]
*   [2025-01-09 22:40]
    *   步骤：3. [修复ESLint错误 - 移除未使用的'reject'参数]
    *   修改：[src/api/report.js - 修复getCalendarWorkHourStatus方法中的ESLint错误]
    *   更改摘要：[移除了Promise构造函数中未使用的reject参数，确保代码符合ESLint规范]
    *   原因：[执行计划步骤 3]
    *   阻碍：[无]
    *   用户确认状态：[成功]
*   [2025-01-09 22:50]
    *   步骤：4. [修改日历组件方法getDateTaskStatus以使用新的API数据]
    *   修改：[src/view/WorkReport.vue - 修改getDateTaskStatus方法使用dateStatusMap，添加mounted生命周期调用refreshCalendarStatus]
    *   更改摘要：[将模拟数据逻辑替换为使用API获取的真实工时数据，在组件初始化时加载日历状态]
    *   原因：[执行计划步骤 4]
    *   阻碍：[无]
    *   状态：[待确认]
*   [2025-01-09 23:00]
    *   步骤：5. [添加今天以后数据不查询功能]
    *   修改：[src/api/report.js - 在getCalendarWorkHourStatus方法的FilterString中添加日期过滤条件；src/view/WorkReport.vue - 在calculateDateWorkHourStatus方法中添加前端日期验证]
    *   更改摘要：[实现了今天以后的数据不进行查询的功能，在API层和前端层都添加了日期过滤逻辑]
    *   原因：[用户需求：添加今天以后数据不查询功能]
    *   阻碍：[无]
    *   状态：[待确认]
*   [2025-01-09 23:10]
    *   步骤：6. [添加今天以后红点不显示功能]
    *   修改：[src/view/WorkReport.vue - 修改getDateTaskStatus方法，今天以后的日期返回null，表示不显示任何指示器]
    *   更改摘要：[实现了今天以后的日期既不查询数据也不显示红点的功能，完善了日历控件的未来日期处理逻辑]
    *   原因：[用户需求：今天以后的红点也不用显示]
    *   阻碍：[无]
    *   状态：[待确认] 