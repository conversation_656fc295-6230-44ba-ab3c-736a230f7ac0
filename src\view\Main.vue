<template>
  <div class="main-container">
    <!-- 主要内容区域 -->
    <div class="app-content">
      <!-- 企业自建卡片 -->
      <div class="work-card">
        <div class="card-title">企业自建</div>
        <div class="card-content">
          <div class="app-item" @click="goToFeature('/report')">
            <div class="app-icon">  
              <van-icon name="orders-o" color="#1989fa" size="26" />
            </div>
            <div class="app-name">工时汇报</div>
          </div>
          
          <div class="app-item" @click="showChangePassword">
            <div class="app-icon">  
              <van-icon name="lock" color="#1989fa" size="26" />
            </div>
            <div class="app-name">修改密码</div>
          </div>
          
          <div class="app-item" @click="logout">
            <div class="app-icon">  
              <van-icon name="sign" color="#1989fa" size="26" />
            </div>
            <div class="app-name">注销</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 修改密码弹窗 -->
    <van-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      show-cancel-button
      @confirm="changePassword"
    >
      <van-field
        v-model="passwordForm.oldPassword"
        type="password"
        label="原密码"
        placeholder="请输入原密码"
        :rules="[{ required: true, message: '请输入原密码' }]"
      />
      <van-field
        v-model="passwordForm.newPassword"
        type="password"
        label="新密码"
        placeholder="请输入新密码"
        :rules="[{ required: true, message: '请输入新密码' }]"
      />
      <van-field
        v-model="passwordForm.confirmPassword"
        type="password"
        label="确认密码"
        placeholder="请再次输入新密码"
        :rules="[{ required: true, message: '请再次输入新密码' }]"
      />
    </van-dialog>
  </div>
</template>

<script>
import { changePassword, logout, verifyLogin } from '../api/auth';

export default {
  name: 'MainInfo',
  data() {
    return {
      // 修改密码相关
      showPasswordDialog: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    };
  },
  methods: {
    goToFeature(path) {
      this.$router.push(path).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    },
    
    // 显示修改密码对话框
    showChangePassword() {
      // 重置表单
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.showPasswordDialog = true;
    },
    
    // 处理修改密码
    changePassword() {
      // 验证表单输入的完整性
      if (!this.passwordForm.oldPassword) {
        this.$toast.fail('请输入原密码');
        return;
      }
      
      if (!this.passwordForm.newPassword) {
        this.$toast.fail('请输入新密码');
        return;
      }
      
      if (!this.passwordForm.confirmPassword) {
        this.$toast.fail('请确认新密码');
        return;
      }
      
      // 验证两次输入的密码是否一致
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$toast.fail('两次输入的密码不一致');
        return;
      }
      
      // 获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const userId = userInfo.userEntityId || userInfo.userId; // 优先使用userEntityId，后备使用userId
      const username = userInfo.username;
      
      if (!userId || !username) {
        this.$toast.fail('用户信息不完整，请重新登录');
        setTimeout(() => {
          this.logout();
        }, 1500);
        return;
      }
      
      // 显示验证原密码的加载状态
      this.$toast.loading({
        message: '正在验证原密码...',
        forbidClick: true
      });
      
      // 第一步：验证原密码
      verifyLogin(username, this.passwordForm.oldPassword)
        .then(result => {
          if (result.success) {
            // 原密码验证成功，继续修改密码
            this.$toast.loading({
              message: '正在修改密码...',
              forbidClick: true
            });
            
            // 第二步：修改密码
            return changePassword(userId, this.passwordForm.newPassword);
          } else {
            // 原密码验证失败
            this.$toast.clear();
            this.$toast.fail('原密码错误，请重新输入');
            throw new Error('原密码验证失败');
          }
        })
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            this.$toast.success(result.message || '密码修改成功');
            this.showPasswordDialog = false;
            
            // 可选：密码修改成功后，提示用户重新登录以确保安全
            setTimeout(() => {
              this.$dialog.confirm({
                title: '密码修改成功',
                message: '为了您的账户安全，建议重新登录。是否现在重新登录？'
              }).then(() => {
                this.logout();
              }).catch(() => {
                // 用户选择不重新登录
              });
            }, 1500);
          } else {
            this.$toast.fail(result.message || '密码修改失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          // 如果是原密码验证失败，错误信息已经在第一个then中处理了
          if (error.message !== '原密码验证失败') {
            this.$toast.fail('修改密码请求失败: ' + error.message);
          }
        });
    },
    
    // 注销登录
    logout() {
      this.$dialog.confirm({
        title: '提示',
        message: '确定要注销登录吗？'
      }).then(() => {
        // 调用登出函数
        logout();
        
        // 跳转到登录页
        this.$router.replace('/login');
        
        this.$toast.success('已安全退出');
      }).catch(() => {
        // 取消操作
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.main-container {
  min-height: 100vh;
  background-color: #f5f6fa;
  padding: 0;
  position: relative;
  
  .app-content {
    padding: 10px;
  }
  
  .work-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .card-title {
      font-size: 15px;
      font-weight: bold;
      padding: 12px 15px;
      color: #333;
      border-bottom: 1px solid #eee;
    }
    
    .card-content {
      padding: 15px;
      
      .app-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .app-icon {
          width: 45px;
          height: 45px;
          background-color: #e6f7ff;
          border-radius: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 10px;
          position: relative;
          
          // 模拟层叠图标效果
          &::before {
            content: "";
            position: absolute;
            width: 35px;
            height: 35px;
            background-color: #d9efff;
            border-radius: 6px;
            z-index: 0;
          }
          
          .van-icon {
            position: relative;
            z-index: 1;
          }
        }
        
        .app-name {
          font-size: 14px;
          color: #333;
          font-weight: 400;
        }
      }
    }
  }
}

// 响应式设计 - 桌面端适配
@media screen and (min-width: 768px) {
  .main-container {
    .app-content {
      max-width: 576px;
      margin: 0 auto;
      padding: 20px;
    }
  }
}
</style>