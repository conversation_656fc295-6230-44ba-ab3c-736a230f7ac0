@use "sass:color";

/**
 * 全局深色主题变量定义
 * 基于金蝶主题色 #276ff5 设计
 * 参考现有 login.vue 的深色模式实现
 */

// 金蝶品牌色系
$kingdee-primary: #276ff5;
$kingdee-primary-light: color.adjust($kingdee-primary, $lightness: 10%);
$kingdee-primary-dark: color.adjust($kingdee-primary, $lightness: -10%);

// 深色模式基础色彩
$dark-bg-base: #1a1a1a;
$dark-bg-secondary: #2d2d2d;
$dark-bg-tertiary: #3a3a3a;
$dark-text-primary: #e0e0e0;
$dark-text-secondary: #b0b0b0;
$dark-text-tertiary: #888888;
$dark-border: #404040;
$dark-border-light: #333333;

// 深色模式状态色
$dark-success: #52c41a;
$dark-warning: #faad14;
$dark-danger: #f5222d;
$dark-info: $kingdee-primary-light;

/* 全局主题变量定义 */
:root {
  /* ===== 浅色模式 (默认) ===== */
  
  /* 基础背景色 */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-tertiary: #f7f8fa;
  --theme-bg-disabled: #fafafa;
  
  /* 业务页面专用背景色 */
  --theme-page-bg: #f5f6fa; /* 替换页面容器背景 #f5f6fa */
  --theme-card-container-bg: #f9f9f9; /* 替换日历容器等背景 #f9f9f9 */
  --theme-white-bg: #ffffff; /* 替换所有 white 和 #fff */
  --theme-list-hover-bg: #f7f8fa; /* 替换列表悬停背景 */
  --theme-active-bg: #f5f5f5; /* 替换激活状态背景 */
  --theme-table-even-bg: #fafbfc; /* 替换表格偶数行背景 */
  --theme-table-hover-bg: #f0f7ff; /* 替换表格悬停背景 */
  
  /* 基础文字色 */
  --theme-text-primary: #333333;
  --theme-text-secondary: #666666;
  --theme-text-tertiary: #999999;
  --theme-text-disabled: #c8c9cc;
  --theme-text-placeholder: #c8c9cc;
  
  /* 业务页面专用文字色 */
  --theme-text-main: #323233; /* 替换主要文字 #323233 */
  --theme-text-label: #646566; /* 替换标签文字 #646566 */
  --theme-text-light: #969799; /* 替换浅色文字 #969799 */
  --theme-text-dark: #1a1a1a; /* 替换深色文字 */
  
  /* 边框和分割线 */
  --theme-border-color: #e8e8e8;
  --theme-border-light: #ebedf0;
  --theme-divider-color: #ebedf0;
  
  /* 阴影和层次 */
  --theme-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
  --theme-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --theme-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.12);
  --theme-shadow-card: 0 2px 12px rgba(0, 0, 0, 0.05);
  --theme-shadow-nav: 0 1px 4px rgba(0, 0, 0, 0.08); /* 导航阴影 */
  
  /* 品牌色和功能色 */
  --theme-primary: #{$kingdee-primary};
  --theme-primary-light: #{$kingdee-primary-light};
  --theme-primary-dark: #{$kingdee-primary-dark};
  --theme-success: #52c41a;
  --theme-warning: #faad14;
  --theme-danger: #f5222d;
  --theme-info: #{$kingdee-primary};
  
  /* 状态背景色 */
  --theme-success-bg: #f6ffed;
  --theme-warning-bg: #fff7e6;
  --theme-danger-bg: #fff2f0;
  --theme-info-bg: #e6f7ff;
  
  /* 状态标签专用色 */
  --theme-status-draft-bg: #f2f3f5;
  --theme-status-draft-text: #909399;
  --theme-status-submitted-bg: #e8f4ff;
  --theme-status-submitted-text: #1989fa;
  --theme-status-approved-bg: #f0f9eb;
  --theme-status-approved-text: #67c23a;
  --theme-status-reaudit-bg: #fff2e8;
  --theme-status-reaudit-text: #ff976a;
  
  /* 输入框和表单元素 */
  --theme-input-bg: #ffffff;
  --theme-input-border: #dcdfe6;
  --theme-input-focus: #{$kingdee-primary};
  --theme-input-disabled: #f5f7fa;
  
  /* 按钮相关 */
  --theme-button-default-bg: #ffffff;
  --theme-button-default-border: #dcdfe6;
  --theme-button-default-text: #606266;
  --theme-button-hover-bg: #f5f7fa;
  
  /* 卡片和容器 */
  --theme-card-bg: #ffffff;
  --theme-card-shadow: var(--theme-shadow-card);
  --theme-container-bg: #f5f5f5;
  
  /* 导航和头部 */
  --theme-header-bg: #ffffff;
  --theme-header-text: #333333;
  --theme-nav-active: #{$kingdee-primary};
  
  /* 列表和表格 */
  --theme-list-bg: #ffffff;
  --theme-list-item-bg: #ffffff;
  --theme-list-item-hover: #f7f8fa;
  --theme-list-item-active: #e6f7ff;
  --theme-table-header-bg: #fafafa;
  --theme-table-border: #ebedf0;
  
  /* 弹窗和遮罩 */
  --theme-overlay: rgba(0, 0, 0, 0.5);
  --theme-modal-bg: #ffffff;
  
  /* 图标和装饰 */
  --theme-icon-color: #969799;
  --theme-icon-active: #{$kingdee-primary};
  --theme-decoration: rgba(39, 111, 245, 0.1);
}

/* ===== 深色模式覆盖 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* 基础背景色 */
    --theme-bg-primary: #{$dark-bg-base};
    --theme-bg-secondary: #{$dark-bg-secondary};
    --theme-bg-tertiary: #{$dark-bg-tertiary};
    --theme-bg-disabled: #{color.mix($dark-bg-base, $dark-bg-secondary, 70%)};
    
    /* 业务页面专用背景色 - 深色模式适配 */
    --theme-page-bg: #{$dark-bg-base}; /* 页面容器背景深色 */
    --theme-card-container-bg: #{color.mix($dark-bg-base, $dark-bg-secondary, 60%)}; /* 日历容器背景 */
    --theme-white-bg: #{$dark-bg-secondary}; /* 原白色区域使用次级背景 */
    --theme-list-hover-bg: #{$dark-bg-tertiary}; /* 列表悬停背景 */
    --theme-active-bg: #{color.mix($dark-bg-tertiary, #ffffff, 90%)}; /* 激活状态背景 */
    --theme-table-even-bg: #{color.mix($dark-bg-base, $dark-bg-secondary, 80%)}; /* 表格偶数行 */
    --theme-table-hover-bg: #{color.mix($kingdee-primary, $dark-bg-base, 15%)}; /* 表格悬停 */
    
    /* 基础文字色 */
    --theme-text-primary: #{$dark-text-primary};
    --theme-text-secondary: #{$dark-text-secondary};
    --theme-text-tertiary: #{$dark-text-tertiary};
    --theme-text-disabled: #{color.mix($dark-text-tertiary, $dark-bg-base, 60%)};
    --theme-text-placeholder: #{$dark-text-tertiary};
    
    /* 业务页面专用文字色 - 深色模式适配 */
    --theme-text-main: #{$dark-text-primary}; /* 主要文字深色模式 */
    --theme-text-label: #{$dark-text-secondary}; /* 标签文字深色模式 */
    --theme-text-light: #{$dark-text-tertiary}; /* 浅色文字深色模式 */
    --theme-text-dark: #{$dark-text-primary}; /* 原深色文字在深色模式下使用主文字色 */
    
    /* 边框和分割线 */
    --theme-border-color: #{$dark-border};
    --theme-border-light: #{$dark-border-light};
    --theme-divider-color: #{$dark-border-light};
    
    /* 阴影和层次 - 深色模式下阴影更重 */
    --theme-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
    --theme-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.4);
    --theme-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.5);
    --theme-shadow-card: 0 2px 12px rgba(0, 0, 0, 0.3);
    --theme-shadow-nav: 0 1px 4px rgba(0, 0, 0, 0.4);
    
    /* 品牌色在深色模式下稍微调亮 */
    --theme-primary: #{$kingdee-primary-light};
    --theme-primary-light: #{color.adjust($kingdee-primary, $lightness: 20%)};
    --theme-primary-dark: #{$kingdee-primary};
    --theme-success: #{color.adjust($dark-success, $lightness: 5%)};
    --theme-warning: #{color.adjust($dark-warning, $lightness: 5%)};
    --theme-danger: #{color.adjust($dark-danger, $lightness: 10%)};
    --theme-info: #{$kingdee-primary-light};
    
    /* 状态背景色 - 深色模式下使用更暗的色调 */
    --theme-success-bg: #{color.mix($dark-success, $dark-bg-base, 15%)};
    --theme-warning-bg: #{color.mix($dark-warning, $dark-bg-base, 15%)};
    --theme-danger-bg: #{color.mix($dark-danger, $dark-bg-base, 15%)};
    --theme-info-bg: #{color.mix($kingdee-primary, $dark-bg-base, 15%)};
    
    /* 状态标签专用色 - 深色模式适配 */
    --theme-status-draft-bg: #{color.mix(#909399, $dark-bg-base, 20%)};
    --theme-status-draft-text: #{color.adjust(#909399, $lightness: 20%)};
    --theme-status-submitted-bg: #{color.mix(#1989fa, $dark-bg-base, 20%)};
    --theme-status-submitted-text: #{color.adjust(#1989fa, $lightness: 15%)};
    --theme-status-approved-bg: #{color.mix(#67c23a, $dark-bg-base, 20%)};
    --theme-status-approved-text: #{color.adjust(#67c23a, $lightness: 10%)};
    --theme-status-reaudit-bg: #{color.mix(#ff976a, $dark-bg-base, 20%)};
    --theme-status-reaudit-text: #{color.adjust(#ff976a, $lightness: 10%)};
    
    /* 输入框和表单元素 */
    --theme-input-bg: #{color.mix($dark-bg-secondary, $dark-bg-tertiary, 50%)};
    --theme-input-border: #{$dark-border};
    --theme-input-focus: #{$kingdee-primary-light};
    --theme-input-disabled: #{$dark-bg-secondary};
    
    /* 按钮相关 */
    --theme-button-default-bg: #{$dark-bg-secondary};
    --theme-button-default-border: #{$dark-border};
    --theme-button-default-text: #{$dark-text-primary};
    --theme-button-hover-bg: #{$dark-bg-tertiary};
    
    /* 卡片和容器 */
    --theme-card-bg: #{$dark-bg-secondary};
    --theme-card-shadow: var(--theme-shadow-card);
    --theme-container-bg: #{$dark-bg-base};
    
    /* 导航和头部 */
    --theme-header-bg: #{$dark-bg-secondary};
    --theme-header-text: #{$dark-text-primary};
    --theme-nav-active: #{$kingdee-primary-light};
    
    /* 列表和表格 */
    --theme-list-bg: #{$dark-bg-secondary};
    --theme-list-item-bg: #{$dark-bg-secondary};
    --theme-list-item-hover: #{$dark-bg-tertiary};
    --theme-list-item-active: #{color.mix($kingdee-primary, $dark-bg-base, 20%)};
    --theme-table-header-bg: #{$dark-bg-tertiary};
    --theme-table-border: #{$dark-border};
    
    /* 弹窗和遮罩 */
    --theme-overlay: rgba(0, 0, 0, 0.7);
    --theme-modal-bg: #{$dark-bg-secondary};
    
    /* 图标和装饰 */
    --theme-icon-color: #{$dark-text-secondary};
    --theme-icon-active: #{$kingdee-primary-light};
    --theme-decoration: #{color.mix($kingdee-primary, $dark-bg-base, 20%)};
  }
}

/* ===== 工具类 ===== */

/* 主题感知的过渡动画 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 主题感知的文字色彩 */
.theme-text-primary {
  color: var(--theme-text-primary);
}

.theme-text-secondary {
  color: var(--theme-text-secondary);
}

.theme-text-tertiary {
  color: var(--theme-text-tertiary);
}

/* 主题感知的背景色 */
.theme-bg-primary {
  background-color: var(--theme-bg-primary);
}

.theme-bg-secondary {
  background-color: var(--theme-bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--theme-bg-tertiary);
}

/* 主题感知的边框 */
.theme-border {
  border-color: var(--theme-border-color);
}

.theme-border-light {
  border-color: var(--theme-border-light);
}

/* 主题感知的卡片样式 */
.theme-card {
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-light);
  box-shadow: var(--theme-shadow-card);
}

/* 主题感知的按钮样式 */
.theme-button {
  background-color: var(--theme-button-default-bg);
  color: var(--theme-button-default-text);
  border: 1px solid var(--theme-button-default-border);
  
  &:hover {
    background-color: var(--theme-button-hover-bg);
  }
}

/* Vant 组件深色模式覆盖预设 */
@media (prefers-color-scheme: dark) {
  
  /* Vant Cell 组件 */
  .van-cell {
    background-color: var(--theme-list-item-bg) !important;
    color: var(--theme-text-primary) !important;
    border-color: var(--theme-border-light) !important;
    
    &::after {
      border-color: var(--theme-border-light) !important;
    }
    
    .van-cell__title {
      color: var(--theme-text-primary) !important;
    }
    
    .van-cell__value {
      color: var(--theme-text-secondary) !important;
    }
    
    .van-cell__label {
      color: var(--theme-text-tertiary) !important;
    }
  }
  
  /* Vant Button 组件 */
  .van-button--default {
    background-color: var(--theme-button-default-bg) !important;
    color: var(--theme-button-default-text) !important;
    border-color: var(--theme-button-default-border) !important;
  }
  
  /* Vant Field 组件 */
  .van-field__control {
    background-color: var(--theme-input-bg) !important;
    color: var(--theme-text-primary) !important;
    
    &::placeholder {
      color: var(--theme-text-placeholder) !important;
    }
  }
  
  /* Vant NavBar 组件 */
  .van-nav-bar {
    background-color: var(--theme-header-bg) !important;
    color: var(--theme-header-text) !important;
    
    .van-nav-bar__title {
      color: var(--theme-header-text) !important;
    }
  }
  
  /* Vant Popup 组件 */
  .van-popup {
    background-color: var(--theme-modal-bg) !important;
    color: var(--theme-text-primary) !important;
  }
  
  /* Vant Toast 组件 */
  .van-toast {
    background-color: var(--theme-modal-bg) !important;
    color: var(--theme-text-primary) !important;
  }
  
  /* Vant ActionSheet 组件 */
  .van-action-sheet {
    background-color: var(--theme-modal-bg) !important;
    
    .van-action-sheet__item {
      color: var(--theme-text-primary) !important;
      
      &:active {
        background-color: var(--theme-list-item-hover) !important;
      }
    }
    
    .van-action-sheet__cancel {
      background-color: var(--theme-modal-bg) !important;
      color: var(--theme-text-primary) !important;
    }
  }
  
  /* Vant Dialog 组件 */
  .van-dialog {
    background-color: var(--theme-modal-bg) !important;
    color: var(--theme-text-primary) !important;
    
    .van-dialog__title {
      color: var(--theme-text-primary) !important;
    }
    
    .van-dialog__content {
      color: var(--theme-text-secondary) !important;
    }
  }
  
  /* Vant Picker 组件 */
  .van-picker {
    background-color: var(--theme-modal-bg) !important;
    
    .van-picker-column__item {
      color: var(--theme-text-primary) !important;
    }
  }
  
  /* Vant DatetimePicker 组件 */
  .van-datetime-picker {
    background-color: var(--theme-modal-bg) !important;
  }
  
  /* Vant Calendar 组件 */
  .van-calendar {
    background-color: var(--theme-modal-bg) !important;
    
    .van-calendar__header {
      background-color: var(--theme-header-bg) !important;
    }
    
    .van-calendar__day {
      color: var(--theme-text-primary) !important;
    }
    
    .van-calendar__selected-day {
      background-color: var(--theme-primary) !important;
    }
  }
  
  /* Vant List 组件 */
  .van-list {
    background-color: var(--theme-list-bg) !important;
  }
  
  /* Vant PullRefresh 组件 */
  .van-pull-refresh {
    background-color: var(--theme-bg-primary) !important;
    
    .van-pull-refresh__text {
      color: var(--theme-text-secondary) !important;
    }
  }
  
  /* Vant SwipeCell 组件 */
  .van-swipe-cell__wrapper {
    background-color: var(--theme-list-item-bg) !important;
  }
  
  /* Vant Tab 组件 */
  .van-tabs {
    background-color: var(--theme-header-bg) !important;
    
    .van-tab {
      color: var(--theme-text-secondary) !important;
      
      &--active {
        color: var(--theme-primary) !important;
      }
    }
    
    .van-tabs__line {
      background-color: var(--theme-primary) !important;
    }
  }
  
  /* Vant Tag 组件 */
  .van-tag--default {
    background-color: var(--theme-bg-tertiary) !important;
    color: var(--theme-text-primary) !important;
  }
  
  /* Vant Loading 组件 */
  .van-loading__text {
    color: var(--theme-text-secondary) !important;
  }
} 