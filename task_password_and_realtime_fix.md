# 上下文
文件名：task_password_and_realtime_fix.md
创建于：2024年12月19日
创建者：AI助手
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
修复两个核心问题：
1. 修改密码功能需要增加原密码验证，确保修改密码时先验证原密码是否正确
2. 实现所有网页的数据实时更新机制，确保用户看到的数据始终是最新的

# 项目概述
这是一个基于Vue 2的工时报告系统，使用Element UI和Vant UI组件库，通过axios与金蝶K3Cloud ERP系统进行数据交互。当前项目结构包含登录、工时报告、审批等模块。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
## 密码修改问题分析
- 当前实现位置：`src/view/Main.vue` 的 `changePassword` 方法（第124-167行）
- 问题：直接调用 `changePassword(userId, newPassword)` API，没有验证原密码
- 相关API：`src/api/auth.js` 中的 `changePassword` 函数和 `verifyLogin` 函数
- 修改点：需要在调用密码修改API前，先验证原密码是否正确

## 实时更新问题分析
- 当前页面：工时报告页面(`src/view/WorkReport.vue`)、审批页面(`src/view/WorkReportApproval.vue`)、主页面(`src/view/Main.vue`)
- 现状：所有页面都是手动刷新或响应用户操作时才更新数据
- 技术约束：项目未使用WebSocket，主要通过HTTP API与后端交互
- 数据源：金蝶K3Cloud ERP系统，通过axios调用REST API获取数据

## 关键文件识别
- `src/view/Main.vue`：主页面，包含密码修改功能
- `src/api/auth.js`：认证API，包含密码修改和验证函数
- `src/view/WorkReport.vue`：工时报告页面，需要实时更新
- `src/view/WorkReportApproval.vue`：审批页面，需要实时更新
- `src/api/report.js`：报告相关API
- `src/api/approval.js`：审批相关API

# 提议的解决方案 (由 INNOVATE 模式填充)
## 密码修改验证解决方案
**选择方案：复用现有验证API**
- 利用现有的 `verifyLogin(username, oldPassword)` 函数验证原密码
- 在 `src/view/Main.vue` 的 `changePassword` 方法中增加原密码验证步骤
- 验证流程：原密码验证 → 密码修改 → 成功反馈
- 优势：逻辑一致性，复用现有代码，减少API依赖

## 实时更新解决方案
**选择方案：智能轮询 + 事件驱动混合机制**

### 核心组件设计
1. **数据更新管理器（DataUpdateManager）**
   - 统一管理所有页面的数据更新
   - 实现页面可见性检测
   - 提供智能轮询机制

2. **事件驱动更新**
   - 本地操作后立即更新相关数据
   - 使用Vuex或Vue事件总线进行页面间通信
   - 实现精确的增量更新

3. **智能轮询机制**
   - 基于Page Visibility API的智能轮询
   - 可配置轮询间隔（默认30秒）
   - 页面不可见时自动停止轮询

### 实现策略
- 创建全局mixin提供实时更新能力
- 在关键页面（WorkReport、WorkReportApproval）集成更新机制
- 实现数据版本控制，避免不必要的更新
- 提供手动刷新和自动刷新切换选项

# 实施计划 (由 PLAN 模式生成)
## 第一部分：密码修改验证功能

### 文件修改：`src/view/Main.vue`
- 目标：修改 `changePassword` 方法，添加原密码验证逻辑
- 具体实现：
  1. 从localStorage获取当前用户账号信息
  2. 在密码修改前调用 `verifyLogin(username, oldPassword)` 验证原密码
  3. 只有原密码验证成功后才执行 `changePassword(userId, newPassword)`
  4. 完善错误处理和用户反馈信息

## 第二部分：实时更新机制

### 新建文件：`src/utils/dataUpdateManager.js`
- 目标：创建数据更新管理器
- 功能：页面可见性检测、智能轮询、数据更新队列管理、错误重试机制
- 关键方法：`startPolling()`, `stopPolling()`, `registerUpdateTask()`, `unregisterUpdateTask()`

### 新建文件：`src/mixins/realtimeUpdate.js`
- 目标：创建全局mixin提供实时更新能力
- 功能：自动注册/注销更新任务、提供数据刷新方法、处理更新状态管理
- 生命周期集成：在mounted时注册，在beforeDestroy时注销

### 修改文件：`src/store/index.js`
- 目标：添加实时更新相关状态管理
- 新增状态：`realtimeUpdateEnabled`, `lastUpdateTime`, `dataVersions`
- 新增mutations：`SET_REALTIME_UPDATE`, `UPDATE_DATA_VERSION`, `SET_LAST_UPDATE_TIME`

### 修改文件：`src/view/WorkReport.vue`
- 目标：集成实时更新mixin
- 实现：导入realtimeUpdate mixin，配置数据更新方法，设置更新间隔

### 修改文件：`src/view/WorkReportApproval.vue`
- 目标：集成实时更新mixin
- 实现：导入realtimeUpdate mixin，配置审批数据更新方法，设置更新间隔

## 技术规范详述

### 密码验证流程
1. 用户输入验证：检查原密码、新密码、确认密码的完整性
2. 密码一致性验证：确保两次新密码输入一致
3. 原密码验证：调用 `verifyLogin(username, oldPassword)`
4. 密码修改：验证成功后调用 `changePassword(userId, newPassword)`
5. 结果反馈：显示成功或失败信息给用户

### 实时更新架构
1. **DataUpdateManager** 核心功能：
   - 使用 `document.visibilityState` 检测页面可见性
   - 可配置轮询间隔（默认30秒）
   - 维护更新任务队列
   - 实现指数退避的错误重试机制

2. **RealtimeUpdate Mixin** 功能：
   - 提供 `enableRealtimeUpdate()` 和 `disableRealtimeUpdate()` 方法
   - 自动处理组件生命周期集成
   - 提供数据刷新状态管理

3. **数据版本控制**：
   - 为每个数据源维护版本时间戳
   - 服务器响应数据时比较版本
   - 实现增量更新，减少不必要的UI刷新

实施检查清单：
1. 修改 `src/view/Main.vue` 的密码修改功能，添加原密码验证
2. 创建 `src/utils/dataUpdateManager.js` 数据更新管理器
3. 创建 `src/mixins/realtimeUpdate.js` 实时更新mixin
4. 更新 `src/store/index.js` 添加实时更新状态管理
5. 修改 `src/view/WorkReport.vue` 集成实时更新功能  
6. 修改 `src/view/WorkReportApproval.vue` 集成实时更新功能
7. 测试密码验证功能的正确性
8. 测试实时更新机制在不同场景下的表现
9. 验证页面可见性切换时轮询的启停
10. 确认所有修改不影响现有功能的正常运行 

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "1. 修改 `src/view/Main.vue` 的密码修改功能，添加原密码验证"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*   2024-12-19 当前时间
    *   步骤：1. 修改 `src/view/Main.vue` 的密码修改功能，添加原密码验证
    *   修改：
      - 在script标签中导入 `verifyLogin` 函数
      - 修改 `changePassword` 方法，添加表单完整性验证
      - 在密码修改前调用 `verifyLogin(username, oldPassword)` 验证原密码
      - 只有原密码验证成功后才执行密码修改
      - 添加详细的错误处理和用户反馈
      - 密码修改成功后提示用户重新登录以确保安全
    *   更改摘要：完成密码修改功能的安全增强，现在用户必须输入正确的原密码才能修改密码
    *   原因：执行计划步骤 1
    *   阻碍：无
    *   状态：待确认 