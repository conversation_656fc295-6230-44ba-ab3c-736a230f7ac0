import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    // 用户信息
    userInfo: null,
    // 是否已登录
    isLoggedIn: !!localStorage.getItem('UserToken'),
    // 应用功能列表
    appFeatures: [
      { id: 1, name: '签到', icon: 'records', path: '/check-in' },
      { id: 2, name: '移动销售', icon: 'cart', path: '/mobile-sales' },
      { id: 3, name: '智能审批', icon: 'passed', path: '/approval' },
      { id: 4, name: '时间助手', icon: 'clock', path: '/time-assistant' },
      { id: 5, name: '知识中心', icon: 'notes', path: '/knowledge' },
      { id: 6, name: '工作汇报', icon: 'chart', path: '/work-report' },
      { id: 7, name: '办公用品', icon: 'label', path: '/office-supplies' },
      { id: 8, name: '智能会议', icon: 'friends', path: '/meeting' }
    ],
    // 广告轮播图
    banners: [
      { id: 1, image: require('@/assets/banner-midautumn.jpg'), title: '欢迎中秋 欢度国庆' }
    ],
    // 当前选中的Tab
    activeTab: 0,
    // 工时报告单据编号
    fbillno: null
  },
  mutations: {
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      state.isLoggedIn = true;
    },
    // 清除用户信息
    CLEAR_USER_INFO(state) {
      state.userInfo = null;
      state.isLoggedIn = false;
      localStorage.removeItem('UserToken');
    },
    // 切换Tab
    SET_ACTIVE_TAB(state, index) {
      state.activeTab = index;
    },
    // 保存FBillno
    SET_FBILLNO(state, fbillno) {
      state.fbillno = fbillno;
      console.log('[Store] FBillno已更新:', fbillno);
    }
  },
  actions: {
    // 登录
    login({ commit }, userInfo) {
      // 这里通常会发送请求到后端进行验证
      // 模拟的例子：
      return new Promise((resolve) => {
        setTimeout(() => {
          commit('SET_USER_INFO', userInfo);
          localStorage.setItem('UserToken', 'demo-token');
          resolve();
        }, 300);
      });
    },
    // 登出
    logout({ commit }) {
      commit('CLEAR_USER_INFO');
    },
    // 保存FBillno
    saveFBillno({ commit }, fbillno) {
      commit('SET_FBILLNO', fbillno);
    }
  },
  getters: {
    // 获取用户名称
    userName: state => {
      return state.userInfo ? state.userInfo.name : '未登录';
    },
    // 按Tab分组的应用功能
    featuresByTab: state => {
      const tabs = [
        { id: 0, features: state.appFeatures.slice(0, 4) }, // 协同办公
        { id: 1, features: state.appFeatures.slice(4, 6) }, // 企业管理
        { id: 2, features: state.appFeatures.slice(6)     } // 自建应用
      ];
      return tabs;
    },
    // 获取FBillno
    fbillno: state => {
      return state.fbillno;
    }
  }
}); 