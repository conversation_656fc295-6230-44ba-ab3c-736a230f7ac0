<template>
  <el-form 
    ref="form" 
    :model="form" 
    :rules="formRules" 
    class="login-form"
    :class="{'touch-optimized': isMobile}"
  >
    <!-- 用户名输入框 -->
    <el-form-item prop="username">
      <el-input
        v-model="form.username"
        prefix-icon="el-icon-user"
        placeholder="请输入用户名"
        maxlength="30"
        autocomplete="off"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </el-form-item>
    
    <!-- 密码输入框 -->
    <el-form-item prop="password">
      <el-input
        v-model="form.password"
        prefix-icon="el-icon-lock"
        type="password"
        placeholder="请输入密码"
        maxlength="20"
        autocomplete="new-password"
        @keyup.enter.native="submitLogin"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </el-form-item>
    
    <!-- 记住密码选项 -->
    <el-form-item class="remember-item">
      <el-checkbox 
        v-model="form.remember"
        @change="handleRememberChange"
      >记住密码</el-checkbox>
      
      <span 
        class="forget-pwd" 
        @click="onForgetPassword"
        @touchstart="addActiveClass"
        @touchend="removeActiveClass"
      >忘记密码?</span>
    </el-form-item>
    
    <!-- 登录按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        class="login-button"
        :loading="loading"
        @click="submitLogin"
        @touchstart="addActiveClass"
        @touchend="removeActiveClass"
      >
        登录
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'LoginForm',
  props: {
    isMobile: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 用户名验证规则
    const validateUsername = (rule, value, callback) => {
      if (!value || value.trim() === '') {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    
    // 密码验证规则
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        callback()
      }
    }
    
    return {
      form: {
        username: '',
        password: '',
        remember: false
      },
      formRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      isKeyboardVisible: false,
      activeElement: null,
      isFocused: false,
      touchFeedbackEnabled: 'ontouchstart' in window
    }
  },
  created() {
    // 添加触摸状态检测
    this.detectTouchCapability()
  },
  mounted() {
    // 从本地存储恢复登录信息
    this.restoreLoginInfo()
    
    // 设置表单元素的触摸事件
    if (this.isMobile && this.touchFeedbackEnabled) {
      this.setupTouchEvents()
    }
  },
  beforeDestroy() {
    // 清理事件监听器
    if (this.isMobile && this.touchFeedbackEnabled) {
      this.cleanupTouchEvents()
    }
  },
  methods: {
    // 检测设备触摸能力
    detectTouchCapability() {
      this.touchFeedbackEnabled = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    },
    
    // 设置触摸事件
    setupTouchEvents() {
      try {
        const touchElements = this.$el.querySelectorAll('.el-button, .forget-pwd, .el-checkbox')
        
        touchElements.forEach(el => {
          el.addEventListener('touchstart', this.addActiveClass)
          el.addEventListener('touchend', this.removeActiveClass)
          el.addEventListener('touchcancel', this.removeActiveClass)
        })
      } catch (e) {
        console.warn('无法设置触摸事件:', e)
      }
    },
    
    // 清理触摸事件
    cleanupTouchEvents() {
      try {
        const touchElements = this.$el.querySelectorAll('.el-button, .forget-pwd, .el-checkbox')
        
        touchElements.forEach(el => {
          el.removeEventListener('touchstart', this.addActiveClass)
          el.removeEventListener('touchend', this.removeActiveClass)
          el.removeEventListener('touchcancel', this.removeActiveClass)
        })
      } catch (e) {
        console.warn('无法清理触摸事件:', e)
      }
    },
    
    // 从本地存储恢复登录信息
    restoreLoginInfo() {
      try {
        if (localStorage.getItem('kingdee_username')) {
          this.form.username = localStorage.getItem('kingdee_username')
          if (localStorage.getItem('kingdee_password')) {
            this.form.password = localStorage.getItem('kingdee_password')
          }
          this.form.remember = true
        }
      } catch (e) {
        console.warn('无法从本地存储恢复登录信息', e)
      }
    },
    
    // 提交登录
    submitLogin() {
      // 移除焦点，关闭键盘
      if (this.isMobile && document.activeElement) {
        document.activeElement.blur()
      }
      
      this.$refs.form.validate(valid => {
        if (valid) {
          // 震动反馈（如果可用）
          this.vibrateDevice()
          
          // 保存登录信息到本地存储
          this.saveLoginInfo()
          
          // 触发登录事件
          this.$emit('login', { ...this.form })
        } else {
          // 震动反馈（如果可用）表示验证失败
          this.vibrateDevice('error')
          return false
        }
      })
    },
    
    // 设备震动反馈
    vibrateDevice(type = 'success') {
      if (navigator.vibrate) {
        try {
          if (type === 'error') {
            // 错误模式：三次短震动
            navigator.vibrate([70, 50, 70, 50, 70])
          } else {
            // 成功模式：一次短震动
            navigator.vibrate(50)
          }
        } catch (e) {
          console.warn('振动反馈失败:', e)
        }
      }
    },
    
    // 保存登录信息到本地存储
    saveLoginInfo() {
      try {
        if (this.form.remember) {
          localStorage.setItem('kingdee_username', this.form.username)
          localStorage.setItem('kingdee_password', this.form.password)
        } else {
          localStorage.removeItem('kingdee_username')
          localStorage.removeItem('kingdee_password')
        }
      } catch (e) {
        console.warn('无法保存登录信息到本地存储', e)
      }
    },
    
    // 处理记住密码状态变化
    handleRememberChange(val) {
      // 如果取消记住密码，下次需要清除存储
      if (!val) {
        try {
          localStorage.removeItem('kingdee_username')
          localStorage.removeItem('kingdee_password')
        } catch (e) {
          console.warn('无法移除存储的登录信息', e)
        }
      }
    },
    
    // 添加触摸时的活跃类
    addActiveClass(event) {
      if (this.isMobile && event && event.currentTarget) {
        try {
          event.currentTarget.classList.add('active-touch')
          this.activeElement = event.currentTarget
        } catch (e) {
          console.warn('添加活跃样式失败:', e)
        }
      }
    },
    
    // 移除触摸时的活跃类
    removeActiveClass() {
      if (this.isMobile && this.activeElement) {
        setTimeout(() => {
          try {
            if (this.activeElement && this.activeElement.classList) {
              this.activeElement.classList.remove('active-touch')
            }
            this.activeElement = null
          } catch (e) {
            console.warn('移除活跃样式失败:', e)
          }
        }, 150) // 稍微延迟，使反馈效果更明显
      }
    },
    
    // 处理输入框获得焦点
    handleFocus(event) {
      this.isFocused = true
      
      if (this.isMobile) {
        // 标记当前获焦的表单项
        if (event && event.target) {
          try {
            const formItem = event.target.closest('.el-form-item')
            if (formItem && formItem.classList) {
              formItem.classList.add('is-focus')
            }
          } catch (e) {
            console.warn('添加焦点样式失败:', e)
          }
        }
        
        // 通知父组件键盘可能已弹出
        this.$emit('keyboard-change', true)
      }
    },
    
    // 处理输入框失去焦点
    handleBlur(event) {
      this.isFocused = false
      
      if (this.isMobile) {
        // 移除表单项的焦点状态
        if (event && event.target) {
          try {
            const formItem = event.target.closest('.el-form-item')
            if (formItem && formItem.classList) {
              formItem.classList.remove('is-focus')
            }
          } catch (e) {
            console.warn('移除焦点样式失败:', e)
          }
        }
        
        // 检查是否切换到另一个输入框
        const isAnotherInput = event && event.relatedTarget && 
            (event.relatedTarget.tagName === 'INPUT' || 
             event.relatedTarget.tagName === 'TEXTAREA')
             
        if (!isAnotherInput) {
          // 通知父组件键盘可能已收起
          setTimeout(() => {
            this.$emit('keyboard-change', false)
          }, 100)
        }
      }
    },
    
    // 处理忘记密码
    onForgetPassword() {
      this.$emit('forget-password')
    },
    
    // 重置表单状态
    reset() {
      if (!this.form.remember) {
        this.form.username = ''
        this.form.password = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入变量
$kingdee-primary: #276ff5; // 金蝶主蓝色
$kingdee-secondary: #1890ff; // 浅蓝色

// 登录表单样式
.login-form {
  width: 100%;
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    
    &.is-focus {
      transform: translateY(-2px);
      
      &::before {
        content: '';
        position: absolute;
        left: -10px;
        top: 10%;
        height: 80%;
        width: 3px;
        background: linear-gradient(to bottom, $kingdee-primary, $kingdee-secondary);
        border-radius: 3px;
        opacity: 0.7;
        transform: scaleY(1);
        transition: all 0.3s ease;
      }
      
      :deep(.el-input__inner) {
        border-color: $kingdee-primary;
        box-shadow: 0 0 0 2px rgba($kingdee-primary, 0.1);
      }
      
      :deep(.el-input__prefix .el-input__icon) {
        color: $kingdee-primary;
        transform: scale(1.1);
      }
    }
    
    &.is-error {
      animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
      
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        20%, 60% { transform: translateX(-5px); }
        40%, 80% { transform: translateX(5px); }
      }
    }
  }
  
  :deep(.el-input) {
    .el-input__inner {
      height: 40px;
      border-radius: 4px;
      transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
      
      &:focus {
        border-color: $kingdee-primary;
        box-shadow: 0 0 0 2px rgba($kingdee-primary, 0.1);
      }
    }
    
    .el-input__prefix {
      left: 5px;
      
      .el-input__icon {
        line-height: 40px;
        transition: all 0.3s;
      }
    }
  }
  
  .remember-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    
    :deep(.el-checkbox__label) {
      color: var(--login-text-light, #666);
    }
    
    .forget-pwd {
      color: $kingdee-secondary;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
      padding: 5px;
      position: relative;
      
      &:hover {
        color: $kingdee-primary;
        text-decoration: underline;
      }
      
      // 增加移动端触摸面积
      &::after {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        z-index: -1;
      }
    }
  }
  
  .login-button {
    width: 100%;
    height: 44px;
    background: $kingdee-primary;
    border-color: $kingdee-primary;
    transition: all 0.3s;
    font-size: 16px;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    
    &:hover, &:focus {
      background-color: lighten($kingdee-primary, 10%);
      border-color: lighten($kingdee-primary, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($kingdee-primary, 0.3);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    // 波纹效果
    &::after {
      content: '';
      display: block;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      pointer-events: none;
      background-image: radial-gradient(circle, rgba(255,255,255,0.3) 10%, transparent 10.01%);
      background-repeat: no-repeat;
      background-position: 50%;
      transform: scale(10, 10);
      opacity: 0;
      transition: transform .5s, opacity 1s;
    }
    
    &:active::after {
      transform: scale(0, 0);
      opacity: 0.3;
      transition: 0s;
    }
  }
}

// 移动端触摸优化
.touch-optimized {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.el-input) {
    .el-input__inner {
      height: 46px;
      font-size: 16px;
      padding-left: 35px;
    }
    
    .el-input__prefix {
      .el-input__icon {
        line-height: 46px;
        font-size: 18px;
      }
    }
  }
  
  .remember-item {
    justify-content: center;
    gap: 20px;
    
    :deep(.el-checkbox__label) {
      font-size: 14px;
    }
    
    .forget-pwd {
      padding: 8px;
    }
  }
  
  .login-button {
    height: 48px;
    font-size: 17px;
    box-shadow: 0 4px 10px rgba($kingdee-primary, 0.2);
  }
  
  // 超小屏幕优化
  @media (max-width: 375px) {
    .remember-item {
      flex-direction: column;
      gap: 10px;
      margin-bottom: 20px;
      
      .forget-pwd {
        margin-top: 5px;
      }
    }
  }
  
  // 触摸反馈效果
  .active-touch {
    opacity: 0.7;
    transform: scale(0.98);
    transition: all 0.15s ease;
  }
}

/* 登录表单锁定为浅色模式 - 移除深色模式适配 */
.login-form {
  color-scheme: light !important;
}

/*
// 深色模式适配 - 已禁用，登录表单锁定为浅色模式
@media (prefers-color-scheme: dark) {
  :deep(.el-input) {
    .el-input__inner {
      background-color: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      color: #e0e0e0;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }
      
      &:focus {
        border-color: $kingdee-primary;
      }
    }
    
    .el-input__prefix {
      .el-input__icon {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
  
  :deep(.el-checkbox__label) {
    color: rgba(255, 255, 255, 0.7) !important;
  }
  
  .login-form .login-button {
    background: darken($kingdee-primary, 5%);
    border-color: darken($kingdee-primary, 5%);
    
    &:hover, &:focus {
      background-color: $kingdee-primary;
      border-color: $kingdee-primary;
    }
  }
}
*/
</style> 