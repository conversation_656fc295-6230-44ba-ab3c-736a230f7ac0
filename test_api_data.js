/**
 * 测试API数据获取和格式化
 * 验证FProj.FID和FTask.FID是否正确获取和保留
 */

// 模拟一个完整的API响应数据（数组格式）
const mockApiResponse = [
  [
    'GSHB001',           // 0: FBillno
    '测试项目A',          // 1: FProj.FName
    '开发任务1',          // 2: FTask.FName
    'B',                 // 3: FDocumentStatus
    '2024-01-01',        // 4: FPlanStartDate
    '2024-01-31',        // 5: FPlanEndDate
    40,                  // 6: FPlanWorkHours
    8,                   // 7: FManHour
    2,                   // 8: FOverTime
    80,                  // 9: FFinishRate
    '完成了功能开发',      // 10: FReport
    false,               // 11: FisFinish
    '开发工程师',         // 12: FPOST.FName
    '2024-01-15',        // 13: FHBDate
    '张三',              // 14: FReportEmp.FName
    'FID123',            // 15: FID
    8.5,                 // 16: FConfirmTime
    '',                  // 17: FOtherTaskName
    '',                  // 18: FOtherTaskType
    '',                  // 19: FTmpTaskTypeid.Fname
    '',                  // 20: FBurnOffTypeid.Fname
    16,                  // 21: FSUMTIME
    '2024-01-15',        // 22: FSubmitDate
    'TASK_FID_001',      // 23: FTask.FID (任务内码)
    'PROJ_FID_001'       // 24: FProj.FID (项目内码)
  ],
  [
    'GSHB002',           // 0: FBillno
    '测试项目B',          // 1: FProj.FName
    '测试任务2',          // 2: FTask.FName
    'C',                 // 3: FDocumentStatus
    '2024-02-01',        // 4: FPlanStartDate
    '2024-02-28',        // 5: FPlanEndDate
    32,                  // 6: FPlanWorkHours
    6,                   // 7: FManHour
    1,                   // 8: FOverTime
    90,                  // 9: FFinishRate
    '完成了测试工作',      // 10: FReport
    true,                // 11: FisFinish
    '测试工程师',         // 12: FPOST.FName
    '2024-02-15',        // 13: FHBDate
    '李四',              // 14: FReportEmp.FName
    'FID456',            // 15: FID
    7,                   // 16: FConfirmTime
    '',                  // 17: FOtherTaskName
    '',                  // 18: FOtherTaskType
    '',                  // 19: FTmpTaskTypeid.Fname
    '',                  // 20: FBurnOffTypeid.Fname
    14,                  // 21: FSUMTIME
    '2024-02-15',        // 22: FSubmitDate
    'TASK_FID_002',      // 23: FTask.FID (任务内码)
    'PROJ_FID_002'       // 24: FProj.FID (项目内码)
  ]
];

// 模拟formatApprovalData函数的核心逻辑
function testFormatApprovalData(apiData) {
  console.log('========== 测试数据格式化 ==========');
  console.log('输入数据:', apiData);
  console.log('输入数据长度:', apiData.length);
  
  const formattedData = apiData.map((item, index) => {
    console.log(`\n--- 处理第${index + 1}条数据 ---`);
    console.log('原始数据:', item);
    console.log('数据类型:', Array.isArray(item) ? '数组' : '对象');
    console.log('数据长度:', Array.isArray(item) ? item.length : Object.keys(item).length);
    
    if (Array.isArray(item)) {
      console.log('数组索引映射:');
      console.log('  [0] FBillno:', item[0]);
      console.log('  [1] FProj.FName:', item[1]);
      console.log('  [2] FTask.FName:', item[2]);
      console.log('  [23] FTask.FID:', item[23]);
      console.log('  [24] FProj.FID:', item[24]);
      
      const rowData = {
        id: item[0] || '',                 // FBillno
        project: item[1] || '',            // FProj.FName
        taskName: item[2] || '',           // FTask.FName
        documentStatus: item[3] || '',     // FDocumentStatus
        planStartDate: item[4] || '',      // FPlanStartDate
        planEndDate: item[5] || '',        // FPlanEndDate
        planHours: item[6] || 0,           // FPlanWorkHours
        hours: item[7] || 0,               // FManHour (汇报工时)
        overtimeHours: item[8] || 0,       // FOverTime
        progress: item[9] || 0,            // FFinishRate
        content: item[10] || '',           // FReport
        isFinish: item[11] || '',          // FisFinish
        position: item[12] || '',          // FPOST.FName
        workDate: item[13] || '',          // FHBDate
        submitter: item[14] || '',         // FReportEmp.FName
        fid: item[15] || '',               // FID
        confirmTime: item[16] || '',       // FConfirmTime
        otherTaskName: item[17] || '',     // FOtherTaskName
        otherTaskType: item[18] || '',     // FOtherTaskType
        tmpTaskTypeName: item[19] || '',   // FTmpTaskTypeid.Fname
        burnOffTypeName: item[20] || '',   // FBurnOffTypeid.Fname
        sumTime: item[21] || 0,            // FSUMTIME (已报工时)
        submitDate: item[22] || '',        // FSubmitDate
        taskFID: item[23] || '',           // FTask.FID
        projectFID: item[24] || ''         // FProj.FID
      };
      
      console.log('解析后的关键字段:');
      console.log('  工单号 (id):', rowData.id);
      console.log('  项目名称 (project):', rowData.project);
      console.log('  任务名称 (taskName):', rowData.taskName);
      console.log('  任务内码 (taskFID):', rowData.taskFID);
      console.log('  项目内码 (projectFID):', rowData.projectFID);
      console.log('  汇报工时 (hours):', rowData.hours);
      console.log('  已报工时 (sumTime):', rowData.sumTime);
      
      // 验证内码是否正确获取
      if (rowData.taskFID && rowData.projectFID) {
        console.log('✅ 内码数据获取成功');
      } else {
        console.log('❌ 内码数据获取失败');
        console.log('  taskFID:', rowData.taskFID);
        console.log('  projectFID:', rowData.projectFID);
      }
      
      return {
        id: rowData.id,
        submitter: rowData.submitter,
        project: rowData.project,
        taskName: rowData.taskName,
        content: rowData.content,
        hours: rowData.hours,
        sumTime: rowData.sumTime,
        taskFID: rowData.taskFID,          // 任务内码
        projectFID: rowData.projectFID,    // 项目内码
        status: rowData.documentStatus === 'C' ? 'approved' : 'pending'
      };
    }
    
    return null;
  }).filter(item => item !== null);
  
  console.log('\n========== 格式化结果 ==========');
  console.log('格式化后数据条数:', formattedData.length);
  formattedData.forEach((item, index) => {
    console.log(`\n第${index + 1}条格式化数据:`);
    console.log('  工单号:', item.id);
    console.log('  项目名称:', item.project);
    console.log('  任务名称:', item.taskName);
    console.log('  任务内码:', item.taskFID);
    console.log('  项目内码:', item.projectFID);
    console.log('  汇报工时:', item.hours);
    console.log('  已报工时:', item.sumTime);
    
    if (item.taskFID && item.projectFID) {
      console.log('  ✅ 内码保留成功');
    } else {
      console.log('  ❌ 内码保留失败');
    }
  });
  
  return formattedData;
}

// 运行测试
console.log('开始测试API数据格式化...\n');
const result = testFormatApprovalData(mockApiResponse);

console.log('\n========== 最终验证 ==========');
console.log('总共处理数据条数:', result.length);
const successCount = result.filter(item => item.taskFID && item.projectFID).length;
console.log('成功保留内码的数据条数:', successCount);
console.log('成功率:', `${successCount}/${result.length} (${(successCount/result.length*100).toFixed(1)}%)`);

if (successCount === result.length) {
  console.log('🎉 所有数据的内码都成功保留！');
} else {
  console.log('⚠️  部分数据的内码保留失败，请检查数据格式或索引映射');
}

console.log('\n========== 测试完成 ==========');
