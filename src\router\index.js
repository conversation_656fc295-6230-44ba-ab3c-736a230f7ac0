import Vue from 'vue'
import VueRouter from 'vue-router'
import { isSessionValid, refreshSessionId } from '../utils/session'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'LoginPage',
    component: () => import('../view/login.vue')
  },
  {
    path: '/main',
    name: 'MainInfo',
    component: () => import('../view/Main.vue')
  },
  {
    path: '/report',
    name: 'WorkReport',
    component: () => import('../view/WorkReport.vue')
  },
  {
    path: '/approval',
    name: 'WorkReportApproval',
    component: () => import('../view/WorkReportApproval.vue')
  },
  {
    path: '/statistics',
    name: 'WorkReportStatistics',
    component: () => import('../view/WorkReportStatistics.vue')
  },
  {
    path: '/work-report',
    redirect: '/report'
  }
]

const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫，检查登录状态和SessionId有效性
router.beforeEach((to, from, next) => {
  // 检查金蝶API的登录状态
  const isAuthenticated = localStorage.getItem('UserToken')
  
  if (to.path !== '/login' && !isAuthenticated) {
    // 如果不是登录页面，且没有登录，则重定向到登录页
    next('/login')
    return
  }
  
  // 如果用户已登录且不是前往登录页，检查SessionId是否有效
  if (isAuthenticated && to.path !== '/login') {
    // 检查SessionId是否有效
    if (!isSessionValid()) {
      console.log('路由守卫检测到SessionId无效，尝试刷新...')
      // SessionId无效，尝试刷新
      refreshSessionId(true)
        .then(sessionId => {
          console.log('路由守卫已刷新SessionId:', sessionId)
          next()
        })
        .catch(error => {
          console.error('路由守卫刷新SessionId失败:', error.message)
          // 如果刷新失败，可能需要重新登录
          next('/login')
        })
    } else {
      // SessionId有效，继续导航
      next()
    }
  } else {
    // 用户未登录或前往登录页，直接继续
    next()
  }
})

export default router 