# 上下文
文件名：任务修改记录.md
创建于：2024-12-19
创建者：用户/AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
用户需要对工时汇报系统进行以下修改：
1. 将所有按钮或主题颜色更改为 #276ff5
2. 取消欢迎页
3. 工时汇报的日期改成审核通过的那天的下面是对勾（如图），选择的当天变成蓝色来区分
4. 取消暂存/已提交/已审核左边的日期显示

# 项目概述
这是一个基于Vue.js的工时汇报系统，使用了Vant UI组件库，包含登录、工时汇报、审批和统计等功能模块。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)

## 项目结构分析
- 主要技术栈：Vue.js + Vant UI + SCSS
- 核心文件：
  - `src/view/login.vue` - 登录页面，包含欢迎弹窗
  - `src/view/WorkReport.vue` - 工时汇报主页面
  - `src/assets/kingdee-theme.scss` - 主题样式文件
  - `src/App.vue` - 全局样式配置

## 当前主题色分析
- 在 `login.vue` 中发现主题色变量：`$kingdee-primary: #276ff5`（已经是目标颜色）
- 在 `kingdee-theme.scss` 中发现主题色：`$kingdee-primary: #0e5cb3`（需要修改）
- 多个组件中使用了 `$kingdee-blue` 变量（需要统一）

## 欢迎页分析
- 在 `login.vue` 中发现欢迎弹窗相关代码：
  - 模板中的欢迎弹窗：`showWelcome` 控制显示
  - 登录成功后会显示欢迎弹窗：`this.loginStatus.showWelcome = true`

## 工时汇报日期显示分析
- 在 `WorkReport.vue` 第102行发现：`暂存/已提交/已审核 {{ taskStatusStats }}`
- 日期显示在 `task-date-header` 中：`{{ formattedSelectedDate }}`
- 日历组件中有审核状态相关的日期标记逻辑

## 关键发现
1. 主题色在不同文件中不统一，需要统一为 #276ff5
2. 欢迎弹窗通过 `showWelcome` 变量控制
3. 任务状态统计显示在日期旁边，需要移除日期部分
4. 日历中需要添加审核通过日期的对勾标记功能

# 提议的解决方案 (由 INNOVATE 模式填充)

## 方案1：主题色统一修改
**实施策略**：通过修改SCSS变量实现全局主题色统一
- 修改 `kingdee-theme.scss` 中的主题色变量为 #276ff5
- 统一所有组件中的 `$kingdee-blue` 等相关变量
- 检查并更新任何硬编码的颜色值

**优势**：影响范围可控，保持现有组件结构，实现全局一致性

## 方案2：欢迎页取消
**实施策略**：直接移除欢迎弹窗功能
- 注释或删除 `login.vue` 中的欢迎弹窗模板代码
- 移除登录成功后显示欢迎弹窗的逻辑
- 清理相关的样式定义

**优势**：简单直接，符合用户明确需求，减少代码复杂度

## 方案3：日历审核状态标记
**实施策略**：扩展日历组件功能，添加审核状态视觉标记
- 在日历数据结构中添加审核状态字段
- 为审核通过的日期添加对勾图标显示
- 为选中日期添加蓝色背景样式区分
- 可能需要调用API获取历史审核状态数据

**创新点**：提升用户体验，直观显示审核状态，保持日历选择功能

## 方案4：任务状态显示优化
**实施策略**：重构任务状态显示区域
- 将日期显示和状态统计分离到不同的UI区域
- 移除状态统计前的日期显示
- 保持状态统计的功能性和可读性

**优势**：界面更简洁，突出重要的状态信息

## 推荐实施顺序
1. 主题色统一（影响最广，优先处理）
2. 欢迎页取消（简单快速）
3. 任务状态显示优化（中等复杂度）
4. 日历审核状态标记（最复杂，需要数据支持）

## 技术考虑
- 所有修改都相对独立，可以分步实施
- 日历标记功能可能需要额外的API调用来获取审核状态
- 需要确保修改不影响现有的核心功能

# 实施计划 (由 PLAN 模式生成)

## 第一阶段：主题色统一修改

### 目标
将所有组件的主题色统一为 #276ff5，确保全局视觉一致性。

### 具体修改内容
1. **修改主题样式文件**
   - 文件：`src/assets/kingdee-theme.scss`
   - 修改：将 `$kingdee-primary: #0e5cb3` 改为 `$kingdee-primary: #276ff5`
   - 修改：将其他相关蓝色变量统一为 #276ff5

2. **统一各组件中的颜色变量**
   - 文件：`src/view/WorkReport.vue`
   - 修改：将 `$kingdee-blue` 等变量统一为 `$kingdee-primary`
   - 文件：`src/view/WorkReportApproval.vue`
   - 修改：统一颜色变量引用
   - 文件：`src/view/WorkReportStatistics.vue`
   - 修改：统一颜色变量引用
   - 文件：`src/components/MobileHeader.vue`
   - 修改：统一颜色变量引用
   - 文件：`src/components/LoginForm.vue`
   - 修改：统一颜色变量引用

## 第二阶段：欢迎页取消

### 目标
完全移除登录后的欢迎弹窗功能。

### 具体修改内容
1. **移除欢迎弹窗模板代码**
   - 文件：`src/view/login.vue`
   - 修改：删除或注释第76-95行的欢迎弹窗模板代码

2. **移除欢迎弹窗逻辑**
   - 文件：`src/view/login.vue`
   - 修改：删除 `showWelcome: false` 数据属性
   - 修改：移除登录成功后的 `this.loginStatus.showWelcome = true` 逻辑

3. **清理相关样式**
   - 文件：`src/view/login.vue`
   - 修改：删除 `:deep(.welcome-dialog)` 相关样式定义

## 第三阶段：任务状态显示优化

### 目标
重构任务列表头部，分离日期显示和状态统计，移除状态统计前的日期。

### 具体修改内容
1. **修改任务列表头部结构**
   - 文件：`src/view/WorkReport.vue`
   - 修改：重构 `task-date-header` 区域的HTML结构
   - 修改：将日期显示和状态统计分离到不同的区域

2. **调整样式布局**
   - 文件：`src/view/WorkReport.vue`
   - 修改：更新相关CSS样式以适应新的布局结构

## 第四阶段：日历审核状态标记

### 目标
为日历组件添加审核状态的视觉标记，审核通过的日期显示对勾，选中日期显示蓝色背景。

### 具体修改内容
1. **扩展日历数据结构**
   - 文件：`src/view/WorkReport.vue`
   - 修改：在日历数据中添加审核状态字段
   - 修改：扩展 `calendarDays` 计算属性以包含审核状态

2. **添加审核状态标记**
   - 文件：`src/view/WorkReport.vue`
   - 修改：在日历模板中添加对勾图标的条件渲染
   - 修改：为审核通过的日期添加特殊样式类

3. **优化选中日期样式**
   - 文件：`src/view/WorkReport.vue`
   - 修改：增强选中日期的蓝色背景样式
   - 修改：确保选中状态与审核状态标记的视觉层次

4. **添加获取审核状态的方法**
   - 文件：`src/view/WorkReport.vue`
   - 修改：添加方法来获取历史日期的审核状态
   - 修改：集成到现有的数据加载流程中

## 实施检查清单

1. 修改 `src/assets/kingdee-theme.scss` 中的主题色变量为 #276ff5
2. 统一 `src/view/WorkReport.vue` 中的颜色变量引用
3. 统一 `src/view/WorkReportApproval.vue` 中的颜色变量引用
4. 统一 `src/view/WorkReportStatistics.vue` 中的颜色变量引用
5. 统一 `src/components/MobileHeader.vue` 中的颜色变量引用
6. 统一 `src/components/LoginForm.vue` 中的颜色变量引用
7. 移除 `src/view/login.vue` 中的欢迎弹窗模板代码
8. 移除 `src/view/login.vue` 中的欢迎弹窗相关逻辑和数据
9. 清理 `src/view/login.vue` 中的欢迎弹窗样式定义
10. 重构 `src/view/WorkReport.vue` 中的任务状态显示区域
11. 扩展 `src/view/WorkReport.vue` 中的日历数据结构以支持审核状态
12. 在 `src/view/WorkReport.vue` 中添加日历审核状态的视觉标记
13. 优化 `src/view/WorkReport.vue` 中选中日期的蓝色背景样式
14. 在 `src/view/WorkReport.vue` 中添加获取审核状态的方法 