/**
 * Session管理工具
 * 提供KDSVCSessionId的获取、验证和刷新功能
 */
import { apiLogin } from '../api/auth';

// SessionId在localStorage中的键名
const SESSION_ID_KEY = 'KDSVCSessionId';
// SessionId最后刷新时间的键名
const SESSION_LAST_REFRESH_KEY = 'KDSessionLastRefresh';
// SessionId过期时间（毫秒），目前设置为2小时
const SESSION_EXPIRY = 2 * 60 * 60 * 1000;
// 是否正在刷新SessionId
let isRefreshingSession = false;
// 等待SessionId刷新的请求队列
let pendingRequests = [];

/**
 * 获取当前存储的SessionId
 * @returns {string|null} 当前保存的SessionId，如果没有则返回null
 */
export function getSessionId() {
  return localStorage.getItem(SESSION_ID_KEY);
}

/**
 * 保存SessionId到localStorage
 * @param {string} sessionId 要保存的SessionId
 */
export function saveSessionId(sessionId) {
  if (!sessionId) {
    console.warn('尝试保存的SessionId为空');
    return;
  }
  
  localStorage.setItem(SESSION_ID_KEY, sessionId);
  // 保存刷新时间
  localStorage.setItem(SESSION_LAST_REFRESH_KEY, Date.now().toString());
  
  console.log('SessionId已保存，长度:', sessionId.length);
}

/**
 * 清除SessionId
 */
export function clearSessionId() {
  localStorage.removeItem(SESSION_ID_KEY);
  localStorage.removeItem(SESSION_LAST_REFRESH_KEY);
  console.log('SessionId已清除');
}

/**
 * 检查SessionId是否有效
 * @returns {boolean} SessionId是否有效
 */
export function isSessionValid() {
  const sessionId = getSessionId();
  const lastRefreshStr = localStorage.getItem(SESSION_LAST_REFRESH_KEY);
  
  if (!sessionId || !lastRefreshStr) {
    return false;
  }
  
  // 检查SessionId是否过期
  const lastRefresh = parseInt(lastRefreshStr, 10);
  const now = Date.now();
  const age = now - lastRefresh;
  
  // 如果SessionId年龄超过过期时间，则认为无效
  return age < SESSION_EXPIRY;
}

/**
 * 刷新SessionId
 * @param {boolean} force 是否强制刷新，即使当前SessionId尚未过期
 * @returns {Promise<string>} 包含新SessionId的Promise
 */
export function refreshSessionId(force = false) {
  // 检查是否需要刷新
  if (!force && isSessionValid()) {
    return Promise.resolve(getSessionId());
  }
  
  // 如果已经在刷新，则返回等待Promise
  if (isRefreshingSession) {
    return new Promise((resolve, reject) => {
      pendingRequests.push({ resolve, reject });
    });
  }
  
  console.log('开始刷新SessionId...');
  isRefreshingSession = true;
  
  // 调用登录API获取新的SessionId
  return apiLogin()
    .then(sessionId => {
      console.log('SessionId刷新成功');
      saveSessionId(sessionId);
      
      isRefreshingSession = false;
      
      // 处理等待队列中的请求
      pendingRequests.forEach(request => {
        request.resolve(sessionId);
      });
      pendingRequests = [];
      
      return sessionId;
    })
    .catch(error => {
      console.error('SessionId刷新失败:', error.message);
      
      isRefreshingSession = false;
      
      // 处理等待队列中的请求
      pendingRequests.forEach(request => {
        request.reject(error);
      });
      pendingRequests = [];
      
      return Promise.reject(error);
    });
}

/**
 * 确保有有效的SessionId
 * 如果SessionId不存在或已过期，则会自动刷新
 * @returns {Promise<string>} 包含有效SessionId的Promise
 */
export function ensureValidSession() {
  if (isSessionValid()) {
    return Promise.resolve(getSessionId());
  }
  
  return refreshSessionId(true);
}

/**
 * 获取SessionId的状态信息
 * @returns {Object} SessionId的状态信息
 */
export function getSessionStatus() {
  const sessionId = getSessionId();
  const lastRefreshStr = localStorage.getItem(SESSION_LAST_REFRESH_KEY);
  
  if (!sessionId || !lastRefreshStr) {
    return {
      exists: false,
      valid: false,
      age: null,
      expiresIn: null
    };
  }
  
  const lastRefresh = parseInt(lastRefreshStr, 10);
  const now = Date.now();
  const age = now - lastRefresh;
  const expiresIn = Math.max(0, SESSION_EXPIRY - age);
  
  return {
    exists: true,
    valid: age < SESSION_EXPIRY,
    age: age,
    expiresIn: expiresIn,
    ageFormatted: formatDuration(age),
    expiresInFormatted: formatDuration(expiresIn)
  };
}

/**
 * 格式化持续时间（毫秒）为人类可读的格式
 * @param {number} ms 毫秒数
 * @returns {string} 格式化后的字符串
 */
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`;
  } else {
    return `${seconds}秒`;
  }
} 