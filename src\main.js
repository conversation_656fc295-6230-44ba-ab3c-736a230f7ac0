import Vue from 'vue'
import App from './App.vue'
import router from './router'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './assets/kingdee-theme.scss';
import axios from 'axios'
import './api/config'

// 引入Vant UI
import Vant from 'vant';
import 'vant/lib/index.css';

// 引入轮播图组件
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/css/swiper.css';

// 引入Vuex
import store from './store';

// 引入手势库
import VueTouch from 'vue-touch';

// 引入会话管理工具
import { ensureValidSession, getSessionStatus } from './utils/session';

// 使用组件
Vue.use(Vant);
Vue.use(VueAwesomeSwiper);
Vue.use(VueTouch, { name: 'v-touch' });

Vue.prototype.axios = axios;
Vue.use(ElementUI);
Vue.config.productionTip = false

// 应用初始化时检查SessionId
console.log('应用程序启动，检查金蝶SessionId状态...');
const sessionStatus = getSessionStatus();
if (sessionStatus.exists) {
  console.log(`金蝶SessionId状态: ${sessionStatus.valid ? '有效' : '已过期'}`);
  if (sessionStatus.valid) {
    console.log(`SessionId剩余有效期: ${sessionStatus.expiresInFormatted}`);
  } else {
    console.log('SessionId已过期，将在必要时自动刷新');
  }
} else {
  console.log('未找到金蝶SessionId，将在必要时自动获取');
}

// 预先尝试获取有效的SessionId，但不阻塞应用启动
ensureValidSession()
  .then(sessionId => {
    console.log('已成功获取/验证金蝶SessionId，长度:', sessionId.length);
  })
  .catch(error => {
    console.warn('初始获取金蝶SessionId失败:', error.message);
    console.log('应用程序将在需要时重试获取');
  });

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app')
