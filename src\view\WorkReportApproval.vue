<template>
  <div class="work-report-container">
    <!-- 顶部标签页导航 -->
    <div class="tab-nav">
      <back-button />
      <div class="tab-item" @click="goToReport">汇报</div>
      <div class="tab-item active">审批</div>
      <div class="tab-item" @click="goToStatistics">统计</div>
    </div>

    <!-- 审批列表区域 -->
    <div class="approval-container">
      <!-- 筛选条件 -->
      <div class="filter-bar">
        <div class="filter-item">
          <span class="filter-label">时间范围</span>
          <div class="date-range" @click="showDateRangePicker = true">
            {{ dateRange }}
            <van-icon name="arrow-down" />
          </div>
          <van-popup
            v-model="showDateRangePicker"
            position="bottom"
            :style="{ height: '40%' }"
          >
            <van-picker
              :columns="dateRangeOptions"
              @confirm="onDateRangeOptionConfirm"
              @cancel="showDateRangePicker = false"
              show-toolbar
              title="选择时间范围"
              :cancel-button-text="'取消'"
              :confirm-button-text="'确认'"
            />
          </van-popup>
        </div>
        <div class="filter-item">
          <span class="filter-label">审核状态</span>
          <div class="status-select" @click="showStatusPicker = true">
            {{ statusFilterText }}
            <van-icon name="arrow-down" />
          </div>
          <van-popup
            v-model="showStatusPicker"
            position="bottom"
            :style="{ height: '40%' }"
          >
            <van-picker
              :columns="statusOptions"
              @confirm="onStatusConfirm"
              @cancel="showStatusPicker = false"
              show-toolbar
              title="选择审核状态"
              :cancel-button-text="'取消'"
              :confirm-button-text="'确认'"
            />
          </van-popup>
        </div>
        <van-button type="primary" size="small" class="filter-btn" @click="applyFilter">查询</van-button>
      </div>

      <!-- 批量操作按钮 -->
      <div class="batch-actions" v-if="approvalItems.length > 0">
        <div class="select-all">
          <van-checkbox v-model="isAllSelected" @click="toggleSelectAll">全选</van-checkbox>
          <span class="selected-count" v-if="selectedConfirmedItems.length > 0">{{ selectedConfirmedItems.length }} 项</span>
        </div>
        <div class="action-buttons" v-if="selectedConfirmedItems.length > 0">
          <van-button type="warning" size="small" @click="batchAudit">确认</van-button>
          <van-button type="danger" size="small" @click="batchReject" style="margin-left: 10px;">驳回</van-button>
        </div>
      </div>

      <!-- 审批列表 -->
      <div class="approval-list" v-if="approvalItems.length > 0">
        <div class="approval-item" v-for="item in approvalItems" :key="item.id">
          <div class="item-header">
            <van-checkbox v-model="item.isSelected" v-if="item.status === 'pending' || item.status === 'confirmed'" />
            <div class="project-task-info" @click="toggleExpand(item)">
              <div class="project-title">
                {{ item.project }}
                <van-icon :name="item.expanded ? 'arrow-up' : 'arrow-down'" class="expand-icon" />
              </div>
              <div class="task-subtitle" v-if="item.taskName">{{ item.taskName }}</div>
            </div>
            <div class="user-status-info">
            <div class="submitter">{{ item.submitter }}</div>
            <div class="status" :class="item.status">{{ getStatusText(item.status) }}</div>
            </div>
          </div>
          
          <div class="item-body" v-show="item.expanded">
            <div class="basic-info">
              
              <!-- 日期信息行 -->
              <div class="info-row">
                <div class="info-item">
                  <span class="label">汇报日期:</span>
                  <span class="value">{{ item.workDate || '未填写' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">提交日期:</span>
                  <span class="value">{{ item.submitDate || '未提交' }}</span>
                </div>
              </div>
              
              <!-- 第三行：工时信息标签组 -->
              <div class="hours-tags">
                <!-- 第一行：计划、已报、岗位 -->
                <div class="hours-row">
                  <div class="hours-tag">
                    <span class="tag-label">计划：</span>
                    <span class="tag-value">{{ item.planHours || 0 }}h</span>
                  </div>
                  <div class="hours-tag">
                    <span class="tag-label">已报：</span>
                    <span class="tag-value">
                      <span v-if="reportedHoursLoading && item.cumulativeHours === undefined">加载中...</span>
                      <span v-else>{{ item.cumulativeHours || 0 }}h</span>
                    </span>
                  </div>
                  <div class="hours-tag">
                    <span class="tag-label">岗位：</span>
                    <span class="tag-value">{{ item.positionName || item.position || '未设置' }}</span>
                  </div>
                </div>
                
                <!-- 第二行：汇报、加班、进度 -->
                <div class="hours-row">
                  <div class="hours-tag">
                    <span class="tag-label">汇报：</span>
                    <span class="tag-value">{{ item.hours || 0 }}h</span>
                  </div>
                  <div class="hours-tag">
                    <span class="tag-label">加班：</span>
                    <span class="tag-value">{{ item.overtimeHours || 0 }}h</span>
                  </div>
                  <div class="hours-tag">
                    <span class="tag-label">进度：</span>
                    <span class="tag-value">{{ item.progress || 0 }}%</span>
                  </div>
                </div>
                
                <!-- 第三行：确认工时 -->
                <div class="hours-row">
                  <!-- 确认工时字段单独一行 -->
                  <div class="hours-tag pending-confirm" v-if="item.status === 'pending'">
                    <span class="tag-label">确认工时：</span>
                    <input 
                      type="text" 
                      v-model="item.editingHours" 
                      @input="validateInlineHoursInput(item)"
                      @blur="saveConfirmedHours(item)"
                      @keyup.enter="saveConfirmedHours(item)"
                      class="hours-input"
                      :placeholder="(item.confirmTime || item.hours || 0) "
                    />
                    <span class="tag-unit">h</span>
                  </div>
                  <div class="hours-tag primary" v-else-if="item.confirmTime">
                    <span class="tag-label">确认工时：</span>
                    <span class="tag-value">{{ item.confirmTime }}h</span>
                  </div>
                  <div class="hours-tag" v-else>
                    <span class="tag-label">确认工时：</span>
                    <span class="tag-value">{{ item.confirmedHours || 0 }}h</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="detail-info">
              <!-- 临时任务类型显示 -->
              <div class="item-row" v-if="item.taskType === 'temp' && item.tmpTaskTypeName">
                <span class="label">临时任务类型：</span>
                <span class="value task-type temp">{{ item.tmpTaskTypeName }}</span>
              </div>
              <!-- 耗费类型显示 -->
              <div class="item-row" v-if="item.taskType === 'timecost' && item.burnOffTypeName">
                <span class="label">耗费类型：</span>
                <span class="value task-type timecost">{{ item.burnOffTypeName }}</span>
              </div>
              
              <!-- 工作内容显示 -->
              <div class="work-content" v-if="item.content">
                <div class="content-body">
                  {{ item.content }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 待审批状态显示审核通过和驳回按钮 -->
          <div class="item-footer" v-if="item.status === 'pending'">
            <van-button type="primary" size="small" @click="auditApproval(item)">确认</van-button>
            <van-button type="danger" size="small" @click="rejectApproval(item)" style="margin-left: 10px;">驳回</van-button>
          </div>
          
          <!-- 已确认工时的项目可以进行审核通过 -->
          <div class="item-footer" v-if="item.status === 'confirmed'">
            <van-button type="warning" size="small" @click="auditApproval(item)">确认</van-button>
            <van-button type="danger" size="small" @click="rejectApproval(item)" style="margin-left: 10px;">驳回</van-button>
          </div>
          
          <!-- 已审核状态不显示任何操作按钮 -->
        </div>
      </div>

      <!-- 空状态 -->
      <div class="no-data" v-else>
        <van-empty image="search" description="没有待审批的工时记录" />
      </div>
    </div>

    <!-- 审批意见弹窗 -->
    <van-dialog
      v-model="showCommentDialog"
      :title="commentDialogTitle"
      show-cancel-button
      @confirm="handleCommentConfirm"
      @cancel="handleCommentCancel"
      class="kingdee-dialog"
      :confirm-button-color="kingdeeBlue"
    >
      <div class="comment-dialog-content">
        <!-- 当操作为确认工时时显示工时信息和输入框 -->
        <div class="hours-info" v-if="currentAction === 'confirm'">
          <div class="hours-row">
            <span class="hours-label">提交人：</span>
            <span class="hours-value">{{ currentItem ? currentItem.submitter : '' }}</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">项目：</span>
            <span class="hours-value">{{ currentItem ? currentItem.project : '' }}</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">任务名称：</span>
            <span class="hours-value">{{ currentItem ? currentItem.taskName : '' }}</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">计划工时：</span>
            <span class="hours-value">{{ currentItem ? currentItem.planHours : 0 }}小时</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">已报工时：</span>
            <span class="hours-value">
              <span v-if="reportedHoursLoading && currentItem && currentItem.cumulativeHours === undefined">加载中...</span>
              <span v-else>{{ currentItem ? currentItem.cumulativeHours : 0 }}小时</span>
            </span>
          </div>
          <div class="hours-row">
            <span class="hours-label">提交工时：</span>
            <span class="hours-value">{{ currentItem ? currentItem.hours : 0 }}小时</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">加班工时：</span>
            <span class="hours-value">{{ currentItem ? currentItem.overtimeHours : 0 }}小时</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">完成进度：</span>
            <span class="hours-value">{{ currentItem ? currentItem.progress : 0 }}%</span>
          </div>
          <div class="hours-row">
            <span class="hours-label">确认工时：</span>
            <span class="hours-value">{{ currentItem ? currentItem.confirmTime ? currentItem.confirmTime + '小时' : '0小时' : '' }}</span>
          </div>
        </div>
        
        <!-- 确认工时输入框，仅在确认工时操作时显示 -->
        <div class="hours-input" v-if="currentAction === 'confirm'">
          <van-field
            v-model="confirmedHours"
            label="确认工时"
            type="text"
            placeholder="请输入确认的工时（支持小数）"
            :rules="[{ required: true, message: '请输入确认的工时' }]"
            @input="validateHoursInput"
            class="kingdee-field"
          >
            <template #button>
              <span class="hours-unit">小时</span>
            </template>
          </van-field>
          
          <!-- 同时审核通过选项 -->
          <div class="audit-option" style="margin-top: 12px;">
            <van-checkbox v-model="autoAuditAfterConfirm">
              确认工时后同时审核通过
            </van-checkbox>
          </div>
        </div>
        
        <van-field
          v-model="approvalComment"
          type="textarea"
          placeholder="请输入审批意见"
          rows="3"
          autosize
          :rules="[{ required: true, message: '请输入审批意见' }]"
          class="kingdee-field"
          v-if="currentAction !== 'confirm'"
        />
        <div class="common-comments" v-if="showCommonComments && currentAction !== 'confirm'">
          <div class="common-comments-title">常用意见</div>
          <div class="common-comments-list">
            <van-tag
              v-for="comment in commonComments"
              :key="comment"
              class="comment-tag"
              :class="{ active: approvalComment === comment }"
              @click="selectCommonComment(comment)"
            >
              {{ comment }}
            </van-tag>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import BackButton from '../components/BackButton.vue';
import { 
  getApprovalData, 
  confirmHours, 
  workflowAudit,
  rejectApproval,
  formatApprovalData,
  getAuditedData,
  formatAuditedData
} from '../api/approval';
import { getUserDueProjectTasks } from '../api/report';

export default {
  name: 'WorkReportApproval',
  components: {
    BackButton
  },
  data() {
    return {
      // 金蝶企业蓝
      kingdeeBlue: '#276ff5',
      
      // 日期筛选
      startDate: null,
      endDate: null,
      
      // 新增：时间范围下拉选择相关
      showDateRangePicker: false,
      dateRangeFilterText: '本周',
      dateRangeOptions: [
        '全部时间',
        '本周', 
        '上周',
        '本月',
        '上月'
      ],
      
      // 多选相关
      isSelectAll: false, // 用于控制全选的状态
      
      // 审批意见相关
      showCommentDialog: false,
      commentDialogTitle: '',
      approvalComment: '',
      currentAction: '', // 'approve' 或 'reject' 或 'confirm' 或 'audit'
      currentItem: null,
      showCommonComments: true,
      commonComments: [
        '驳回，请补充工作内容',
        '驳回，工时需要调整',
        '驳回，请完善项目信息',
      ],
      
      // 确认工时相关
      confirmedHours: '',
      autoAuditAfterConfirm: false, // 确认工时后同时审核通过
      
      // 审批列表数据
      approvalItems: [],
      
      // 加载状态
      loading: false,
      
      // 当前用户信息
      userInfo: null,
      
      // 新增：导航相关
      navigating: false,
      
      // 新增：状态筛选相关
      showStatusPicker: false,
      statusFilterText: '待审批',
      statusOptions: [
        '待审批', 
        '已审批'
      ],
      
      // 新增：已审核数据缓存相关
      auditedDataCache: {
        data: [], // 缓存的已审核数据
        lastUpdate: null, // 最后更新时间
        cacheKey: '', // 缓存键，包含用户和日期信息
        expireTime: 5 * 60 * 1000 // 缓存5分钟过期
      },
      
      // 新增：已报工时数据缓存
      reportedHoursCache: {},
      reportedHoursLoading: false
    };
  },
  created() {
    // 组件创建时获取用户信息
    this.getUserInfo();
    // 初始化本周日期范围
    this.initThisWeekDateRange();
    // 加载待审批数据
    this.loadApprovalData();
  },
  beforeDestroy() {
    // 组件销毁前清理状态
    this.navigating = false;
    // 清除所有弹窗状态
    this.showCommentDialog = false;
    this.$toast.clear();
    // 清理已审核数据缓存（可选：只清理内存缓存，保留LocalStorage缓存）
    this.auditedDataCache.data = [];
    this.auditedDataCache.lastUpdate = null;
    this.auditedDataCache.cacheKey = '';
    // 清理已报工时缓存
    this.clearReportedHoursCache();
  },
  computed: {
    dateRange() {
      // 如果使用预设的时间范围选项，显示选项文本加简洁的日期范围
      if (this.dateRangeFilterText && this.dateRangeFilterText !== '全部时间') {
        if (this.startDate && this.endDate) {
          const startStr = this.formatDate(this.startDate);
          const endStr = this.formatDate(this.endDate);
          // 如果是同一个月，只显示月日
          if (startStr.substring(0, 7) === endStr.substring(0, 7)) {
            return `${this.dateRangeFilterText} ${startStr.substring(5)} ~ ${endStr.substring(5)}`;
          }
          return `${this.dateRangeFilterText} ${startStr.substring(5)} ~ ${endStr.substring(5)}`;
        }
        return this.dateRangeFilterText;
      }
      
      // 如果是"全部时间"或没有设置日期范围
      if (!this.startDate && !this.endDate) {
        return '全部时间';
      }
      
      // 如果有具体的日期范围，显示格式化的日期
      if (this.startDate && this.endDate) {
        return `${this.formatDate(this.startDate)} 至 ${this.formatDate(this.endDate)}`;
      }
      
      return this.startDate ? this.formatDate(this.startDate) : this.formatDate(this.endDate);
    },
    selectedItems() {
      return this.approvalItems
        .filter(item => item.isSelected && (item.status === 'pending' || item.status === 'confirmed'))
        .map(item => item.id);
    },
    selectedConfirmedItems() {
      return this.approvalItems
        .filter(item => item.isSelected && (item.status === 'pending' || item.status === 'confirmed'))
        .map(item => item.id);
    },
    isAllSelected: {
      get() {
        // 检查所有状态为pending或confirmed的项是否都被选中
        const selectableItems = this.approvalItems.filter(item => item.status === 'pending' || item.status === 'confirmed');
        return selectableItems.length > 0 && selectableItems.every(item => item.isSelected);
      },
      set(value) {
        this.isSelectAll = value;
        this.toggleSelectAll();
      }
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      try {
        const userInfoStr = localStorage.getItem('work_report_user');
        if (userInfoStr) {
          this.userInfo = JSON.parse(userInfoStr);
          console.log('当前用户信息:', this.userInfo);
        } else {
          console.warn('未找到用户信息，请先登录');
          this.$toast('请先登录');
          this.$router.push('/login');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        this.$toast.fail('获取用户信息失败');
      }
    },
    
    // 加载审批数据
    loadApprovalData() {
      if (!this.userInfo || !this.userInfo.username || !this.userInfo.userEntityId) {
        this.$toast('用户信息不完整，请重新登录');
        return;
      }
      
      this.loading = true;
      this.$toast.loading({
        message: '加载中...',
        forbidClick: true
      });
      
      // 构建查询参数
      const params = {
        username: this.userInfo.username,
        userEntityId: this.userInfo.userEntityId
      };
      
      // 添加日期筛选参数
      if (this.startDate) {
        params.startDate = this.formatDate(this.startDate);
      }
      if (this.endDate) {
        params.endDate = this.formatDate(this.endDate);
      }
      
      // 添加调试信息
      console.log('[WorkReportApproval] 加载审批数据 - 请求参数:', {
        params,
        statusFilterText: this.statusFilterText,
        dateRange: this.dateRange,
        startDate: this.startDate,
        endDate: this.endDate
      });
      
      // 根据状态筛选条件选择不同的API调用
      if (this.statusFilterText === '已审批') {
        // 直接调用已审核数据API，不使用缓存以确保数据实时更新
        getAuditedData(params)
          .then(result => {
            this.loading = false;
            this.$toast.clear();
            
            console.log('[WorkReportApproval] 已审核数据API返回结果:', result);
            
            if (result.success) {
              // 打印API原始返回数据的详细信息
              console.log('========== 已审核数据详细信息 ==========');
              console.log('API原始返回数据:', result.data);
              console.log('原始数据类型:', typeof result.data);
              console.log('原始数据是否为数组:', Array.isArray(result.data));
              if (Array.isArray(result.data)) {
                console.log('原始数据条数:', result.data.length);
                if (result.data.length > 0) {
                  console.log('第一条原始数据示例:', result.data[0]);
                  console.log('原始数据字段结构:', Object.keys(result.data[0] || {}));
                }
              }
              
              // 转换API返回的已审核数据为组件需要的格式
              this.approvalItems = formatAuditedData(result.data);
              
              // 设置所有卡片默认展开
              this.approvalItems.forEach(item => {
                this.$set(item, 'expanded', true);
              });
              
              console.log('========== 格式化后的已审核数据 ==========');
              console.log('[WorkReportApproval] 格式化后的已审核数据:', this.approvalItems);
              console.log('[WorkReportApproval] 已审核数据数量:', this.approvalItems.length);
              
              // 新增：加载已报工时数据
              this.loadReportedHoursForItems(this.approvalItems);
            } else {
              console.warn('获取已审核数据失败:', result.message);
              this.$toast.fail(`获取已审核数据失败: ${result.message}`);
            }
          })
          .catch(error => {
            this.loading = false;
            this.$toast.clear();
            console.error('获取已审核数据错误:', error);
            this.$toast.fail(`加载已审核数据失败: ${error.message}`);
          });
      } else {
        // 调用原有的审批数据API（未审核和全部状态）
        // 根据状态筛选条件设置status参数
        if (this.statusFilterText === '待审批') {
          params.status = 'B'; // 只查询待审批状态
        } else {
          params.status = 'B,C'; // 查询所有状态（全部）
        }
        
        getApprovalData(params)
          .then(result => {
            this.loading = false;
            this.$toast.clear();
            
            console.log('[WorkReportApproval] 审批数据API返回结果:', result);
            
            if (result.success) {
              // 转换API返回的数据为组件需要的格式
              this.approvalItems = formatApprovalData(result.data);

              // 排序：未审核（pending/confirmed）在前，已审核/已驳回在后，同组内按 submitDate/workDate 倒序
              this.approvalItems.sort((a, b) => {
                // 1. 未审核优先
                const statusOrder = (status) => {
                  if (status === 'pending' || status === 'confirmed') return 0;
                  if (status === 'rejected') return 2;
                  return 1; // 其他如已审核
                };
                const soA = statusOrder(a.status);
                const soB = statusOrder(b.status);
                if (soA !== soB) return soA - soB;
                // 2. 同组内按 submitDate/workDate 倒序
                const dateA = new Date(a.submitDate || a.workDate || 0).getTime();
                const dateB = new Date(b.submitDate || b.workDate || 0).getTime();
                return dateB - dateA;
              });

              // 初始化编辑状态
              this.approvalItems.forEach(item => {
                if (item.status === 'pending') {
                  this.$set(item, 'editingHours', String(item.confirmTime || item.hours || 0));
                }
                // 设置所有卡片默认展开
                this.$set(item, 'expanded', true);
              });
              
              console.log('[WorkReportApproval] 格式化后的审批数据:', this.approvalItems);
              console.log('[WorkReportApproval] 审批数据数量:', this.approvalItems.length);
              
              // 新增：加载已报工时数据
              this.loadReportedHoursForItems(this.approvalItems);
            } else {
              console.warn('获取审批数据失败:', result.message);
              this.$toast.fail(`获取数据失败: ${result.message}`);
            }
          })
          .catch(error => {
            this.loading = false;
            this.$toast.clear();
            console.error('获取审批数据错误:', error);
            this.$toast.fail(`加载失败: ${error.message}`);
          });
      }
    },
    
    goToReport() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/report') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/report')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到汇报页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    goToStatistics() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/statistics') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/statistics')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到统计页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    formatDate(date) {
      if (!date) return '';
      
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },
    onDateRangeOptionConfirm(value) {
      this.dateRangeFilterText = value;
      this.showDateRangePicker = false;
      
      // 根据选择的选项计算日期范围
      switch (value) {
        case '本周': {
          const thisWeek = this.getThisWeekRange();
          this.startDate = thisWeek.startDate;
          this.endDate = thisWeek.endDate;
          break;
        }
        case '上周': {
          const lastWeek = this.getLastWeekRange();
          this.startDate = lastWeek.startDate;
          this.endDate = lastWeek.endDate;
          break;
        }
        case '本月': {
          const thisMonth = this.getThisMonthRange();
          this.startDate = thisMonth.startDate;
          this.endDate = thisMonth.endDate;
          break;
        }
        case '上月': {
          const lastMonth = this.getLastMonthRange();
          this.startDate = lastMonth.startDate;
          this.endDate = lastMonth.endDate;
          break;
        }
        case '全部时间':
        default:
          this.startDate = null;
          this.endDate = null;
          break;
      }
      
      console.log(`[WorkReportApproval] 时间范围选择: ${value}`, {
        startDate: this.startDate ? this.formatDate(this.startDate) : null,
        endDate: this.endDate ? this.formatDate(this.endDate) : null,
        currentStatus: this.statusFilterText
      });
      
      // 如果是已审核状态，时间范围变化后直接重新加载数据（不再使用缓存）
      if (this.statusFilterText === '已审批') {
        console.log('[WorkReportApproval] 检测到已审核状态下的时间范围变化，重新加载数据');
        this.loadApprovalData();
      }
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已审批',
        'rejected': '已驳回',
        'confirmed': '已审批',
        'audited': '已审批'
      };
      return statusMap[status] || status;
    },
    applyFilter() {
      // 应用日期筛选，重新加载数据
      this.$toast('应用日期筛选');
      console.log('[WorkReportApproval] 手动应用筛选 - 筛选条件:', {
        dateRange: this.dateRange,
        startDate: this.startDate ? this.formatDate(this.startDate) : null,
        endDate: this.endDate ? this.formatDate(this.endDate) : null,
        statusFilterText: this.statusFilterText
      });
      
      // 重新加载数据（已审核状态下不再使用缓存）
      this.loadApprovalData();
    },
    // 多选相关方法
    toggleSelectAll() {
      this.approvalItems.forEach(item => {
        if (item.status === 'pending' || item.status === 'confirmed') {
          item.isSelected = this.isSelectAll;
        }
      });
    },
    // 批量操作相关方法
    batchAudit() {
      if (this.selectedConfirmedItems.length === 0) {
        this.$toast('请选择要审核的项目');
        return;
      }
      // 直接批量审核通过，不弹窗
      this.processWorkflowAudit(this.selectedConfirmedItems, '批量审核通过');
    },
    // 单个审批相关方法
    auditApproval(item) {
      // 直接审核通过，不弹窗
      this.processWorkflowAudit([item.id], '审核通过');
    },
    rejectApproval(item) {
      // 弹出审批意见弹窗
      this.currentAction = 'reject';
      this.currentItem = item;
      this.commentDialogTitle = '驳回审批';
      this.approvalComment = '';
      this.showCommentDialog = true;
    },
    batchReject() {
      if (this.selectedConfirmedItems.length === 0) {
        this.$toast('请选择要驳回的项目');
        return;
      }
      
      // 弹出批量驳回审批意见弹窗
      this.currentAction = 'batchReject';
      this.currentItem = null;
      this.commentDialogTitle = '批量驳回审批';
      this.approvalComment = '';
      this.showCommentDialog = true;
    },
    processRejectApproval(id, comment) {
      // 调用API进行单个驳回
      this.$toast.loading({
        message: '处理中...',
        forbidClick: true
      });
      
      // 获取用户信息
      if (!this.userInfo || !this.userInfo.userEntityId) {
        this.$toast.fail('用户信息不完整，请重新登录');
        return;
      }
      
      // 准备参数 - 使用新的WorkflowAudit接口格式
      const params = {
        numbers: [id],
        userId: this.userInfo.userEntityId,
        disposition: comment || "不同意"
      };
      
      console.log('[WorkReportApproval] 驳回 - 参数:', params);
      
      // 调用API
      rejectApproval(params)
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            // 更新本地状态
            const index = this.approvalItems.findIndex(item => item.id === id);
            if (index !== -1) {
              this.approvalItems[index].status = 'rejected';
              this.approvalItems[index].comment = comment;
            }
            
            this.$toast.success('驳回成功');
            console.log('[WorkReportApproval] 驳回 - API响应JSON:', result.data.response);
            // 刷新数据以确保状态同步
            this.loadApprovalData();
          } else {
            this.$toast.fail(`驳回失败: ${result.message}`);
            console.error('[WorkReportApproval] 驳回 - API失败响应:', result.data);
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('驳回错误:', error);
          this.$toast.fail(`操作失败: ${error.message}`);
        });
    },
    processBatchReject(numbers, comment) {
      // 调用新的WorkflowAudit接口进行批量驳回
      this.$toast.loading({
        message: '批量驳回中...',
        forbidClick: true
      });
      
      // 获取用户信息
      if (!this.userInfo || !this.userInfo.userEntityId) {
        this.$toast.fail('用户信息不完整，请重新登录');
        return;
      }
      
      console.log('[WorkReportApproval] 批量驳回 - 项目数量:', numbers.length);
      
      // 准备参数 - 使用新的WorkflowAudit接口格式
      const params = {
        numbers: numbers,
        userId: this.userInfo.userEntityId,
        disposition: comment || "不同意"
      };
      
      console.log('[WorkReportApproval] 批量驳回 - 参数:', params);
      
      // 调用批量驳回API
      rejectApproval(params)
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            // 更新本地状态
            numbers.forEach(number => {
              const index = this.approvalItems.findIndex(item => item.id === number);
              if (index !== -1) {
                this.approvalItems[index].status = 'rejected';
                this.approvalItems[index].comment = comment;
                this.approvalItems[index].isSelected = false; // 取消选中
              }
            });
            
            this.$toast.success(`批量驳回成功：${numbers.length}项`);
            console.log('[WorkReportApproval] 批量驳回 - API响应JSON:', result.data.response);
            // 刷新数据以确保状态同步
            this.loadApprovalData();
          } else {
            this.$toast.fail(`批量驳回失败: ${result.message}`);
            console.error('[WorkReportApproval] 批量驳回 - API失败响应:', result.data);
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('批量驳回错误:', error);
          this.$toast.fail(`操作失败: ${error.message}`);
        });
    },
    validateInlineHoursInput(item) {
      // 只允许输入数字和一个小数点
      if (!/^[0-9]*\.?[0-9]*$/.test(item.editingHours)) {
        // 找到最后一个合法值
        let lastValidValue = '';
        for (let i = 0; i < item.editingHours.length; i++) {
          const char = item.editingHours[i];
          if (/[0-9]/.test(char) || (char === '.' && lastValidValue.indexOf('.') === -1)) {
            lastValidValue += char;
          }
        }
        this.$set(item, 'editingHours', lastValidValue);
      }
    },
    saveConfirmedHours(item) {
      if (!item.editingHours || !item.editingHours.trim()) {
        // 如果输入为空，恢复原值
        this.$set(item, 'editingHours', String(item.confirmTime || item.hours || 0));
        return;
      }
      
      const hours = parseFloat(item.editingHours);
      if (isNaN(hours) || hours <= 0) {
        this.$toast('请输入有效的工时数值（大于0的数字）');
        this.$set(item, 'editingHours', String(item.confirmTime || item.hours || 0));
        return;
      }
      
      // 如果值没有变化，不需要保存
      if (hours === (item.confirmTime || item.hours)) {
        return;
      }
      
      // 调用确认工时API
      this.$toast.loading({
        message: '保存中...',
        forbidClick: true
      });
      
      const params = {
        id: item.id,
        hours: hours,
        comment: '工时已确认',
        fid: item.fid
      };
      
      confirmHours(params)
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            // 更新本地状态
            item.status = 'confirmed';
            item.confirmTime = hours;
            item.confirmedHours = hours;
            item.comment = '工时已确认';
            
            this.$toast.success('工时确认成功');
            
            // 确认工时成功后，直接进行审核通过
            this.$toast.loading({
              message: '审核中...',
              forbidClick: true
            });
            
            // 调用审核通过API
            this.processWorkflowAudit([item.id], '工时已确认，审核通过');
          } else {
            this.$toast.fail(`确认工时失败: ${result.message}`);
            // 恢复原值
            this.$set(item, 'editingHours', String(item.confirmTime || item.hours || 0));
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('确认工时错误:', error);
          this.$toast.fail(`操作失败: ${error.message}`);
          // 恢复原值
          this.$set(item, 'editingHours', String(item.confirmTime || item.hours || 0));
        });
    },
    handleCommentConfirm() {
      // 确认工时操作的特殊处理
      if (this.currentAction === 'confirm') {
        if (!this.confirmedHours.trim()) {
          this.$toast('请输入确认的工时');
          return;
        }
        
        const hours = parseFloat(this.confirmedHours);
        if (isNaN(hours) || hours <= 0) {
          this.$toast('请输入有效的工时数值（大于0的数字，支持小数）');
          return;
        }
        
        // 直接进行工时确认，不再弹审批通过窗口
        this.processHoursConfirmation(this.currentItem.id, hours);
        this.resetApprovalState();
        return;
      }

      if (!this.approvalComment.trim()) {
        this.$toast('请输入审批意见');
        return;
      }
      
      if (this.currentAction === 'audit') {
        if (this.currentItem) {
          // 单个审核通过
          this.processWorkflowAudit([this.currentItem.id], this.approvalComment);
        } else {
          // 批量审核通过
          this.processWorkflowAudit(this.selectedConfirmedItems, this.approvalComment);
        }
      } else if (this.currentAction === 'reject') {
        // 处理驳回操作
        this.processRejectApproval(this.currentItem.id, this.approvalComment);
      } else if (this.currentAction === 'batchReject') {
        // 处理批量驳回操作
        this.processBatchReject(this.selectedConfirmedItems, this.approvalComment);
      }

      // 重置状态
      this.resetApprovalState();
    },
    handleCommentCancel() {
      this.resetApprovalState();
    },
    processHoursConfirmation(id, hours) {
      // 调用API进行工时确认
      this.$toast.loading({
        message: '处理中...',
        forbidClick: true
      });
      
      // 查找当前工时报告项目，获取FID
      const currentItem = this.approvalItems.find(item => item.id === id);
      
      if (!currentItem || !currentItem.fid) {
        this.$toast.fail('无法获取工时报告FID，请刷新页面后重试');
        console.error('[WorkReportApproval] 确认工时失败 - 无法获取工时报告FID:', { id, currentItem });
        return;
      }
      
      // 准备参数，包含FID
      const params = {
        id: id,
        hours: hours,
        comment: '工时已确认',  // 固定的确认信息
        fid: currentItem.fid
      };
      
      console.log('[WorkReportApproval] 确认工时 - 参数:', params);
      
      // 调用API
      confirmHours(params)
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            // 更新本地状态
            const index = this.approvalItems.findIndex(item => item.id === id);
            if (index !== -1) {
              this.approvalItems[index].status = 'confirmed';
              this.approvalItems[index].confirmedHours = hours;
              this.approvalItems[index].comment = '工时已确认';
            }
            
            this.$toast.success('工时确认成功');
            // 打印返回的JSON数据
            console.log('[WorkReportApproval] 确认工时 - API响应JSON:', result.data.response);
          } else {
            this.$toast.fail(`确认工时失败: ${result.message}`);
             // 也打印失败时的响应数据
            console.error('[WorkReportApproval] 确认工时 - API失败响应:', result.data);
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('确认工时错误:', error);
          this.$toast.fail(`操作失败: ${error.message}`);
        });
    },
    processWorkflowAudit(numbers, disposition) {
      // 调用工作流审核通过API
      this.$toast.loading({
        message: '审核中...',
        forbidClick: true
      });
      
      // 获取用户信息
      if (!this.userInfo || !this.userInfo.userEntityId || !this.userInfo.userName) {
        this.$toast.fail('用户信息不完整，请重新登录');
        return;
      }
      
      // 准备参数
      const params = {
        numbers: numbers,
        userId: this.userInfo.userEntityId,
        userName: this.userInfo.userName,
        disposition: disposition
      };
      
      console.log('[WorkReportApproval] 审核通过 - 参数:', params);
      
      // 调用API
      workflowAudit(params)
        .then(result => {
          this.$toast.clear();
          
          if (result.success) {
            // 更新本地状态
            numbers.forEach(number => {
              const index = this.approvalItems.findIndex(item => item.id === number);
              if (index !== -1) {
                this.approvalItems[index].status = 'audited';
                this.approvalItems[index].comment = disposition;
              }
            });
            
            this.$toast.success('审核通过成功');
            console.log('[WorkReportApproval] 审核通过 - API响应JSON:', result.data.response);
          } else {
            this.$toast.fail(`审核通过失败: ${result.message}`);
            console.error('[WorkReportApproval] 审核通过 - API失败响应:', result.data);
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('审核通过错误:', error);
          this.$toast.fail(`操作失败: ${error.message}`);
        });
    },
    resetApprovalState() {
      this.showCommentDialog = false;
      this.approvalComment = '';
      this.confirmedHours = '';
      this.autoAuditAfterConfirm = false;
      this.currentAction = '';
      this.currentItem = null;
      this.isSelectAll = false;
    },
    selectCommonComment(comment) {
      this.approvalComment = comment;
    },
    toggleExpand(item) {
      // 使用Vue的$set方法确保响应式更新
      this.$set(item, 'expanded', !item.expanded);
    },
    onStatusConfirm(value) {
      this.statusFilterText = value;
      this.showStatusPicker = false;
      // 状态改变后重新加载数据
      this.loadApprovalData();
    },
    // 新增：缓存相关方法
    // 生成缓存键
    generateCacheKey(userEntityId, startDate, endDate) {
      const dateKey = `${startDate || 'nostart'}_${endDate || 'noend'}`;
      return `audited_data_${userEntityId}_${dateKey}`;
    },
    
    // 检查是否有有效的缓存
    checkAuditedDataCache(userEntityId, startDate, endDate) {
      const cacheKey = this.generateCacheKey(userEntityId, startDate, endDate);
      
      // 检查内存缓存
      if (this.auditedDataCache.cacheKey === cacheKey && 
          this.auditedDataCache.lastUpdate && 
          this.auditedDataCache.data.length > 0) {
        
        const now = Date.now();
        const timeDiff = now - this.auditedDataCache.lastUpdate;
        
        if (timeDiff < this.auditedDataCache.expireTime) {
          console.log('[WorkReportApproval] 使用内存中的已审核数据缓存');
          return this.auditedDataCache.data;
        }
      }
      
      // 检查LocalStorage缓存
      try {
        const cachedDataStr = localStorage.getItem(cacheKey);
        if (cachedDataStr) {
          const cachedData = JSON.parse(cachedDataStr);
          const now = Date.now();
          const timeDiff = now - cachedData.timestamp;
          
          if (timeDiff < this.auditedDataCache.expireTime) {
            console.log('[WorkReportApproval] 使用LocalStorage中的已审核数据缓存');
            // 更新内存缓存
            this.auditedDataCache.data = cachedData.data;
            this.auditedDataCache.lastUpdate = cachedData.timestamp;
            this.auditedDataCache.cacheKey = cacheKey;
            return cachedData.data;
          } else {
            // 缓存过期，删除
            localStorage.removeItem(cacheKey);
          }
        }
      } catch (error) {
        console.error('[WorkReportApproval] 读取已审核数据缓存失败:', error);
      }
      
      return null;
    },
    
    // 保存已审核数据到缓存
    saveAuditedDataCache(data, userEntityId, startDate, endDate) {
      const cacheKey = this.generateCacheKey(userEntityId, startDate, endDate);
      const timestamp = Date.now();
      
      // 保存到内存
      this.auditedDataCache.data = data;
      this.auditedDataCache.lastUpdate = timestamp;
      this.auditedDataCache.cacheKey = cacheKey;
      
      // 保存到LocalStorage
      try {
        const cacheData = {
          data: data,
          timestamp: timestamp,
          userEntityId: userEntityId,
          dateRange: { startDate, endDate }
        };
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
        console.log('[WorkReportApproval] 已审核数据已缓存到LocalStorage');
      } catch (error) {
        console.error('[WorkReportApproval] 保存已审核数据缓存失败:', error);
      }
    },
    
    // 清除已审核数据缓存
    clearAuditedDataCache(userEntityId = null) {
      // 清除内存缓存
      this.auditedDataCache.data = [];
      this.auditedDataCache.lastUpdate = null;
      this.auditedDataCache.cacheKey = '';
      
      // 清除LocalStorage缓存
      if (userEntityId) {
        // 清除特定用户的缓存
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith(`audited_data_${userEntityId}_`)) {
            localStorage.removeItem(key);
          }
        });
      } else {
        // 清除所有已审核数据缓存
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('audited_data_')) {
            localStorage.removeItem(key);
          }
        });
      }
      console.log('[WorkReportApproval] 已审核数据缓存已清除');
    },
    
    // 新增：日期范围计算工具方法
    getThisWeekRange() {
      const now = new Date();
      const today = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
      const mondayOffset = today === 0 ? -6 : 1 - today; // 计算到周一的偏移量
      
      const monday = new Date(now);
      monday.setDate(now.getDate() + mondayOffset);
      monday.setHours(0, 0, 0, 0);
      
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      sunday.setHours(23, 59, 59, 999);
      
      return { startDate: monday, endDate: sunday };
    },
    
    getLastWeekRange() {
      const thisWeek = this.getThisWeekRange();
      const lastWeekStart = new Date(thisWeek.startDate);
      lastWeekStart.setDate(thisWeek.startDate.getDate() - 7);
      
      const lastWeekEnd = new Date(thisWeek.endDate);
      lastWeekEnd.setDate(thisWeek.endDate.getDate() - 7);
      
      return { startDate: lastWeekStart, endDate: lastWeekEnd };
    },
    
    getThisMonthRange() {
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      endDate.setHours(23, 59, 59, 999);
      
      return { startDate, endDate };
    },
    
    getLastMonthRange() {
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endDate = new Date(now.getFullYear(), now.getMonth(), 0);
      endDate.setHours(23, 59, 59, 999);
      
      return { startDate, endDate };
    },
    initThisWeekDateRange() {
      const thisWeek = this.getThisWeekRange();
      this.startDate = thisWeek.startDate;
      this.endDate = thisWeek.endDate;
      this.dateRangeFilterText = '本周';
    },
    
    // 获取任务的真实已报工时
    async getTaskRealReportedHours(task) {
      if (!task) return 0;

      // 优先使用任务对象中的 sumTime 字段（来自 FSUMTIME）
      if (task.sumTime !== undefined && task.sumTime > 0) {
        const reportedHours = parseFloat(task.sumTime) || 0;
        console.log('[审批] 使用任务对象中的已报工时 (sumTime):', reportedHours, '任务:', task.taskName);

        // 将结果缓存，避免重复计算
        const taskOrProjectId = task.taskId || task.projectId;
        if (taskOrProjectId) {
          const cacheKey = `approval_reported_${taskOrProjectId}`;
          this.$set(this.reportedHoursCache, cacheKey, reportedHours);
        }

        return reportedHours;
      }

      // 优先获取任务ID，如果没有则使用项目ID
      const taskOrProjectId = task.taskId || task.projectId;
      if (!taskOrProjectId) {
        console.warn('[审批] 无法获取任务或项目ID，任务对象:', task);
        return 0;
      }

      console.log('[审批] 获取已报工时，ID:', taskOrProjectId);

      // 检查缓存
      const cacheKey = `approval_reported_${taskOrProjectId}`;
      if (this.reportedHoursCache[cacheKey] !== undefined) {
        console.log('[审批] 使用缓存的已报工时:', this.reportedHoursCache[cacheKey]);
        return this.reportedHoursCache[cacheKey];
      }

      try {
        console.log('[审批] 调用API获取已报工时，ID:', taskOrProjectId);
        const response = await getUserDueProjectTasks(taskOrProjectId, {
          forceRefresh: false,
          timeout: 10000
        });
        console.log('[审批] API响应:', response);

        if (response.success && response.data) {
          const reportedHours = response.data.reportedHours || 0;
          console.log('[审批] 获取到的已报工时:', reportedHours);
          // 缓存结果
          this.$set(this.reportedHoursCache, cacheKey, reportedHours);
          return reportedHours;
        }
      } catch (error) {
        console.error('[审批] 获取已报工时失败:', error);
      }

      // 如果API失败，返回原始值作为后备
      const originalHours = task.cumulativeHours || task.sumTime || 0;
      console.log('[审批] 使用原始值作为后备:', originalHours);
      return originalHours;
    },

    // 批量获取已报工时数据
    async loadReportedHoursForItems(items) {
      if (!items || items.length === 0) return;

      console.log('[审批] 开始批量获取已报工时数据，项目数量:', items.length);

      this.reportedHoursLoading = true;

      try {
        // 分离已有 sumTime 数据的项目和需要 API 获取的项目
        const itemsWithSumTime = [];
        const itemsNeedingAPI = [];

        items.forEach(item => {
          if (item.sumTime !== undefined && item.sumTime > 0) {
            // 直接使用 sumTime 字段
            const reportedHours = parseFloat(item.sumTime) || 0;
            this.$set(item, 'cumulativeHours', reportedHours);
            itemsWithSumTime.push({ id: item.id, reportedHours, source: 'sumTime' });
            console.log('[审批] 直接使用 sumTime 字段:', item.taskName, reportedHours);
          } else {
            itemsNeedingAPI.push(item);
          }
        });

        console.log(`[审批] 数据源分析: ${itemsWithSumTime.length} 个项目使用 sumTime 字段, ${itemsNeedingAPI.length} 个项目需要 API 获取`);

        // 只对需要 API 获取的项目进行异步调用
        const apiPromises = itemsNeedingAPI.map(async (item) => {
          const reportedHours = await this.getTaskRealReportedHours(item);
          // 更新项目的已报工时
          this.$set(item, 'cumulativeHours', reportedHours);
          return { id: item.id, reportedHours, source: 'API' };
        });

        const apiResults = await Promise.all(apiPromises);
        const allResults = [...itemsWithSumTime, ...apiResults];

        console.log('[审批] 批量获取已报工时完成:', allResults);

        // 更新显示
        this.$forceUpdate();
      } catch (error) {
        console.error('[审批] 批量获取已报工时失败:', error);
      } finally {
        this.reportedHoursLoading = false;
      }
    },

    // 清理已报工时缓存
    clearReportedHoursCache() {
      this.reportedHoursCache = {};
      console.log('[审批] 已清理已报工时缓存');
    },

    // 刷新已报工时数据
    async refreshReportedHours() {
      console.log('[审批] 开始刷新已报工时数据');
      this.clearReportedHoursCache();
      await this.loadReportedHoursForItems(this.approvalItems);
    }
  }
};
</script>

<style lang="scss" scoped>
// 定义金蝶企业蓝色变量
$kingdee-blue: #276ff5;
$kingdee-blue-light: rgba(39, 111, 245, 0.1);
$kingdee-blue-bright: #276ff5;

.work-report-container {
  min-height: 100vh;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
  
  .tab-nav {
    display: flex;
    background-color: #fff;
    height: 44px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    
    .tab-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      color: #323233;
      position: relative;
      cursor: pointer;
      
      &.active {
        color: $kingdee-blue;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 2px;
          background-color: $kingdee-blue;
        }
      }
    }
  }
  
  .approval-container {
    padding: 15px;
    margin-top: 44px; // 添加顶部边距44px以避免被固定导航栏遮挡
    flex: 1;
    
    .filter-bar {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 12px;
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      
      .filter-item {
        margin-right: 15px;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        
        .filter-label {
          font-size: 14px;
          color: #646566;
          margin-right: 8px;
        }
        
        .date-range, .status-select {
          font-size: 14px;
          color: #323233;
          display: flex;
          align-items: center;
          padding: 0 8px;
          height: 28px;
          background-color: #f7f8fa;
          border-radius: 4px;
          cursor: pointer;
          
          .van-icon {
            margin-left: 5px;
            font-size: 12px;
            color: #969799;
          }
        }
      }
      
      .filter-btn {
        margin-left: auto;
        background-color: #276ff5;
        border-color: #276ff5;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 4px;
      }
    }
    
    .batch-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .select-all {
        display: flex;
        align-items: center;
        
        .selected-count {
          margin-left: 10px;
          font-size: 14px;
          color: #646566;
        }
      }

      .action-buttons {
        display: flex;
        
        // 确认按钮使用金蝶蓝背景色
        .van-button--warning {
          background-color: $kingdee-blue;
          border-color: $kingdee-blue;
          color: #ffffff;
          
          &:active {
            background-color: darken($kingdee-blue, 10%);
            border-color: darken($kingdee-blue, 10%);
          }
        }
      }
    }
    
    .approval-list {
      .approval-item {
        background-color: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 15px;
          border-bottom: 1px solid #f2f3f5;
          
          .project-task-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
            margin-right: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
            
            &:hover {
              background-color: #f8f9fa;
            }
            
            &:active {
              background-color: #f2f3f5;
            }
            
            .project-title {
              font-size: 16px;
              font-weight: 600;
              color: #323233;
              line-height: 1.3;
              margin-bottom: 2px;
              word-break: break-all;
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .expand-icon {
                font-size: 14px;
                color: #969799;
                margin-left: 8px;
                flex-shrink: 0;
                transition: transform 0.2s ease, color 0.2s ease;
              }
              
              &:hover .expand-icon {
                color: $kingdee-blue;
              }
            }
            
            .task-subtitle {
              font-size: 14px;
              font-weight: 500;
              color: #646566;
              line-height: 1.2;
              word-break: break-all;
            }
          }
          
          .hours-info-display {
            font-size: 12px;
            color: #646566;
            margin-top: 4px;
            line-height: 1.2;
          }
          
          .user-status-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
            flex-shrink: 0;
          
          .submitter {
              font-size: 13px;
            font-weight: 500;
              color: #646566;
              white-space: nowrap;
          }
          
          .status {
              font-size: 12px;
              padding: 3px 8px;
            border-radius: 10px;
              white-space: nowrap;
            
            &.pending {
              color: #ff976a;
              background-color: rgba(255, 151, 106, 0.1);
            }
            
            &.approved, &.confirmed {
              color: #07c160;
              background-color: rgba(7, 193, 96, 0.1);
            }
            
            &.rejected {
              color: #ee0a24;
              background-color: rgba(238, 10, 36, 0.1);
            }
            
            &.audited {
              color: #1989fa;
              background-color: rgba(25, 137, 250, 0.1);
            }
            }
          }
        }
        
        .item-body {
          padding: 12px 15px;
          
          .basic-info {
            margin-bottom: 8px;
            
            .info-row {
              display: flex;
              margin-bottom: 8px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .info-item {
                flex: 1;
                display: flex;
                align-items: center;
                min-width: 0;
                padding: 2px 6px;
                
                &.full-width {
                  flex: 1 1 100%;
                }
                
                .label {
                  font-size: 13px;
                  color: #646566;
                  margin-right: 2px;
                  flex-shrink: 0;
                }
                
                .value {
                  font-size: 13px;
                  color: #646566;
                  font-weight: 500;
                  line-height: 1.3;
                  word-break: break-all;
                }
              }
            }
            
            .hours-tags {
              display: flex;
              flex-direction: column;
              gap: 6px;
              margin-top: 8px;
              
              .hours-row {
                display: flex;
                justify-content: space-between;
                gap: 8px;
              }
              
              .hours-tag {
                display: inline-flex;
                align-items: center;
                padding: 2px 6px;
                font-size: 13px;
                color: #646566;
                flex: 1 1 0;
                min-width: 60px;
                justify-content: flex-start;
                text-align: left;
                
                // 岗位字段需要更多空间
                &:last-child {
                  flex: 1.5 1 0;
                  min-width: 80px;
                }
                
                &.primary {
                  color: $kingdee-blue;
                  font-weight: 500;
                }
                
                &.pending-confirm {
                  color: #ff976a;
                  font-weight: 500;
                }
                
                .tag-label {
                  margin-right: 2px;
                }
                
                .tag-value {
                  font-weight: 500;
                }
                
                .hours-input {
                  width: 40px;
                  border: none;
                  background: transparent;
                  font-size: 13px;
                  font-weight: 500;
                  color: $kingdee-blue;
                  text-align: center;
                  outline: none;
                  
                  &:focus {
                    background-color: rgba(255, 255, 255, 0.8);
                    border-radius: 2px;
                  }
                }
                
                .tag-unit {
                  font-size: 13px;
                  font-weight: 500;
                  color: $kingdee-blue;
                }
                
                // pending-confirm 状态下的输入框和单位颜色
                &.pending-confirm {
                  .hours-input {
                    color: #ff976a;
                  }
                  
                  .tag-unit {
                    color: #ff976a;
                  }
                }
              }
            }
          }
          
          .detail-info {
            padding-top: 8px;
            border-top: 1px dashed #ebedf0;
            
            .item-row {
              display: flex;
              margin-bottom: 8px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .label {
                width: 90px;
                font-size: 14px;
                color: #646566;
                flex-shrink: 0;
              }
              
              .value {
                font-size: 14px;
                color: #323233;
                flex: 1;
                
                &.highlight {
                  color: $kingdee-blue;
                  font-weight: 500;
                }
                
                &.confirmed {
                  color: #07c160;
                }
                
                // 任务类型样式
                &.task-type {
                  font-weight: 500;
                  padding: 2px 6px;
                  border-radius: 3px;
                  display: inline-block;
                  
                  &.temp {
                    color: #1989fa;
                    background-color: rgba(25, 137, 250, 0.1);
                  }
                  
                  &.timecost {
                    color: #ff976a;
                    background-color: rgba(255, 151, 106, 0.1);
                  }
                }
              }
            }
            
            // 工作内容样式
            .work-content {
              margin-top: 12px;
              
              .content-header {
                margin-bottom: 6px;
                
                .label {
                  font-size: 14px;
                  color: #646566;
                  font-weight: 500;
                }
              }
              
              .content-body {
                background-color: #f7f8fa;
                border: 1px solid #ebedf0;
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 14px;
                color: #323233;
                line-height: 1.5;
                white-space: pre-wrap;
                word-wrap: break-word;
                max-height: none !important;
                overflow-y: visible !important;
                
                &:empty {
                  display: none;
                }
              }
            }
          }
        }
        
        .item-footer {
          display: flex;
          justify-content: flex-end;
          padding: 12px 15px;
          border-top: 1px solid #f2f3f5;
          
          .van-button {
            margin-left: 10px;
            
            &:first-child {
              margin-left: 0;
            }
          }
          
          .van-button--primary {
            background-color: $kingdee-blue;
            border-color: $kingdee-blue;
          }
        }
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50px 0;
    }
  }
}

// 响应式设计
@media screen and (min-width: 768px) {
  .work-report-container {
    .approval-container {
      max-width: 768px;
      margin: 0 auto;
    }
  }
}

// 移动端适配
@media screen and (max-width: 480px) {
  .work-report-container {
    .approval-container {
      .approval-list {
        .approval-item {
          .item-header {
            padding: 10px 12px;
            
            .project-task-info {
              margin-right: 8px;
              padding: 3px 6px;
              
              .project-title {
                font-size: 15px;
                margin-bottom: 3px;
                
                .expand-icon {
                  font-size: 12px;
                  margin-left: 6px;
                }
              }
              
              .task-subtitle {
                font-size: 13px;
              }
              
              .hours-info-display {
                font-size: 11px;
                margin-top: 3px;
              }
            }
            
            .user-status-info {
              gap: 3px;
              
              .submitter {
                font-size: 12px;
              }
              
              .status {
                font-size: 11px;
                padding: 2px 6px;
              }
            }
          }
        }
      }
    }
  }
}

.comment-dialog-content {
  padding: 16px;

  .hours-info {
    background-color: #f7f8fa;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 16px;
    max-height: 300px;
    overflow-y: auto;
    
    .hours-row {
      display: flex;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .hours-label {
        width: 90px;
        font-size: 14px;
        color: #646566;
        flex-shrink: 0;
      }
      
      .hours-value {
        font-size: 14px;
        color: #323233;
      }
    }
  }
  
  .hours-input {
    margin-bottom: 16px;
    
    .hours-unit {
      font-size: 14px;
      color: #323233;
      margin-right: 8px;
    }
  }

  .common-comments {
    margin-top: 16px;

    .common-comments-title {
      font-size: 14px;
      color: #646566;
      margin-bottom: 8px;
    }

    .common-comments-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .comment-tag {
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #f7f8fa;
        color: #323233;
        border: 1px solid #f2f3f5;
        
        &.active {
          background-color: $kingdee-blue-light;
          color: $kingdee-blue;
          border-color: $kingdee-blue;
        }
        
        &:hover {
          background-color: $kingdee-blue-light;
          color: $kingdee-blue;
        }
      }
    }
  }
}

.approval-item {
  .item-header {
    .van-checkbox {
      margin-right: 10px;
    }
  }
  
  .item-body {
    .value.confirmed {
      color: #07c160;
      font-weight: 500;
    }
  }
  
  .item-footer {
    .van-button {
      margin-left: 10px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

// 使用深度选择器自定义 van-dialog 样式
:deep(.kingdee-dialog) {
  border-radius: 8px;
  overflow: hidden;
  
  .van-dialog__header {
    background-color: $kingdee-blue;
    color: rgba(255, 255, 255, 0.95);
    padding: 14px 16px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .van-dialog__content {
    padding: 0;
  }
  
  .van-dialog__footer {
    padding: 8px 16px 16px;
    
    .van-button {
      border-radius: 4px;
      font-size: 14px;
      height: 36px;
      
      &.van-dialog__confirm {
        background-color: $kingdee-blue;
        border-color: $kingdee-blue;
        color: #ffffff !important;
        
        &:active {
          background-color: darken($kingdee-blue, 10%);
        }
      }
    }
  }
  
  .van-field__control {
    background-color: #f7f8fa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px 12px;
    color: #323233;
    
    &:focus {
      border-color: $kingdee-blue;
    }
    
    &::placeholder {
      color: #969799;
    }
  }
}

// 自定义输入框样式
:deep(.kingdee-field) {
  .van-field__control {
    background-color: #f7f8fa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px 12px;
    color: #323233;
    
    &:focus {
      border-color: $kingdee-blue;
      outline: none;
    }
    
    &::placeholder {
      color: #969799;
    }
  }
}
</style> 