# 已报工时API使用说明

## 概述

根据您的需求，我们已经为"已报"字段添加了一个新的API接口，用于获取真实的已报工时数据，而不是通过计算得出。

## API接口信息

### 接口地址
```
http://**************:81/k3cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.GetUserDueProjectTasks,JR.K3.PLM.WorkhourReport.common.kdsvc
```

### 请求方法
```
POST
```

### 请求参数
```json
{
  "parameter": {
    "data": {
      "TP": 10000
    }
  }
}
```

**参数说明：**
- `TP`: 任务ID或项目ID，优先获取任务，如果没有任务则获取项目ID

## 新增的API函数

### 1. getUserDueProjectTasks()

**函数签名：**
```javascript
getUserDueProjectTasks(taskOrProjectId, options = {})
```

**参数说明：**
- `taskOrProjectId` (string|number): 任务ID或项目ID
- `options` (Object): 可选配置项
  - `timeout` (number): 请求超时时间(毫秒)，默认30秒
  - `retries` (number): 失败重试次数，默认1次
  - `forceRefresh` (boolean): 是否强制刷新缓存，默认false
  - `cacheExpiry` (number): 缓存有效期(毫秒)，默认1小时

**返回值：**
```javascript
{
  success: boolean,
  message: string,
  data: {
    reportedHours: number,    // 总已报工时
    tasks: Array             // 任务详情列表
  }
}
```

## 使用示例

### 1. 在Vue组件中导入
```javascript
import { getUserDueProjectTasks } from '../api/report';
```

### 2. 基本使用
```javascript
// 获取任务ID为10000的已报工时
async function getReportedHours() {
  try {
    const response = await getUserDueProjectTasks(10000);
    if (response.success) {
      console.log('已报工时:', response.data.reportedHours);
      console.log('任务详情:', response.data.tasks);
    }
  } catch (error) {
    console.error('获取已报工时失败:', error);
  }
}
```

### 3. 带配置选项的使用
```javascript
// 强制刷新并设置超时时间
async function getReportedHoursWithOptions() {
  try {
    const response = await getUserDueProjectTasks(10000, {
      forceRefresh: true,
      timeout: 60000,  // 60秒超时
      retries: 2       // 重试2次
    });
    
    if (response.success) {
      console.log('已报工时:', response.data.reportedHours);
    }
  } catch (error) {
    console.error('获取已报工时失败:', error);
  }
}
```

### 4. 在统计组件中的使用示例
```javascript
// 在WorkReportStatistics.vue中已经添加了示例方法

// 获取特定任务的已报工时
async getTaskReportedHours(taskId) {
  const response = await getUserDueProjectTasks(taskId);
  return response.success ? response.data.reportedHours : 0;
}

// 获取特定项目的已报工时
async getProjectReportedHours(projectId) {
  const response = await getUserDueProjectTasks(projectId);
  return response.success ? response.data.reportedHours : 0;
}
```

## 数据格式说明

### API响应数据结构
API返回的原始数据会被自动格式化为以下结构：

```javascript
{
  success: true,
  message: "已报工时数据获取成功",
  data: {
    reportedHours: 25.5,  // 总已报工时（小时）
    tasks: [
      {
        id: "reported-task-1642567890123-0-abc123def",
        taskId: "TASK001",
        projectId: "PROJ001", 
        taskName: "任务名称",
        projectName: "项目名称",
        reportedHours: 8.5,
        reportDate: "2025-01-26",
        status: "C"
      }
      // ... 更多任务
    ]
  }
}
```

## 缓存机制

- **缓存键格式**: `user_due_project_tasks_{taskOrProjectId}`
- **默认缓存时间**: 1小时
- **缓存策略**: 优先使用缓存，支持强制刷新
- **失败后备**: 网络失败时尝试使用过期缓存

## 错误处理

API函数包含完整的错误处理机制：
- 网络超时重试
- 服务器错误处理
- 缓存后备机制
- 详细的错误日志记录

## 注意事项

1. **TP参数**: 优先获取任务ID，如果没有任务则获取项目ID
2. **数据用途**: 此API专门用于"已报"字段的数据获取，不用于计算
3. **性能优化**: 使用了缓存机制，避免频繁请求
4. **兼容性**: 与现有系统完全兼容，不影响其他功能

## 集成位置

新的API函数已经添加到：
- `src/api/report.js` - 主要API函数
- `src/view/WorkReportStatistics.vue` - 使用示例

您可以在任何需要获取已报工时数据的地方调用这个API函数。
