/**
 * 下拉刷新混入
 * 提供统一的下拉刷新状态管理和通用方法
 */
export default {
  data() {
    return {
      // 下拉刷新状态
      isRefreshing: false,
      // 刷新提示文本
      refreshText: {
        pulling: '下拉即可刷新...',
        loosing: '释放即可刷新...',
        loading: '加载中...',
        success: '刷新成功'
      }
    }
  },
  
  methods: {
    /**
     * 下拉刷新处理方法
     * 各页面需要重写此方法来实现具体的刷新逻辑
     */
    async onRefresh() {
      try {
        this.isRefreshing = true;
        
        // 调用页面特定的刷新方法
        if (typeof this.refreshData === 'function') {
          await this.refreshData();
        } else {
          console.warn('页面未定义 refreshData 方法');
        }
        
        // 显示刷新成功提示
        this.$toast.success('刷新成功');
        
      } catch (error) {
        console.error('下拉刷新失败:', error);
        this.$toast.fail('刷新失败，请重试');
      } finally {
        // 确保刷新状态被重置
        this.isRefreshing = false;
      }
    },
    
    /**
     * 手动触发刷新
     * 可以在其他地方调用此方法来触发刷新
     */
    triggerRefresh() {
      this.onRefresh();
    }
  }
} 