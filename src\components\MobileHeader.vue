<template>
  <div class="mobile-header">
    <div class="status-bar">
      <div class="time">{{ currentTime }}</div>
      <div class="status-icons">
        <van-icon name="wifi" />
        <van-icon name="bell" />
        <div class="battery">
          <span>{{ batteryLevel }}%</span>
          <van-icon name="phone" />
        </div>
      </div>
    </div>
    <div class="app-header">
      <div class="company-info">
        <div class="avatar">
          <span>{{ firstLetter }}</span>
        </div>
        <div class="company-name">
          <h3>杰软管理咨询</h3>
          <div class="dropdown-icon">
            <van-icon name="arrow-down" />
          </div>
        </div>
      </div>
      <div class="user-actions">
        <div class="action-icon">
          <van-icon name="service" size="24" />
        </div>
      </div>
    </div>
    <div class="slogan">
      <p>我们生活在行动中，而不是生活在岁月里</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MobileHeader',
  data() {
    return {
      currentTime: '10:11',
      batteryLevel: 88,
    };
  },
  computed: {
    firstLetter() {
      return '杰';
    }
  },
  mounted() {
    this.updateTime();
    // 设置定时器，每分钟更新一次时间
    setInterval(this.updateTime, 60000);
  },
  methods: {
    updateTime() {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      this.currentTime = `${hours}:${minutes}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.mobile-header {
  width: 100%;
  padding: 10px 15px;
  background: linear-gradient(135deg, #1890ff 0%, #0960bd 100%);
  color: #fff;
  position: relative;
  
  .status-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    
    .status-icons {
      display: flex;
      gap: 8px;
      
      .battery {
        display: flex;
        align-items: center;
        gap: 2px;
      }
    }
  }
  
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .company-info {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .avatar {
        width: 40px;
        height: 40px;
        background-color: #ffa022;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }
      
      .company-name {
        display: flex;
        align-items: center;
        
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: bold;
        }
        
        .dropdown-icon {
          margin-left: 5px;
        }
      }
    }
  }
  
  .slogan {
    margin-top: 15px;
    font-size: 15px;
    opacity: 0.9;
    
    p {
      margin: 0;
    }
  }
}

// 响应式设计 - 桌面端适配
@media screen and (min-width: 768px) {
  .mobile-header {
    max-width: 768px;
    margin: 0 auto;
    border-radius: 0 0 10px 10px;
    
    .app-header {
      .company-info {
        .company-name {
          h3 {
            font-size: 22px;
          }
        }
      }
    }
    
    .slogan {
      font-size: 16px;
    }
  }
}
</style> 