import axios from 'axios';
import { getCache, setCache, getWorkReportCacheKey, removeCache, getStatisticsCacheKey } from '../utils/cache';
import { showToast } from 'vant';

/**
 * 获取工时报告数据
 * @param {string} reportDate 汇报日期，格式为YYYY-MM-DD
 * @param {string} creatorAccount 创建者账号（用户名）
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @param {number} options.retries 失败重试次数，默认1次
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认2小时（工时数据更新频繁）
 * @returns {Promise} 返回包含工时报告数据的Promise
 */
export function getWorkReportData(reportDate, creatorAccount, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  const maxRetries = options.retries || 1; // 默认重试1次
  const forceRefresh = options.forceRefresh || false; // 默认不强制刷新
  const cacheExpiry = options.cacheExpiry || 2 * 60 * 60 * 1000; // 默认2小时（工时数据更新频繁）
  
  let retryCount = 0;
  const cacheKey = getWorkReportCacheKey(reportDate, creatorAccount);
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的工时报告数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除工时报告缓存`);
  }

  // 创建执行查询的函数
  const executeQuery = () => {
    return new Promise((resolve, reject) => {
      // 构建请求参数
      const queryData = {
        data: {
          "FormId": "PBEW_GSHB",
          "FieldKeys": "FProj.FName,FTask.FName,FDocumentStatus,FPlanStartDate,FPlanEndDate,FPlanWorkHours,FManHour,FOverTime,FFinishRate,FReport,FisFinish,FPOST.FName,FHBDate,FBillno,FOtherTaskName,FOtherTaskType,FTmpTaskTypeid.Fname,FBurnOffTypeid.Fname,FSUMTIME",
          "FilterString": `FHBDate='${reportDate}' and FReportEmp.FNumber='${creatorAccount}'`,
          "OrderString": "",
          "TopRowCount": 0,
          "StartRow": 0,
          "Limit": 2000,
          "SubSystemId": ""
        }
      };
      
      // 记录请求开始时间
      const requestStartTime = Date.now();
      console.log(`[API] 开始获取工时报告数据，日期: ${reportDate}, 用户账号: ${creatorAccount}`);
      
      axios({
        method: 'post',
        url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
        data: queryData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: timeout // 设置请求超时时间
      })
      .then(response => {
        // 记录请求完成时间和耗时
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        console.log(`[API] 获取工时报告数据成功，耗时: ${requestDuration}ms`);
        
        const data = response.data;
        
        // 打印API原始响应数据
        console.log('[API] 原始响应数据:', JSON.stringify(data, null, 2));
        console.log('[API] 响应数据类型:', typeof data, Array.isArray(data) ? '是数组' : '不是数组', '长度:', Array.isArray(data) ? data.length : '未知');
        
        if (Array.isArray(data) && data.length > 0) {
          // 打印第一条数据作为示例
          console.log('[API] 第一条数据示例:', Array.isArray(data[0]) ? data[0] : JSON.stringify(data[0], null, 2));
        }
        
        if (data) {
          // 检查数据有效性
          if (Array.isArray(data)) {
            // 将API返回的数据转换为前端组件需要的格式
            const formattedData = formatReportData(data);
            
            // 缓存成功获取的数据
            setCache(cacheKey, formattedData, cacheExpiry);
            console.log(`[API] 工时报告数据已缓存，有效期: ${(cacheExpiry / 3600000).toFixed(1)}小时`);
            
            resolve(formattedData);
          } else {
            console.warn('[API] 返回数据格式不符合预期，不是数组:', data);
            resolve({
              success: false,
              message: "返回数据格式不符合预期",
              data: {
                tasks: []
              }
            });
          }
        } else {
          console.warn('[API] 未获取到数据或数据为空');
          resolve({
            success: false,
            message: "未获取到数据",
            data: {
              tasks: []
            }
          });
        }
      })
      .catch(error => {
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        
        // 详细记录错误信息
        let errorDetails = '';
        
        if (error.response) {
          // 服务器返回了错误响应
          errorDetails = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取工时报告数据失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
        } else if (error.request) {
          // 请求已发送但没有收到响应
          if (error.code === 'ECONNABORTED') {
            errorDetails = `请求超时(${timeout}ms)`;
            console.error(`[API] 获取工时报告数据超时，耗时: ${requestDuration}ms`);
          } else {
            errorDetails = `无响应: ${error.message}`;
            console.error(`[API] 获取工时报告数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
          }
        } else {
          // 请求设置出错
          errorDetails = `请求错误: ${error.message}`;
          console.error(`[API] 获取工时报告数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
        }
        
        // 如果还可以重试，则重试请求
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`[API] 准备第${retryCount}次重试获取工时报告数据...`);
          
          // 延迟后重试，每次重试延迟时间增加
          const retryDelay = 1000 * retryCount; // 1秒 * 重试次数
          setTimeout(() => {
            executeQuery().then(resolve).catch(reject);
          }, retryDelay);
        } else {
          // 所有重试失败后，尝试从缓存加载过期数据作为后备
          const expiredCache = localStorage.getItem(cacheKey);
          if (expiredCache) {
            try {
              const cacheData = JSON.parse(expiredCache).data;
              console.warn(`[API] 使用过期缓存作为后备数据`);
              showToast({
                message: '使用离线缓存数据（可能不是最新）',
                type: 'warning',
                duration: 3000
              });
              resolve(cacheData);
            } catch (e) {
              reject(new Error(`获取工时报告失败: ${errorDetails}`));
            }
          } else {
            reject(new Error(`获取工时报告失败: ${errorDetails}`));
          }
        }
      });
    });
  };
  
  // 执行查询
  return executeQuery();
}

/**
 * 格式化API返回的工时报告数据为组件所需格式
 * @param {Array} apiData API返回的原始数据
 * @returns {Object} 格式化后的数据，包含项目和任务
 */
function formatReportData(apiData) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化数据: 无数据可格式化');
    return {
      success: false,
      message: "无数据返回",
      data: {
        tasks: []
      }
    };
  }
  
  console.log('[API] 开始格式化数据, 行数:', apiData.length);
  
  try {
    // 将API返回的数据转换为任务列表
    const tasks = apiData.map((item) => {
      // 检查数据格式，可能是数组或对象
      const rowData = Array.isArray(item) ? {
        projectName: item[0] || '',       // FProj.FName
        taskName: item[1] || '',          // FTask.FName
        documentStatus: item[2] || '',    // FDocumentStatus
        planStartDate: item[3] || '',     // FPlanStartDate
        planEndDate: item[4] || '',       // FPlanEndDate
        plannedHours: item[5] || 0,       // FPlanWorkHours
        regularHours: item[6] || 0,       // FManHour
        overtimeHours: item[7] || 0,      // FOverTime
        progress: item[8] || 0,           // FFinishRate
        taskContent: item[9] || '',       // FReport
        isFinish: item[10] || false,      // FisFinish
        position: item[11] || '',         // FPOST.FName
        reportDate: item[12] || '',       // FHBDate
        billno: item[13] || '',           // FBillno
        otherTaskName: item[14] || '',    // FOtherTaskName
        otherTaskType: item[15] || '',    // FOtherTaskType
        tmpTaskTypeName: item[16] || '',  // FTmpTaskTypeid.Fname
        burnOffTypeName: item[17] || '',  // FBurnOffTypeid.Fname
        fsumtime: item[18] || 0,          // FSUMTIME (已报工时)
        projectId: item[19] || '',        // FProj (项目ID)
        taskId: item[20] || ''            // FTask (任务ID)
      } : {
        projectName: item['FProj.FName'] || '',
        taskName: item['FTask.FName'] || '',
        documentStatus: item['FDocumentStatus'] || '',
        planStartDate: item['FPlanStartDate'] || '',
        planEndDate: item['FPlanEndDate'] || '',
        plannedHours: item['FPlanWorkHours'] || 0,
        regularHours: item['FManHour'] || 0,
        overtimeHours: item['FOverTime'] || 0,
        progress: item['FFinishRate'] || 0,
        taskContent: item['FReport'] || '',
        isFinish: item['FisFinish'] || false,
        position: item['FPOST.FName'] || '',
        reportDate: item['FHBDate'] || '',
        billno: item['FBillno'] || '',
        otherTaskName: item['FOtherTaskName'] || '',
        otherTaskType: item['FOtherTaskType'] || '',
        tmpTaskTypeName: item['FTmpTaskTypeid.Fname'] || '',
        burnOffTypeName: item['FBurnOffTypeid.Fname'] || '',
        fsumtime: item['FSUMTIME'] || 0,  // FSUMTIME (已报工时)
        projectId: item['FProj'] || '',   // FProj (项目ID)
        taskId: item['FTask'] || ''       // FTask (任务ID)
      };
      
      // 处理日期格式，转换为YYYY-MM-DD
      const formatDate = (dateStr) => {
        if (!dateStr) return '';
        try {
          const date = new Date(dateStr);
          return date.toISOString().split('T')[0];
        } catch (e) {
          return dateStr;
        }
      };
      
      // 生成唯一ID，使用时间戳和随机数组合
      const uniqueId = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 记录任务计划日期字段的值
      if (rowData.planStartDate || rowData.planEndDate) {
        console.log(`[API] 任务"${rowData.taskName}"计划日期: FPLANNEDST="${rowData.planStartDate || '无'}", FPLANNEDED="${rowData.planEndDate || '无'}"`);
      }
      
      // 记录任务单据编号
      if (rowData.billno) {
        console.log(`[API] 任务"${rowData.taskName}"单据编号: FBillno="${rowData.billno}"`);
      }
      
      // 处理FOtherTaskType映射关系：1=临时任务，2=耗费任务
      let taskType = 'normal'; // 默认为普通任务
      let displayTaskName = rowData.taskName; // 默认使用原任务名称
      let displayProjectName = rowData.projectName; // 默认使用原项目名称
      
      // 详细记录关键字段的值，用于调试
      console.log(`[API] 任务数据调试 - 原始字段值:
        taskName="${rowData.taskName}"
        otherTaskName="${rowData.otherTaskName}"
        otherTaskType="${rowData.otherTaskType}"
        projectName="${rowData.projectName}"
        projectId="${rowData.projectId}"
        taskId="${rowData.taskId}"`);

      // 打印项目ID和任务ID到控制台（按用户要求）
      console.log(`[API] 项目ID (FProj): ${rowData.projectId || '无'}`);
      console.log(`[API] 任务ID (FTask): ${rowData.taskId || '无'}`);

      if (rowData.otherTaskType) {
        const otherTypeNum = parseInt(rowData.otherTaskType);
        console.log(`[API] 检测到otherTaskType=${otherTypeNum}, 开始处理特殊任务类型`);
        
        if (otherTypeNum === 1) {
          taskType = 'temp'; // 临时任务
          // 改进默认名称处理：如果FOtherTaskName为空，使用更明确的默认名称
          if (rowData.otherTaskName && rowData.otherTaskName.trim() !== '') {
            displayTaskName = rowData.otherTaskName;
            console.log(`[API] 使用FOtherTaskName作为临时任务名称: "${displayTaskName}"`);
          } else if (rowData.taskName && rowData.taskName.trim() !== '') {
            displayTaskName = rowData.taskName;
            console.log(`[API] FOtherTaskName为空，使用原任务名称: "${displayTaskName}"`);
          } else {
            displayTaskName = '临时任务 - ' + (new Date().toLocaleTimeString());
            console.log(`[API] 所有任务名称字段都为空，使用默认临时任务名称: "${displayTaskName}"`);
          }
          displayProjectName = '临时任务'; // 设置虚拟项目名称
          console.log(`[API] 识别到临时任务: FOtherTaskType=1, 最终任务名称="${displayTaskName}", 临时任务类型="${rowData.tmpTaskTypeName}", 项目名称="${displayProjectName}"`);
        } else if (otherTypeNum === 2) {
          taskType = 'timecost'; // 耗费任务
          // 改进默认名称处理：如果FOtherTaskName为空，使用更明确的默认名称
          if (rowData.otherTaskName && rowData.otherTaskName.trim() !== '') {
            displayTaskName = rowData.otherTaskName;
            console.log(`[API] 使用FOtherTaskName作为耗费任务名称: "${displayTaskName}"`);
          } else if (rowData.taskName && rowData.taskName.trim() !== '') {
            displayTaskName = rowData.taskName;
            console.log(`[API] FOtherTaskName为空，使用原任务名称: "${displayTaskName}"`);
          } else {
            displayTaskName = '耗费任务 - ' + (new Date().toLocaleTimeString());
            console.log(`[API] 所有任务名称字段都为空，使用默认耗费任务名称: "${displayTaskName}"`);
          }
          displayProjectName = '耗费'; // 设置虚拟项目名称
          console.log(`[API] 识别到耗费任务: FOtherTaskType=2, 最终任务名称="${displayTaskName}", 耗费类型="${rowData.burnOffTypeName}", 项目名称="${displayProjectName}"`);
        }
      } else {
        console.log(`[API] 普通任务: taskName="${displayTaskName}", projectName="${displayProjectName}"`);
      }
      
      return {
        id: uniqueId,  // 使用唯一ID替代简单的索引
        project: displayProjectName, // 使用处理后的项目名称
        projectName: displayProjectName, // 添加 projectName 字段，与 project 保持一致
        taskName: displayTaskName, // 使用处理后的任务名称
        isProjectLevel: !displayTaskName || displayTaskName.trim() === '', // 修正：基于处理后的任务名称判断是否为项目级别
        taskContent: rowData.taskContent,
        regularHours: parseFloat(rowData.regularHours) || 0,
        overtimeHours: parseFloat(rowData.overtimeHours) || 0,
        progress: parseFloat(rowData.progress) || 0,
        date: formatDate(rowData.reportDate),
        plannedHours: parseFloat(rowData.plannedHours) || 0,
        startDate: formatDate(rowData.planStartDate),
        endDate: formatDate(rowData.planEndDate),
        position: rowData.position,
        status: rowData.documentStatus || 'A',
        content: rowData.taskContent,
        fbillno: rowData.billno, // 添加fbillno字段
        isFinish: rowData.isFinish === true || rowData.isFinish === 'true' || rowData.isFinish === 1,
        // 添加新的字段支持
        taskType: taskType,
        otherTaskName: rowData.otherTaskName,
        otherTaskType: rowData.otherTaskType,
        // 为临时任务和耗费任务添加特殊标记（保持向后兼容）
        isTempTask: taskType === 'temp',
        isTimeCost: taskType === 'timecost',
        tmpTaskTypeName: rowData.tmpTaskTypeName,
        burnOffTypeName: rowData.burnOffTypeName,
        // 添加项目ID和任务ID字段（按用户要求存储但不展示）
        projectId: rowData.projectId,
        taskId: rowData.taskId,
        // 添加已报工时字段
        fsumtime: parseFloat(rowData.fsumtime) || 0
      };
    });
    
    console.log(`[API] 数据格式化完成，共 ${tasks.length} 条任务记录`);
    
    // 返回格式化后的数据
    return {
      success: true,
      message: "数据获取成功",
      data: {
        tasks: tasks
      }
    };
  } catch (error) {
    console.error('[API] 格式化数据时出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化数据错误: ${error.message}`,
      data: {
        tasks: []
      }
    };
  }
}

/**
 * 保存工时报告数据到服务器
 * @param {Array} tasks 任务数据数组
 * @returns {Promise} 返回包含保存结果的Promise
 */
export function saveWorkReportData(tasks) {
  // 保存成功后，更新缓存 (保留现有逻辑)
  if (tasks && tasks.length > 0) {
    const firstTask = tasks[0];
    if (firstTask.date) {
      // 从任务中提取日期，从work_report_user中提取用户账号
      const date = firstTask.date;
      // 修改为从work_report_user获取username
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const username = userInfo.username || '';
      
      if (username) {
        // 计算缓存键并更新缓存
        const cacheKey = getWorkReportCacheKey(date, username);
        
        // 更新缓存数据
        const cacheData = {
          success: true,
          data: {
            tasks: tasks
          }
        };
        
        setCache(cacheKey, cacheData);
        console.log(`[API] 已更新工时报告缓存: ${date}, 用户账号: ${username}`);
      }
    }
  }
  
  // 如果任务数组为空，直接返回
  if (!tasks || tasks.length === 0) {
    return Promise.resolve({
      success: false,
      message: '无任务数据可保存'
    });
  }

  // 将前端任务数据转换为API需要的格式
  const apiRequestData = prepareWorkReportSaveData(tasks);
  
  console.log('[API] 工时报告保存请求数据:', JSON.stringify(apiRequestData, null, 2));
  
  // 调用真实API保存数据
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc',
    data: apiRequestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 工时报告保存响应:', JSON.stringify(response.data, null, 2));
    
    // 检查API响应
    if (response.data && response.data.Result && response.data.Result.ResponseStatus) {
      const status = response.data.Result.ResponseStatus;
      
      if (status.IsSuccess) {
        return {
        success: true,
          message: '数据已保存',
          data: response.data
        };
      } else {
        // API返回错误
        const errors = status.Errors || [];
        const errorMsg = errors.length > 0 ? 
          errors.map(e => e.Message).join('; ') : 
          '保存失败，未知错误';
        
        console.error('[API] 工时报告保存失败:', errorMsg);
        
        return {
          success: false,
          message: errorMsg,
          data: response.data
        };
      }
    } else {
      // 响应格式不符合预期
      console.error('[API] 工时报告保存响应格式不符合预期:', response.data);
      
      return {
        success: false,
        message: '服务器响应格式异常',
        data: response.data
      };
    }
  })
  .catch(error => {
    // 处理网络错误等
    console.error('[API] 工时报告保存请求失败:', error);
    
    let errorMessage = '网络错误';
    
    if (error.response) {
      // 服务器返回了错误状态
      errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
    } else if (error.request) {
      // 请求发送了但没有收到响应
      if (error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请稍后重试';
      } else {
        errorMessage = '无法连接到服务器，请检查网络';
      }
    } else {
      // 请求设置出错
      errorMessage = `请求错误: ${error.message}`;
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error
    };
  });
}

/**
 * 准备工时报告保存数据，将前端任务数据转换为API需要的格式
 * @param {Array} tasks 前端任务数组
 * @returns {Object} 符合API要求的请求数据
 */
function prepareWorkReportSaveData(tasks) {
  // 仅处理第一个任务（根据需求，API一次处理一个任务）
  const task = tasks[0];
  
  // 获取用户信息
  const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
  const userId = userInfo.userEntityId || userInfo.userId || ''; // 优先使用FUserId，后备使用Fid
  
  // 添加日志以便验证使用的是哪个ID
  console.log('[API] 用户ID信息 - userEntityId:', userInfo.userEntityId, 'userId:', userInfo.userId, '最终使用的userId:', userId);
  
  // 获取完整的岗位信息，包含部门编码、岗位编码、职级编码
  const positionOptionsString = localStorage.getItem(`position_data_${userInfo.username}`);
  let departmentCode = '';
  let positionCode = '';
  let rankCode = '';
  
  if (positionOptionsString) {
    try {
      const positionCacheData = JSON.parse(positionOptionsString);
      // 从缓存数据中提取岗位信息
      if (positionCacheData.data && positionCacheData.data.data && Array.isArray(positionCacheData.data.data) && positionCacheData.data.data.length > 0) {
        const firstPositionOption = positionCacheData.data.data[0];
        departmentCode = firstPositionOption.deptNumber || '';
        positionCode = firstPositionOption.positionNumber || '';
        rankCode = firstPositionOption.rankNumber || '';
        
        console.log('[API] 从缓存获取到的编码信息:', {
          departmentCode: departmentCode,
          positionCode: positionCode,
          rankCode: rankCode,
          源数据: firstPositionOption
        });
      } else {
        console.warn('[API] 岗位缓存数据格式不符合预期:', positionCacheData);
      }
    } catch (error) {
      console.error('[API] 解析岗位缓存数据失败:', error);
    }
  } else {
    console.warn('[API] 未找到岗位缓存数据，将使用空编码');
  }
  
  // 判断是否完成，如果完成进度为100%，则设置为已完成
  const finishRate = parseFloat(task.progress || 0);
  const isFinished = finishRate >= 100 ? "1" : "0";
  
  // 提取日期信息，用于年份和月份
  let year = 0;
  let month = 0;
  if (task.date) {
    const dateObj = new Date(task.date);
    if (!isNaN(dateObj.getTime())) {
      year = dateObj.getFullYear();
      month = dateObj.getMonth() + 1; // 月份从0开始，需要+1
    }
  }
  
  // 判断是否为敏捷任务
  const isAgileTask = task.taskType === 'agile';
  
  // 打印任务数据以便调试
  console.log('[API] 准备保存的任务数据:', {
    id: task.id,
    taskName: task.taskName,
    taskCode: task.taskCode,
    project: task.project,
    projectName: task.projectName,
    regularHours: task.regularHours,
    overtimeHours: task.overtimeHours,
    progress: task.progress,
    date: task.date,
    department: task.department,
    position: task.position,
    rank: task.rank,
    taskContent: task.taskContent,
    plannedHours: task.plannedHours,
    plannedStartDate: task.plannedStartDate || task.startDate,
    plannedEndDate: task.plannedEndDate || task.endDate,
    isAgileTask: isAgileTask,
    ownProject: task.ownProject,
    // 打印编码字段以便验证
    positionCode: task.position || '未设置',
    departmentCode: task.department || '未设置',
    rankCode: task.rank || '未设置',
    organizationCode: task.organization || '未设置'
  });
  
  // 判断是否为耗费任务，动态设置ValidateRepeatJson
  const isTimeCostTask = task.taskType === 'timecost';
  
  // 只有耗费任务需要自动提交和审核，临时任务和普通任务都不需要
  const needAutoSubmitAndAudit = task.taskType === 'timecost';
  
  // 构建API请求数据
  const requestData = {
    "FormId": "PBEW_GSHB",
    "data": {
      "NeedUpDateFields": [],
      "NeedReturnFields": [],
      "IsDeleteEntry": "true",
      "SubSystemId": "",
      "IsVerifyBaseDataField": "false",
      "IsEntryBatchFill": "true",
      "ValidateFlag": "true",
      "NumberSearch": "true",
      "IsAutoAdjustField": "true",
      "InterationFlags": "",
      "IgnoreInterationFlag": "",
      "IsControlPrecision": "false",
      "ValidateRepeatJson": "false",
      "IsAutoSubmitAndAudit": needAutoSubmitAndAudit ? "true" : "false",
      "Model": {
        // 任务编码
        "FTask": {
          "FCODE": task.taskCode || ""
        },
        // 添加项目编码 - 对于临时任务和耗费任务，使用空值
        "FProj": {
          "FCODE": (task.taskType === 'temp' || task.taskType === 'timecost') ? "" : (task.project || "")
        },
        "FPlanStartDate": task.plannedStartDate || task.startDate || "",
        "FYear": year,
        "FHBDate": task.date || "",
        "FPlanEndDate": task.plannedEndDate || task.endDate || "",
        "FMonth": month,
        "FPlanTime": parseFloat(task.plannedHours || 0),
        "FManHour": parseFloat(task.regularHours || 0),
        "FOverTime": parseFloat(task.overtimeHours || 0),
        "FFinishRate": task.progress || "0",
        "FIsFinish": isFinished,
        "FReport": task.taskContent || "",
        "FReportEmp": {
          "FNUMBER": userInfo.username || "10001"
        },
        // "FCreatorld": {
        //   "Fuserid": userId
        // },
        "FUserid": userId,
        "FIsProjRpt": task.isProjectLevel === true,
        "FDEPT": {
          "FNUMBER": departmentCode || ""
        },
        "FPOST": {
          "FNUMBER": positionCode || ""
        },
        "FRANK": {
          "FNUMBER": rankCode || ""
        },
        "FPlanWorkHours": parseFloat(task.plannedHours || 0),
        "F_RT": parseFloat(task.workDays || 0),
        "FOrgId": {
          "FNumber": task.organization || "100"
        },
        "FOtherTaskName": "",
        "FOtherTaskType": "",
        "FTmpTaskTypeId": {
            "FNUMBER": ""
        },
        "FBurnOffTypeId": {
            "FNUMBER": ""
        },
        "fistemptsk": "true",
      }
    }
  };
  
  // 如果是敏捷任务，添加FQuickTask字段
  if (isAgileTask) {
    requestData.data.Model.FQuickTask = {
      "FCODE": (task.taskType === 'temp' || task.taskType === 'timecost') ? "" : (task.project || "")
    };
  }
  
  // 记录最终的API请求结构
  console.log('[API] 工时报告保存请求的关键字段:', {
    FormId: requestData.FormId,
    FTask_FCODE: requestData.data.Model.FTask.FCODE,
    FProj_FCODE: requestData.data.Model.FProj.FCODE,
    FQuickTask: isAgileTask ? requestData.data.Model.FQuickTask.FCODE : '不适用(非敏捷任务)',
    FIsFinish: requestData.data.Model.FIsFinish,
    FCreatorld: requestData.data.Model.FCreatorld,
    isAgileTask: isAgileTask,
    isTimeCostTask: isTimeCostTask,
    isTempOrTimeCostTask: needAutoSubmitAndAudit,
    IsAutoSubmitAndAudit: requestData.data.IsAutoSubmitAndAudit,
    ValidateRepeatJson: requestData.data.ValidateRepeatJson,
    部门编码: requestData.data.Model.FDEPT.FNUMBER,
    岗位编码: requestData.data.Model.FPOST.FNUMBER,
    职级编码: requestData.data.Model.FRANK.FNUMBER,
    来源说明: '编码从用户岗位缓存数据中获取'
  });
  
  // 处理临时任务和耗费任务的特殊字段
  if (task.taskType === 'temp') {
    // 临时任务：FOtherTaskType=1，设置FOtherTaskName和FTmpTaskTypeId
    requestData.data.Model.FOtherTaskName = task.otherTaskName || task.taskName || '';
    requestData.data.Model.FOtherTaskType = '1'; // 1表示临时任务
    requestData.data.Model.FTmpTaskTypeId = {
      "FNUMBER": task.tmpTaskTypeId || '' // 临时任务类型ID
    };
    requestData.data.Model.FBurnOffTypeId = {
      "FNUMBER": ""
    };
    requestData.data.Model.fistemptsk = "true"; // 标记为临时任务
    console.log(`[API] 设置临时任务字段: FOtherTaskName="${requestData.data.Model.FOtherTaskName}", FOtherTaskType=1, fistemptsk=true, IsAutoSubmitAndAudit=${requestData.data.IsAutoSubmitAndAudit}`);
  } else if (task.taskType === 'timecost') {
    // 耗费任务：FOtherTaskType=2，设置FOtherTaskName和FBurnOffTypeId
    requestData.data.Model.FOtherTaskName = task.otherTaskName || task.taskName || '';
    requestData.data.Model.FOtherTaskType = '2'; // 2表示耗费任务
    requestData.data.Model.FTmpTaskTypeId = {
      "FNUMBER": ""
    };
    requestData.data.Model.FBurnOffTypeId = {
      "FNUMBER": task.burnOffTypeId || '' // 耗费任务类型ID
    };
    requestData.data.Model.fistemptsk = "true"; // 耗费任务也设置为true
    console.log(`[API] 设置耗费任务字段: FOtherTaskName="${requestData.data.Model.FOtherTaskName}", FOtherTaskType=2, fistemptsk=true, IsAutoSubmitAndAudit=${requestData.data.IsAutoSubmitAndAudit}`);
  } else {
    // 普通任务：清空这些字段
    requestData.data.Model.FOtherTaskName = '';
    requestData.data.Model.FOtherTaskType = '';
    requestData.data.Model.FTmpTaskTypeId = {
      "FNUMBER": ""
    };
    requestData.data.Model.FBurnOffTypeId = {
      "FNUMBER": ""
    };
    requestData.data.Model.fistemptsk = "false"; // 普通任务不是临时任务
    console.log(`[API] 设置普通任务字段: taskType="${task.taskType || 'normal'}", fistemptsk=false, IsAutoSubmitAndAudit=${requestData.data.IsAutoSubmitAndAudit}`);
  }
  
  return requestData;
}

/**
 * 提交工时报告数据
 * @param {Array} tasks 任务数据数组
 * @returns {Promise} 返回包含提交结果的Promise
 */
export function submitWorkReportData(tasks) {
  // 提交成功后，清除缓存（已提交的数据应该从服务器获取最新版本）
  if (tasks && tasks.length > 0) {
    const firstTask = tasks[0];
    if (firstTask.date) {
      // 从任务中提取日期，从work_report_user中提取用户账号
      const date = firstTask.date;
      // 修改为从work_report_user获取username
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const username = userInfo.username || '';
      
      if (username) {
        // 计算缓存键并清除缓存
        const cacheKey = getWorkReportCacheKey(date, username);
        removeCache(cacheKey);
        console.log(`[API] 已清除工时报告缓存(提交后): ${date}, 用户账号: ${username}`);
      }
    }
  }
  
  // 如果任务数组为空，直接返回
  if (!tasks || tasks.length === 0) {
    return Promise.resolve({
      success: false,
      message: '无任务数据可提交'
    });
  }
  
  // 记录原始任务数量
  const originalTasksCount = tasks.length;
  
  // 过滤出状态为"暂存"的任务
  const submittableTasks = tasks.filter(task => {
    // 将状态码转为小写进行比较
    const status = (task.status || '').toLowerCase();
    return status === 'a' || status === 'd'; // 提交状态为"暂存"或"重新审核"的任务
  });
  
  console.log(`[API] 过滤后可提交的任务数量: ${submittableTasks.length}/${originalTasksCount}`);
  
  // 如果没有可提交的任务，返回错误消息
  if (submittableTasks.length === 0) {
    return Promise.resolve({
      success: false,
      message: '没有可提交的任务，所有任务已提交或已审核'
    });
  }

  // 准备提交请求的数据（使用过滤后的任务列表）
  const apiRequestData = prepareWorkReportSubmitData(submittableTasks);
  
  console.log('[API] 工时报告提交请求数据:', JSON.stringify(apiRequestData, null, 2));
  
  // 调用API提交数据
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc',
    data: apiRequestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 工时报告提交响应:', JSON.stringify(response.data, null, 2));
    
    // 检查API响应
    if (response.data && response.data.Result && response.data.Result.ResponseStatus) {
      const status = response.data.Result.ResponseStatus;
      
      if (status.IsSuccess) {
        return {
          success: true,
          message: '数据已提交',
          data: response.data
        };
      } else {
        // API返回错误
        const errors = status.Errors || [];
        const errorMsg = errors.length > 0 ? 
          errors.map(e => e.Message).join('; ') : 
          '提交失败，未知错误';
        
        console.error('[API] 工时报告提交失败:', errorMsg);
        
        return {
          success: false,
          message: errorMsg,
          data: response.data
        };
      }
    } else {
      // 响应格式不符合预期
      console.error('[API] 工时报告提交响应格式不符合预期:', response.data);
      
      return {
        success: false,
        message: '服务器响应格式异常',
        data: response.data
      };
    }
  })
  .catch(error => {
    // 处理网络错误等
    console.error('[API] 工时报告提交请求失败:', error.message);
    
    let errorMessage = '提交失败: ';
    
    if (error.response) {
      // 服务器返回了错误响应
      errorMessage += `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
    } else if (error.request) {
      // 请求已发送但没有收到响应
      errorMessage += '服务器无响应';
    } else {
      // 请求设置出错
      errorMessage += error.message;
    }
    
    return {
      success: false,
      message: errorMessage,
      error: error
    };
  });
}

/**
 * 准备工时报告提交数据，将前端任务数据转换为API需要的格式
 * @param {Array} tasks 前端任务数组
 * @returns {Object} 符合API要求的提交请求数据
 */
function prepareWorkReportSubmitData(tasks) {
  // 从任务中提取FBillno（单据编号）
  let billNoArray = [];
  
  if (tasks && tasks.length > 0) {
    tasks.forEach(task => {
      if (task.fbillno && !billNoArray.includes(task.fbillno)) {
        billNoArray.push(task.fbillno);
      }
    });
  }
  
  // 记录岗位信息，用于调试
  if (tasks && tasks.length > 0) {
    console.log('[API] 提交任务的岗位信息:');
    tasks.forEach((task, index) => {
      console.log(`[API] 任务 ${index + 1}/${tasks.length}: ` +
        `岗位编号=${task.position || '未设置'}, ` +
        `岗位名称=${task.positionName || '未设置'}, ` +
        `单据编号=${task.fbillno || '未设置'}`);
    });
  }
  
  console.log('[API] 提交数据 - 提取的单据编号:', billNoArray);
  
  // 构建API请求数据
  const requestData = {
    "FormId":"PBEW_GSHB",
    "data":{
      "CreateOrgId": 0,
      "Numbers": billNoArray,
      "Ids": "",
      "SelectedPostId": 0,
      "UseOrgId": 0,
      "NetworkCtrl": "",
      "IgnoreInterationFlag": ""
    }
  };
  
  return requestData;
}

/**
 * 检查API服务可用性
 * @returns {Promise<boolean>} 服务是否可用
 */
export function checkApiAvailability() {
  return new Promise((resolve) => {
    const timeoutId = setTimeout(() => {
      console.log('[API] 检查API可用性超时');
      resolve(false);
    }, 5000);
    
    axios.get('http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CheckConnect.common.kdsvc', {
      timeout: 5000
    })
      .then(() => {
        clearTimeout(timeoutId);
        console.log('[API] API服务可用');
        resolve(true);
      })
      .catch(err => {
        clearTimeout(timeoutId);
        console.log('[API] API服务不可用:', err.message);
        resolve(false);
      });
  });
}

/**
 * 清除指定日期的工时报告缓存
 * @param {string} reportDate 报告日期 YYYY-MM-DD
 * @param {string} creatorAccount 创建者账号（用户名）
 */
export function clearWorkReportCache(reportDate, creatorAccount) {
  const cacheKey = getWorkReportCacheKey(reportDate, creatorAccount);
  removeCache(cacheKey);
  console.log(`[API] 已清除工时报告缓存: ${reportDate}, 用户账号: ${creatorAccount}`);
}

/**
 * 获取项目和任务数据
 * @param {string} userId 用户ID (Fid)
 * @param {Object} options 可选配置项
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认4小时（项目任务数据可能变更）
 * @returns {Promise} 返回包含项目和任务数据的Promise
 */
export function getProjectAndTaskData(userId, options = {}) {
  const forceRefresh = options.forceRefresh || false;
  const cacheExpiry = options.cacheExpiry || 4 * 60 * 60 * 1000; // 默认4小时（项目任务数据可能变更）
  
  // 缓存键
  const cacheKey = `project_task_data_${userId}`;
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的项目任务数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除项目任务缓存`);
  }
  
  // 构建请求参数
  const requestData = {
    parameter: {
      data: {
        userId: userId
      }
    }
  };
  
  console.log(`[API] 开始获取项目和任务数据，用户ID: ${userId}`);
  console.log(`[API] 请求参数:`, JSON.stringify(requestData, null, 2));
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.Get,JR.K3.PLM.WorkhourReport.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    console.log('[API] 获取项目和任务数据成功');
    
    // 添加代码记录原始JSON响应数据
    console.log('[API] 项目和任务查询 - 原始JSON响应:', JSON.stringify(response.data, null, 2));
    
    // 特别记录data数组（如果存在）
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      console.log('[API] 项目和任务查询 - 原始data数组结构 (前3条):', JSON.stringify(response.data.data.slice(0, 3), null, 2));
      console.log('[API] 项目和任务查询 - 完整data数组结构:', JSON.stringify(response.data.data, null, 2));
    } else if (response.data && Array.isArray(response.data)) {
      console.log('[API] 项目和任务查询 - 原始响应是数组结构 (前3条):', JSON.stringify(response.data.slice(0, 3), null, 2));
      console.log('[API] 项目和任务查询 - 完整响应数组结构:', JSON.stringify(response.data, null, 2));
    } else {
      console.log('[API] 项目和任务查询 - 无法识别的数据格式:', typeof response.data);
    }
    
    // 获取响应数据 - 处理嵌套的data结构
    let apiData;
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      // 新的API响应格式: {data: [...]}
      apiData = response.data.data;
      console.log('[API] 检测到嵌套data结构，已正确提取数据数组');
    } else if (response.data && Array.isArray(response.data)) {
      // 旧的API响应格式: [...]
      apiData = response.data;
      console.log('[API] 直接使用响应中的数据数组');
    } else {
      // 未知格式或空数据
      apiData = [];
      console.warn('[API] 无法识别的数据格式或空数据:', response.data);
    }
    
    // 打印获取到的数据信息
    if (apiData && apiData.length) {
      console.log('[API] 获取到的数据项数量:', apiData.length);
      
      // 分析数据结构
      console.log('[API] 数据结构分析:');
      
      // 计算项目和任务数量
      let projectCount = 0;
      let taskCount = 0;
      let agileTaskCount = 0; // 添加敏捷任务计数
      
      apiData.forEach(item => {
        if (item.FCATEGORY === 'project' || item.FCATEGORY === '项目') {
          projectCount++;
        } else if (item.FCATEGORY === 'task' || item.FCATEGORY === '任务') {
          taskCount++;
        } else if (item.FCATEGORY === 'agile_task' || item.FCATEGORY === '敏捷任务') {
          // 新增敏捷任务类型识别
          agileTaskCount++;
        }
      });
      
      // 总任务数 = 常规任务 + 敏捷任务
      const totalTasks = taskCount + agileTaskCount;
      console.log(`[API] 初步统计: 项目数量=${projectCount}, 总任务数量=${totalTasks} (常规任务=${taskCount}, 敏捷任务=${agileTaskCount})`);
      
      // 更详细地分析项目字段
      if (projectCount > 0) {
        // 找出第一个项目用于分析
        const firstProject = apiData.find(item => item.FCATEGORY === 'project' || item.FCATEGORY === '项目');
        if (firstProject) {
          console.log('[API] 项目字段分析:');
          console.log(`  FCODE (项目编码) = ${firstProject.FCODE || '未定义'}`);
          console.log(`  FNAME (项目名称) = ${firstProject.FNAME || '未定义'}`);
          console.log(`  FID (项目ID) = ${firstProject.FID || '未定义'}`);
          console.log(`  FOWNPROJECT (所属项目) = ${firstProject.FOWNPROJECT || '未定义'}`);
          console.log(`  FOWNPROJECTID (所属项目ID) = ${firstProject.FOWNPROJECTID || '未定义'}`);
          console.log(`  FPlanWorkHours (计划工时) = ${firstProject.FPlanWorkHours || '未定义'}`);
          console.log(`  FPLANNEDHOURS (计划工时) = ${firstProject.FPLANNEDHOURS || '未定义'}`);
          
          // 检查所有可能与所属项目相关的字段
          const projectOwnFields = Object.keys(firstProject).filter(key => 
            key.toLowerCase().includes('own') && 
            (key.toLowerCase().includes('project') || key.toLowerCase().includes('proj'))
          );
          
          if (projectOwnFields.length > 0) {
            console.log('[API] 项目中可能与所属项目相关的字段:');
            projectOwnFields.forEach(field => {
              console.log(`  ${field} = ${firstProject[field] || '未定义'} (类型: ${typeof firstProject[field]})`);
            });
          }
          
          // 输出所有字段和值，以便识别更多有用字段
          console.log(`[API] 项目所有字段:`, Object.entries(firstProject)
            .map(([key, value]) => `${key}=${value || '空'}`)
            .join(', '));
        }
      }
      
      // 更详细地分析任务字段
      if (taskCount > 0 || agileTaskCount > 0) {
        // 找出第一个任务用于分析（优先查找常规任务，如果没有则查找敏捷任务）
        const firstTask = apiData.find(item => item.FCATEGORY === 'task' || item.FCATEGORY === '任务') || 
                          apiData.find(item => item.FCATEGORY === 'agile_task' || item.FCATEGORY === '敏捷任务');
        if (firstTask) {
          console.log('[API] 任务字段分析:');
          console.log(`  FCODE (任务编码) = ${firstTask.FCODE || '未定义'}`);
          console.log(`  FNAME (任务名称) = ${firstTask.FNAME || '未定义'}`);
          console.log(`  FOWNPROJECTID (任务所属项目ID) = ${firstTask.FOWNPROJECTID || '未定义'}`);
          console.log(`  FOWNPROJECT (任务所属项目) = ${firstTask.FOWNPROJECT || '未定义'}`);
          console.log(`  FPlanWorkHours (计划工时) = ${firstTask.FPlanWorkHours || '未定义'}`);
          console.log(`  FPLANNEDHOURS (计划工时) = ${firstTask.FPLANNEDHOURS || '未定义'}`);
          
          // 记录任务计划日期字段的值
          if (firstTask.FPLANNEDST || firstTask.FPLANNEDED) {
            console.log(`[API] 任务"${firstTask.FNAME}"计划日期: FPLANNEDST="${firstTask.FPLANNEDST || '无'}", FPLANNEDED="${firstTask.FPLANNEDED || '无'}"`);
          }
          
          // 单独检查FOWNPROJECT字段
          if (firstTask.FOWNPROJECT) {
            console.log(`[API] 发现FOWNPROJECT字段值: "${firstTask.FOWNPROJECT}"`);
            
            // 尝试与项目名称匹配
            if (projectCount > 0) {
              const projectNames = apiData
                .filter(item => (item.FCATEGORY === 'project' || item.FCATEGORY === '项目') && item.FNAME)
                .map(item => item.FNAME);
              
              const matchingProjects = projectNames.filter(projName => 
                firstTask.FOWNPROJECT === projName || 
                firstTask.FOWNPROJECT.includes(projName) ||
                projName.includes(firstTask.FOWNPROJECT)
              );
              
              if (matchingProjects.length > 0) {
                console.log(`[API] FOWNPROJECT字段值"${firstTask.FOWNPROJECT}"与以下项目名称匹配:`, matchingProjects.join(', '));
              } else {
                console.log(`[API] FOWNPROJECT字段值"${firstTask.FOWNPROJECT}"未与任何项目名称匹配`);
              }
            }
          }
          
          // 检查所有可能与项目相关的字段
          const possibleProjectFields = Object.keys(firstTask).filter(key => 
            key.toLowerCase().includes('project') || 
            key.toLowerCase().includes('proj')
          );
          
          if (possibleProjectFields.length > 0) {
            console.log('[API] 任务中可能与项目相关的字段:');
            possibleProjectFields.forEach(field => {
              console.log(`  ${field} = ${firstTask[field] || '未定义'} (类型: ${typeof firstTask[field]})`);
            });
            
            // 尝试识别最有可能包含项目名称的字段
            console.log('[API] 尝试识别项目名称字段...');
            const projectNameMatches = [];
            
            if (projectCount > 0) {
              // 获取所有项目名称作为参考
              const projectNames = apiData
                .filter(item => (item.FCATEGORY === 'project' || item.FCATEGORY === '项目') && item.FNAME)
                .map(item => item.FNAME);
              
              // 检查任务的每个可能字段是否包含项目名称
              possibleProjectFields.forEach(field => {
                const fieldValue = firstTask[field];
                if (fieldValue && typeof fieldValue === 'string') {
                  const matchingProjects = projectNames.filter(projName => 
                    fieldValue === projName || 
                    fieldValue.includes(projName) ||
                    projName.includes(fieldValue)
                  );
                  
                  if (matchingProjects.length > 0) {
                    projectNameMatches.push({
                      field,
                      value: fieldValue,
                      matchingProjects,
                      confidence: matchingProjects.length / projectNames.length
                    });
                  }
                }
              });
            }
            
            if (projectNameMatches.length > 0) {
              // 按置信度排序
              projectNameMatches.sort((a, b) => b.confidence - a.confidence);
              console.log('[API] 可能包含项目名称的字段 (按匹配度排序):', 
                projectNameMatches.map(match => 
                  `${match.field} (匹配 ${match.matchingProjects.length} 个项目)`
                ).join(', ')
              );
            } else {
              console.log('[API] 未能在任务字段中找到明确匹配项目名称的字段');
            }
          }
          
          // 记录任务计划工时字段的值
          console.log(`[API] 任务"${firstTask.FNAME}"计划工时字段值: FPlanWorkHours="${firstTask.FPlanWorkHours || '无'}", FPLANNEDHOURS="${firstTask.FPLANNEDHOURS || '无'}"`);
          
          // 输出所有字段和值，以便识别更多有用字段
          console.log(`[API] 任务所有字段:`, Object.entries(firstTask)
            .map(([key, value]) => `${key}=${value || '空'}`)
            .join(', '));
        }
      }
      
      // 打印前3条数据作为示例
      console.log('[API] 数据样本（前3条）:');
      console.log(JSON.stringify(apiData.slice(0, 3), null, 2));
    } else {
      console.log('[API] 获取到的数据为空或不是数组:', apiData);
    }
    
    // 格式化数据
    const formattedData = formatProjectAndTaskData(apiData);
    
    // 打印格式化后的数据结构
    console.log('[API] 格式化后的数据结构:');
    console.log(JSON.stringify(formattedData, null, 2));
    
    // 缓存数据
    setCache(cacheKey, formattedData, cacheExpiry);
    console.log(`[API] 项目任务数据已缓存，有效期: ${(cacheExpiry / 3600000).toFixed(1)}小时`);
    
    return formattedData;
  })
  .catch(error => {
    console.error('[API] 获取项目和任务数据失败:', error.message);
    
    if (error.response) {
      console.error('[API] 错误响应状态码:', error.response.status);
      console.error('[API] 错误响应数据:', error.response.data);
    }
    
    // 尝试从缓存加载过期数据作为后备
    const expiredCache = localStorage.getItem(cacheKey);
    if (expiredCache) {
      try {
        const cacheData = JSON.parse(expiredCache).data;
        console.warn(`[API] 使用过期缓存作为后备数据`);
        showToast({
          message: '使用离线缓存数据（可能不是最新）',
          type: 'warning',
          duration: 3000
        });
        return cacheData;
      } catch (e) {
        throw new Error(`获取项目和任务数据失败: ${error.message}`);
      }
    }
    
    throw new Error(`获取项目和任务数据失败: ${error.message}`);
  });
}

/**
 * 格式化项目和任务数据
 * @param {Array} apiData API返回的原始数据
 * @returns {Object} 格式化后的项目和任务数据
 */
function formatProjectAndTaskData(apiData) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化项目任务数据: 无数据');
    return {
      success: false,
      message: "无项目和任务数据",
      data: {
        projectOptions: [],
        projectTaskMap: {}
      }
    };
  }
  
  try {
    console.log('[API] 开始格式化项目和任务数据, 总项目数:', apiData.length);
    
    // 添加完整的原始数据日志
    console.log('[API] 格式化前的完整原始数据 (项目和任务):', JSON.stringify(apiData, null, 2));
    
    // 检查第一条数据结构，输出字段列表供参考
    if (apiData.length > 0) {
      const firstItem = apiData[0];
      console.log('[API] 第一条数据的字段列表:', Object.keys(firstItem).join(', '));
      console.log('[API] 第一条数据详情:', JSON.stringify(firstItem, null, 2));
    }
    
    // 项目列表
    const projectOptions = [];
    // 项目任务映射
    const projectTaskMap = {};
    
    // 项目名称到代码的映射（作为主要映射）
    const projectNameToCodeMap = {};
    
    // 项目名称大小写不敏感映射
    const projectNameLowercaseMap = {};
    
    // 项目ID到名称的映射（作为备用映射）
    const projectIdToNameMap = {};
    
    // 创建一个默认项目，用于存放无法关联到项目的任务
    const defaultProjectCode = 'unassigned_tasks';
    const defaultProjectName = '未分类任务';
    let hasDefaultProject = false;
    
    // 第一遍遍历，先收集所有项目
    let projectCount = 0;
    apiData.forEach(item => {
      // 如果是项目类型
      if (item.FCATEGORY === 'project' || item.FCATEGORY === '项目') {
        projectCount++;
        
        const projectCode = item.FCODE || '';
        const projectName = item.FNAME || '';
        const projectOwnProject = item.FOWNPROJECT || ''; // 所属上级项目名称
        const projectOwnProjectId = item.FOWNPROJECTID || ''; // 所属上级项目ID
        
        if (!projectName) {
          console.warn(`[API] 警告: 项目没有名称, FCODE=${projectCode}`);
          return; // 跳过没有名称的项目
        }
        
        // 记录详细信息用于调试
        console.log(`[API] 检测到项目 #${projectCount}: 代码="${projectCode}", 名称="${projectName}", 所属项目="${projectOwnProject}"`);
        console.log(`[API] 项目ID字段: FPROJECTID="${item.FPROJECTID || '无'}", FTASKID="${item.FTASKID || '无'}"`);

        // 记录计划日期字段的值
        if (item.FPLANNEDST || item.FPLANNEDED) {
          console.log(`[API] 项目计划日期: FPLANNEDST="${item.FPLANNEDST || '无'}", FPLANNEDED="${item.FPLANNEDED || '无'}"`);
        }

        // 记录项目计划工时字段的值
        console.log(`[API] 项目"${projectName}"计划工时字段值: FPlanWorkHours="${item.FPlanWorkHours || '无'}", FPLANNEDHOURS="${item.FPLANNEDHOURS || '无'}"`);

        const projectOption = {
          text: projectName,
          value: projectCode,
          description: item.FDESCRIPTION || '',
          manager: item.FMANAGER || '',
          startDate: item.FSTARTDATE || '',
          endDate: item.FENDDATE || '',
          // 添加计划日期字段
          plannedStartDate: item.FPLANNEDST || '',
          plannedEndDate: item.FPLANNEDED || '',
          // 添加计划工时字段
          plannedHours: parseFloat(item.FPlanWorkHours || item.FPLANNEDHOURS || 0),
          id: item.FID || '',
          ownProject: projectOwnProject, // 添加所属项目字段
          ownProjectId: projectOwnProjectId, // 添加所属项目ID字段
          // 添加用于已报工时查询的关键字段
          fprojectId: item.FPROJECTID || '', // 项目ID，用于已报工时查询
          ftaskId: item.FTASKID || 0 // 任务ID，项目级别工作为0
        };
        
        projectOptions.push(projectOption);
        
        // 初始化项目的任务列表
        projectTaskMap[projectOption.value] = [];
        
        // 添加到名称-代码映射（主要映射）
        if (projectName) {
          projectNameToCodeMap[projectName] = projectCode;
          
          // 添加到小写映射，便于大小写不敏感匹配
          projectNameLowercaseMap[projectName.toLowerCase()] = {
            originalName: projectName,
            code: projectCode
          };
        }
        
        // 添加到ID-名称映射（备用映射）
        if (item.FID) {
          projectIdToNameMap[item.FID] = {
            name: projectName,
            code: projectCode
          };
        }
      }
    });
    
    console.log(`[API] 成功收集 ${projectCount} 个项目`);
    console.log('[API] 项目名称到代码的映射示例:', Object.entries(projectNameToCodeMap).slice(0, 3));
    
    // 辅助函数：尝试通过多种方式查找项目的代码
    const findProjectCode = (projectName, projectId) => {
      // 不存在任何有效关联信息时，返回null
      if (!projectName && !projectId) {
        return null;
      }
      
      // 尝试直接用项目名称匹配（最精确的方式）
      if (projectName && projectNameToCodeMap[projectName]) {
        return { code: projectNameToCodeMap[projectName], name: projectName, matchType: 'exact-name' };
      }
      
      // 尝试不区分大小写匹配
      if (projectName && projectNameLowercaseMap[projectName.toLowerCase()]) {
        const match = projectNameLowercaseMap[projectName.toLowerCase()];
        return { code: match.code, name: match.originalName, matchType: 'case-insensitive-name' };
      }
      
      // 尝试前缀匹配（如"项目A - 阶段1"可能匹配"项目A"）
      if (projectName) {
        for (const [name, code] of Object.entries(projectNameToCodeMap)) {
          if (projectName.startsWith(name + ' ') || name.startsWith(projectName + ' ') ||
              projectName.includes(name) || name.includes(projectName)) {
            return { code: code, name: name, matchType: 'fuzzy-name-match' };
          }
        }
      }
      
      // 尝试使用项目ID
      if (projectId && projectIdToNameMap[projectId]) {
        const info = projectIdToNameMap[projectId];
        return { code: info.code, name: info.name, matchType: 'id' };
      }
      
      // 所有尝试都失败
      return null;
    };
    
    // 添加默认项目（如果有未分类任务）
    const addDefaultProject = () => {
      if (!hasDefaultProject) {
        const defaultProject = {
          text: defaultProjectName,
          value: defaultProjectCode,
          description: '系统自动创建，用于存放未关联到项目的任务',
          manager: '',
          startDate: '',
          endDate: '',
          id: 'default_project_id'
        };
        
        projectOptions.push(defaultProject);
        projectTaskMap[defaultProjectCode] = [];
        hasDefaultProject = true;
        console.log('[API] 已创建默认项目用于存放未分类任务');
      }
    };
    
    // 第二遍遍历，收集所有任务
    let taskCount = 0;
    let agileTaskCount = 0;
    let unmappedTaskCount = 0;
    let mappingStats = {
      'exact-name': 0,
      'case-insensitive-name': 0,
      'fuzzy-name-match': 0,
      'id': 0,
      'unassigned': 0,
      'ownproject': 0,
      'agile-task': 0  // 添加敏捷任务统计
    };
    
    apiData.forEach(item => {
      // 如果是任务类型（包括普通任务和敏捷任务）
      if (item.FCATEGORY === 'task' || item.FCATEGORY === '任务' || item.FCATEGORY === 'agile_task' || item.FCATEGORY === '敏捷任务') {
        // 区分普通任务和敏捷任务
        const isAgileTask = (item.FCATEGORY === 'agile_task' || item.FCATEGORY === '敏捷任务');
        
        if (isAgileTask) {
          agileTaskCount++;
          // 添加敏捷任务专用日志
          console.log(`[API] 发现敏捷任务: FCATEGORY="${item.FCATEGORY}", FCODE="${item.FCODE || '无'}", FNAME="${item.FNAME || '无'}"`);
          console.log(`[API] 敏捷任务FOWNPROJECT字段值: "${item.FOWNPROJECT || '无'}", FOWNPROJECTID: "${item.FOWNPROJECTID || '无'}"`);
        } else {
          taskCount++;
        }
        
        const projectId = item.FOWNPROJECTID;
        const taskName = item.FNAME || '';
        const taskCode = item.FCODE || '';
        // 任务类型标识
        const taskType = isAgileTask ? 'agile' : 'normal';
        
        // 记录任务计划日期字段的值
        if (item.FPLANNEDST || item.FPLANNEDED) {
          console.log(`[API] 任务"${taskName}"计划日期: FPLANNEDST="${item.FPLANNEDST || '无'}", FPLANNEDED="${item.FPLANNEDED || '无'}"`);
        }
        
        // 检查是否存在FOWNPROJECT字段，并且是否有值
        const ownProject = item.FOWNPROJECT || '';
        let projectCode;
        let projectName;
        let isMapped = false;
        let matchType = '';
        let taskProjectName = '';  // 提升变量声明到更高作用域，初始化为空字符串
        
        // 先尝试通过FOWNPROJECT直接关联
        if (ownProject && projectNameToCodeMap[ownProject]) {
          // 如果FOWNPROJECT存在且能直接匹配到项目名称
          projectCode = projectNameToCodeMap[ownProject];
          projectName = ownProject;
          matchType = 'ownproject';
          taskProjectName = ownProject;  // 在此分支中设置taskProjectName
          mappingStats['ownproject']++;
          
          if (isAgileTask) {
            console.log(`[API] 敏捷任务"${taskName}"通过FOWNPROJECT字段直接关联到项目"${projectName}"`);
          } else {
            console.log(`[API] 任务"${taskName}"通过FOWNPROJECT字段直接关联到项目"${projectName}"`);
          }
          
          isMapped = true;
        } else {
          // 如果FOWNPROJECT不存在或无法直接匹配，则使用其他逻辑
          // 尝试获取多个可能的项目名称字段
          const possibleProjectNames = [
            item.FOWNPROJECT, // 仍然将FOWNPROJECT作为可能的字段，但可能需要模糊匹配
            item.FPROJECTNAME,
            item.PROJECTFNAME,
            item.PROJNAME,
            item.FPROJECT_NAME,
            item.FPROJECT,
            item.FPROJ,
            item.PROJECTNAME,
          ].filter(Boolean); // 过滤掉空值
          
          // 使用找到的第一个非空项目名称
          taskProjectName = possibleProjectNames[0] || '';  // 在此分支中设置taskProjectName
          
          console.log(`[API] 检测到任务 #${taskCount}: 代码="${taskCode}", 名称="${taskName}", 可能的项目名称=${possibleProjectNames.join(', ')}`);
          
          // 使用辅助函数查找项目
          const projectInfo = findProjectCode(taskProjectName, projectId);
          
          if (projectInfo) {
            projectCode = projectInfo.code;
            projectName = projectInfo.name;
            matchType = projectInfo.matchType;
            mappingStats[matchType]++;
            
            if (isAgileTask) {
              console.log(`[API] 敏捷任务"${taskName}"通过${matchType}方式关联到项目"${projectName}"`);
              if (ownProject && ownProject !== projectName) {
                console.warn(`[API] 警告: 敏捷任务"${taskName}"的FOWNPROJECT值"${ownProject}"与映射的项目"${projectName}"不一致`);
              }
            } else {
              console.log(`[API] 任务"${taskName}"通过${matchType}方式关联到项目"${projectName}"`);
            }
            
            isMapped = true;
          }
          // 如果所有尝试都失败，将任务归入未分类项目
          else {
            // 只有当任务名称非空时才添加到未分类项目，避免垃圾数据
            if (taskName) {
              addDefaultProject();
              projectCode = defaultProjectCode;
              projectName = defaultProjectName;
              mappingStats['unassigned']++;
              console.log(`[API] 任务"${taskName}"无有效项目关联信息，已分配到默认项目"${defaultProjectName}"`);
              isMapped = true;
            } else {
              unmappedTaskCount++;
              console.warn(`[API] 警告: 任务没有名称，且无法找到项目关联，将被忽略`);
              isMapped = false;
            }
          }
        }
        
        // 如果成功映射到项目，创建任务对象
        if (isMapped && projectCode) {
          // 记录任务计划工时字段的值
          console.log(`[API] 任务"${taskName}"计划工时字段值: FPlanWorkHours="${item.FPlanWorkHours || '无'}", FPLANNEDHOURS="${item.FPLANNEDHOURS || '无'}"`);
          console.log(`[API] 任务"${taskName}"ID字段: FPROJECTID="${item.FPROJECTID || '无'}", FTASKID="${item.FTASKID || '无'}"`);

          // 创建任务对象
          const taskOption = {
            text: taskName,
            value: taskCode,
            description: item.FDESCRIPTION || '',
            estimatedHours: parseFloat(item.FESTIMATEDHOURS) || 0,
            plannedHours: parseFloat(item.FPlanWorkHours || item.FPLANNEDHOURS || 0),
            startDate: item.FSTARTDATE || '',
            endDate: item.FENDDATE || '',
            // 修正计划日期字段映射，使用正确的API字段名
            plannedStartDate: item.FPLANNEDST || item.FSTARTDATE || '',
            plannedEndDate: item.FPLANNEDED || item.FENDDATE || '',
            priority: item.FPRIORITY || '中',
            projectName: projectName,
            // 保存原始项目关联信息
            projectId: projectId || '',
            originalProjectName: taskProjectName || '',
            ownProject: item.FOWNPROJECT || '', // 保存FOWNPROJECT原始值
            matchType: matchType,  // 记录匹配方式
            taskType: taskType,    // 添加任务类型字段，区分普通任务和敏捷任务
            // 添加用于已报工时查询的关键字段
            fprojectId: item.FPROJECTID || '', // 项目ID，用于已报工时查询
            ftaskId: item.FTASKID || 0, // 任务ID，用于已报工时查询
            // 添加已报工时和进度字段
            reportedHours: parseFloat(item.FTOTTIME) || 0, // 已报工时
            progress: parseFloat(item.FPRECENTCOMPLETE || item.FPERCENTCOMPLETE) || 0 // 任务进度
          };
          
          // 将任务添加到对应项目的任务列表
          if (projectTaskMap[projectCode]) {
            projectTaskMap[projectCode].push(taskOption);
            
            if (isAgileTask) {
              console.log(`[API] 已将敏捷任务 "${taskName}" 添加到项目 "${projectName}", 原始FOWNPROJECT="${item.FOWNPROJECT || '无'}"`);
              mappingStats['agile-task']++;
            } else {
              console.log(`[API] 已将普通任务 "${taskName}" 添加到项目 "${projectName}"`);
            }
          } else {
            console.warn(`[API] 警告: 无法找到项目代码为 "${projectCode}" 的任务列表, ${isAgileTask ? '敏捷' : '普通'}任务 "${taskName}" 将被忽略`);
            unmappedTaskCount++;
          }
        } else if (isAgileTask) {
          console.warn(`[API] 警告: 敏捷任务"${taskName}"无法映射到任何项目, FOWNPROJECT="${ownProject || '无'}"`);
        }
      }
    });
    
    console.log(`[API] 成功收集 ${taskCount + agileTaskCount} 个任务，其中:`);
    console.log(`  - ${mappingStats['ownproject']} 个任务通过FOWNPROJECT字段直接关联`);
    console.log(`  - ${mappingStats['exact-name']} 个任务通过精确项目名称关联`);
    console.log(`  - ${mappingStats['case-insensitive-name']} 个任务通过大小写不敏感项目名称关联`);
    console.log(`  - ${mappingStats['fuzzy-name-match']} 个任务通过模糊项目名称匹配关联`);
    console.log(`  - ${mappingStats['id']} 个任务通过项目ID关联`);
    console.log(`  - ${mappingStats['unassigned']} 个任务分配到未分类项目`);
    console.log(`  - ${mappingStats['agile-task']} 个敏捷任务成功映射`);
    console.log(`  - ${unmappedTaskCount} 个任务因无法映射到项目而被忽略`);
    
    // 移除未使用的默认项目
    if (hasDefaultProject && projectTaskMap[defaultProjectCode].length === 0) {
      // 如果默认项目中没有任务，则移除它
      const defaultIndex = projectOptions.findIndex(p => p.value === defaultProjectCode);
      if (defaultIndex !== -1) {
        projectOptions.splice(defaultIndex, 1);
        delete projectTaskMap[defaultProjectCode];
        console.log('[API] 移除了未使用的默认项目');
      }
    }
    
    // 输出最终收集的项目和任务数量
    let mappedTaskCount = 0;
    Object.values(projectTaskMap).forEach(tasks => {
      mappedTaskCount += tasks.length;
    });
    
    // 统计含有FOWNPROJECT字段的项目和任务数量
    const projectsWithOwnProject = projectOptions.filter(p => p.ownProject).length;
    
    console.log(`[API] 格式化完成: ${projectOptions.length} 个项目, ${mappedTaskCount} 个任务被成功映射`);
    console.log(`[API] FOWNPROJECT字段统计: ${projectsWithOwnProject} 个项目有所属项目, ${mappingStats['ownproject']} 个任务通过FOWNPROJECT直接关联`);
    
    // 如果项目数量为0，可能是API返回的数据结构有问题
    if (projectOptions.length === 0) {
      console.warn('[API] 警告: 未解析到任何项目，可能是API返回的数据格式不符合预期');
      // 打印数据结构以帮助调试
      if (apiData.length > 0) {
        console.log('[API] API数据第一条记录结构:', JSON.stringify(apiData[0], null, 2));
      }
    }
    
    return {
      success: true,
      message: "成功获取项目和任务数据",
      data: {
        projectOptions: projectOptions,
        projectTaskMap: projectTaskMap
      }
    };
  } catch (error) {
    console.error('[API] 格式化项目和任务数据出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化数据出错: ${error.message}`,
      data: {
        projectOptions: [],
        projectTaskMap: {}
      }
    };
  }
}

/**
 * 获取岗位数据
 * @param {string} username 用户账号，用于过滤特定用户的岗位
 * @param {Object} options 可选配置项
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认24小时
 * @returns {Promise} 返回包含岗位数据的Promise
 */
export function getPositionData(username, options = {}) {
  const forceRefresh = options.forceRefresh || false;
  const cacheExpiry = options.cacheExpiry || 24 * 60 * 60 * 1000; // 默认24小时
  
  // 缓存键
  const cacheKey = `position_data_${username}`;
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的岗位数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除岗位数据缓存`);
  }
  
  // 构建请求参数
  const requestData = {
    data: {
      FormId: "BD_NEWSTAFF",
      FieldKeys: "FPosition.FNumber,FPosition.FName,FDept.FNumber,FDept.FName,FRank.FNumber,FRank.FName",
      FilterString: `FDocumentStatus='C' AND FForbidStatus='A' AND FNumber='${username}'`,
      OrderString: "",
      TopRowCount: 0,
      StartRow: 0,
      Limit: 2000,
      SubSystemId: ""
    }
  };
  
  console.log(`[API] 开始获取岗位数据，用户: ${username}`);
  const requestStartTime = Date.now();
  
  // 发送API请求
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    // 记录请求完成时间和耗时
    const requestEndTime = Date.now();
    const requestDuration = requestEndTime - requestStartTime;
    console.log(`[API] 获取岗位数据成功，耗时: ${requestDuration}ms`);
    
    const data = response.data;
    
    // 打印API原始响应数据
    console.log('[API] 岗位数据原始响应:', JSON.stringify(data, null, 2));
    
    // 处理API返回的数据
    let positionOptions = [];
    
    if (Array.isArray(data) && data.length > 0) {
      // 提取岗位数据
      positionOptions = data.map(item => {
        // 处理数组格式的返回数据
        if (Array.isArray(item)) {
          const positionNumber = item[0] || '';        // FPosition.FNumber
          const positionName = item[1] || '未知岗位';   // FPosition.FName
          
          return {
            text: positionName,                       // 显示用的岗位名称
            value: positionNumber || positionName,    // 用于数据传递，优先使用编号，没有则用名称
            positionNumber: positionNumber,           // 明确存储岗位编号
            positionName: positionName,               // 明确存储岗位名称
            deptNumber: item[2] || '',                // FDept.FNumber
            deptName: item[3] || '',                  // FDept.FName
            rankNumber: item[4] || '',                // FRank.FNumber
            rankName: item[5] || '',                   // FRank.FName
          };
        } 
        // 处理对象格式的返回数据
        else if (typeof item === 'object') {
          const positionNumber = item.FPosition?.FNumber || '';
          const positionName = item.FPosition?.FName || '未知岗位';
          
          return {
            text: positionName,
            value: positionNumber || positionName,
            positionNumber: positionNumber,
            positionName: positionName,
            deptNumber: item.FDept?.FNumber || '',
            deptName: item.FDept?.FName || '',
            rankNumber: item.FRank?.FNumber || '',
            rankName: item.FRank?.FName || ''
          };
        }
        return null;
      }).filter(Boolean); // 过滤掉null值
    }
    
    // 如果没有获取到岗位数据，使用默认岗位列表
    if (positionOptions.length === 0) {
      console.log('[API] 未获取到岗位数据，使用默认岗位列表');
      positionOptions = [
        { text: '开发工程师', value: 'DEV001', positionNumber: 'DEV001', positionName: '开发工程师' },
        { text: '测试工程师', value: 'TEST001', positionNumber: 'TEST001', positionName: '测试工程师' },
        { text: '产品经理', value: 'PM001', positionNumber: 'PM001', positionName: '产品经理' },
        { text: '项目经理', value: 'PJM001', positionNumber: 'PJM001', positionName: '项目经理' },
        { text: '设计师', value: 'DES001', positionNumber: 'DES001', positionName: '设计师' },
        { text: '运维工程师', value: 'OPS001', positionNumber: 'OPS001', positionName: '运维工程师' },
        { text: '技术支持', value: 'SUP001', positionNumber: 'SUP001', positionName: '技术支持' },
        { text: '行政人员', value: 'ADM001', positionNumber: 'ADM001', positionName: '行政人员' },
        { text: '其他', value: 'OTHER001', positionNumber: 'OTHER001', positionName: '其他' }
      ];
    }
    
    // 添加"其他"选项（如果API返回的数据中没有）
    if (!positionOptions.some(p => p.text === '其他')) {
      positionOptions.push({ 
        text: '其他', 
        value: 'OTHER001', 
        positionNumber: 'OTHER001', 
        positionName: '其他' 
      });
    }
    
    // 打印处理后的岗位选项
    console.log(`[API] 处理后的岗位选项数量: ${positionOptions.length}`);
    console.log('[API] 岗位选项示例:', positionOptions.slice(0, 3));
    
    // 缓存结果
    setCache(cacheKey, { success: true, data: positionOptions }, cacheExpiry);
    
    return {
      success: true,
      data: positionOptions
    };
  })
  .catch(error => {
    console.error('[API] 获取岗位数据失败:', error);
    
    // 使用默认岗位列表作为备份
    const defaultPositionOptions = [
      { text: '开发工程师', value: 'DEV001', positionNumber: 'DEV001', positionName: '开发工程师' },
      { text: '测试工程师', value: 'TEST001', positionNumber: 'TEST001', positionName: '测试工程师' },
      { text: '产品经理', value: 'PM001', positionNumber: 'PM001', positionName: '产品经理' },
      { text: '项目经理', value: 'PJM001', positionNumber: 'PJM001', positionName: '项目经理' },
      { text: '设计师', value: 'DES001', positionNumber: 'DES001', positionName: '设计师' },
      { text: '运维工程师', value: 'OPS001', positionNumber: 'OPS001', positionName: '运维工程师' },
      { text: '技术支持', value: 'SUP001', positionNumber: 'SUP001', positionName: '技术支持' },
      { text: '行政人员', value: 'ADM001', positionNumber: 'ADM001', positionName: '行政人员' },
      { text: '其他', value: 'OTHER001', positionNumber: 'OTHER001', positionName: '其他' }
    ];
    
    return {
      success: false,
      message: error.message || '获取岗位数据失败',
      data: defaultPositionOptions
    };
  });
}

/**
 * 敏捷任务FOWNPROJECT分析工具 - 用于调试敏捷任务与项目的关联关系
 * @param {Array} tasks 任务数组
 * @param {Object} projectOptions 项目选项
 * @returns {Object} 分析结果
 */
export function analyzeAgileTaskProjectMapping(tasks, projectOptions) {
  console.log('[API] 开始分析敏捷任务FOWNPROJECT映射...');
  
  if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
    console.warn('[API] 分析失败: 无任务数据');
    return { success: false, message: '无任务数据', data: { agileTasksCount: 0 } };
  }
  
  if (!projectOptions || !Array.isArray(projectOptions) || projectOptions.length === 0) {
    console.warn('[API] 分析失败: 无项目数据');
    return { success: false, message: '无项目数据', data: { agileTasksCount: 0 } };
  }
  
  // 过滤出敏捷任务
  const agileTasks = tasks.filter(task => task.taskType === 'agile');
  console.log(`[API] 找到 ${agileTasks.length} 个敏捷任务`);
  
  if (agileTasks.length === 0) {
    return { 
      success: true, 
      message: '没有敏捷任务', 
      data: { 
        agileTasksCount: 0,
        projectsCount: projectOptions.length
      } 
    };
  }
  
  // 创建项目映射（项目名称到项目代码）
  const projectNameToCode = {};
  projectOptions.forEach(project => {
    if (project.text) {
      projectNameToCode[project.text] = project.value;
    }
  });
  
  // 分析结果
  const result = {
    agileTasksCount: agileTasks.length,
    projectsCount: projectOptions.length,
    mappingStats: {
      withOwnProject: 0,
      withoutOwnProject: 0,
      matchingProject: 0,
      mismatchingProject: 0,
      unassigned: 0
    },
    detailedAnalysis: []
  };
  
  // 分析每个敏捷任务
  agileTasks.forEach(task => {
    const taskAnalysis = {
      taskName: task.taskName,
      taskId: task.id,
      hasOwnProject: Boolean(task.ownProject),
      ownProject: task.ownProject || '',
      currentProject: task.projectName || '',
      currentProjectCode: task.project || '',
      matchType: task.matchType || 'unknown',
      isMatch: false,
      issue: ''
    };
    
    if (task.ownProject) {
      result.mappingStats.withOwnProject++;
      
      // 检查FOWNPROJECT是否匹配当前项目
      if (task.projectName) {
        if (task.projectName === task.ownProject || 
            task.projectName.includes(task.ownProject) || 
            task.ownProject.includes(task.projectName)) {
          taskAnalysis.isMatch = true;
          result.mappingStats.matchingProject++;
        } else {
          result.mappingStats.mismatchingProject++;
          taskAnalysis.issue = `FOWNPROJECT值"${task.ownProject}"与项目名称"${task.projectName}"不匹配`;
        }
      } else {
        result.mappingStats.unassigned++;
        taskAnalysis.issue = '有FOWNPROJECT但无项目名称';
      }
    } else {
      result.mappingStats.withoutOwnProject++;
      taskAnalysis.issue = '无FOWNPROJECT值';
    }
    
    result.detailedAnalysis.push(taskAnalysis);
  });
  
  // 添加修复建议
  result.suggestions = [];
  
  // 如果有不匹配的任务，提供修复建议
  if (result.mappingStats.mismatchingProject > 0) {
    result.suggestions.push('对于FOWNPROJECT与项目名称不匹配的敏捷任务，建议手动确认正确的项目关联');
  }
  
  // 如果有没有FOWNPROJECT的任务，提供修复建议
  if (result.mappingStats.withoutOwnProject > 0) {
    result.suggestions.push('对于没有FOWNPROJECT值的敏捷任务，建议设置FOWNPROJECT为当前项目名称');
  }
  
  console.log('[API] 敏捷任务FOWNPROJECT分析完成:', result);
  return { success: true, message: '分析完成', data: result };
} 

/**
 * 获取统计数据
 * @param {Object} params 查询参数
 * @param {string} params.reportDate 汇报日期，格式为YYYY-MM-DD（单日查询）
 * @param {string} params.startDate 开始日期，格式为YYYY-MM-DD（范围查询）
 * @param {string} params.endDate 结束日期，格式为YYYY-MM-DD（范围查询）
 * @param {string} params.reportEmpNumber 登录用户账号
 * @param {string} params.documentStatus 项目状态筛选，默认'c'表示已审核，''表示查询未审核数据
 * @param {string} params.timeMode 时间模式，'day'/'week'/'month'
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @param {number} options.retries 失败重试次数，默认1次
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认1小时（统计数据更新频率较高）
 * @returns {Promise} 返回包含统计数据的Promise
 */
export function getStatisticsData(params, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  const maxRetries = options.retries || 1; // 默认重试1次
  const forceRefresh = options.forceRefresh || false; // 默认不强制刷新
  const cacheExpiry = options.cacheExpiry || 60 * 60 * 1000; // 默认1小时缓存
  
  let retryCount = 0;
  const cacheKey = getStatisticsCacheKey(params);
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的统计数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除统计数据缓存`);
  }

  // 创建执行查询的函数
  const executeQuery = () => {
    return new Promise((resolve, reject) => {
      // 构建筛选条件
      let filterString = '';
      
      // 处理日期筛选条件
      if (params.reportDate) {
        // 单日查询
        filterString += `FHBDate='${params.reportDate}'`;
      } else if (params.startDate && params.endDate) {
        // 日期范围查询
        filterString += `FHBDate>='${params.startDate}' and FHBDate<='${params.endDate}'`;
      } else if (params.startDate) {
        // 只有开始日期
        filterString += `FHBDate>='${params.startDate}'`;
      } else if (params.endDate) {
        // 只有结束日期
        filterString += `FHBDate<='${params.endDate}'`;
      } else {
        // 如果没有指定日期，默认查询所有数据
        filterString += `FHBDate<>''`;
      }
      
      // 添加用户筛选条件
      if (params.reportEmpNumber) {
        filterString += ` and FReportEmp.FNumber='${params.reportEmpNumber}'`;
      } else {
        filterString += ` and FReportEmp.FNumber<>''`;
      }
      
      // 添加用户实体ID筛选条件（针对我的工时查询）
      if (params.userEntityId) {
        filterString += ` and FUserId='${params.userEntityId}'`;
      }
      
      // 添加项目状态筛选条件
      if (params.documentStatus !== undefined && params.documentStatus !== null) {
        if (params.documentStatus === '') {
          // 查询未审核数据（除了C以外的数据）
          filterString += ` and FDocumentStatus<>'C'`;
        } else {
          // 查询指定状态的数据
          filterString += ` and FDocumentStatus='${params.documentStatus}'`;
        }
      } else {
        // 默认查询已审核和已提交的数据
        filterString += ` and (FDocumentStatus='C' or FDocumentStatus='B')`;
      }
      
      // 构建请求参数
      const queryData = {
        data: {
          "FormId": "PBEW_GSHB",
          "FieldKeys": "FBillno,FProj.FName,FTask.FName,FDocumentStatus,FPlanStartDate,FPlanEndDate,FPlanWorkHours,FManHour,FOverTime,FFinishRate,FReport,FisFinish,FPOST.FName,FHBDate,FConfirmTime,FOtherTaskName,FOtherTaskType,FTmpTaskTypeid.Fname,FBurnOffTypeid.Fname,FSUMTIME,FUserId,FSubmitDate,FTask,FProj ",
          "FilterString": filterString,
          "OrderString": "",
          "TopRowCount": 0,
          "StartRow": 0,
          "Limit": 2000,
          "SubSystemId": ""
        }
      };
      
      // 记录请求开始时间
      const requestStartTime = Date.now();
      console.log(`[API] 开始获取统计数据`);
      console.log(`[API] 查询参数:`, params);
      console.log(`[API] 筛选条件:`, filterString);
      console.log(`[API] 用户实体ID筛选:`, params.userEntityId ? `FUserId='${params.userEntityId}'` : '未设置');
      
      axios({
        method: 'post',
        url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
        data: queryData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: timeout // 设置请求超时时间
      })
      .then(response => {
        // 记录请求完成时间和耗时
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        console.log(`[API] 获取统计数据成功，耗时: ${requestDuration}ms`);
        
        const data = response.data;
        
        // 打印API原始响应数据
        console.log('[API] 统计数据原始响应:', JSON.stringify(data, null, 2));
        console.log('[API] 响应数据类型:', typeof data, Array.isArray(data) ? '是数组' : '不是数组', '长度:', Array.isArray(data) ? data.length : '未知');
        
        if (Array.isArray(data) && data.length > 0) {
          // 打印第一条数据作为示例
          console.log('[API] 第一条数据示例:', Array.isArray(data[0]) ? data[0] : JSON.stringify(data[0], null, 2));
        }
        
        if (data) {
          // 检查数据有效性
          if (Array.isArray(data)) {
            // 将API返回的数据转换为统计组件需要的格式
            const formattedData = formatStatisticsData(data, params);
            
            // 缓存成功获取的数据
            setCache(cacheKey, formattedData, cacheExpiry);
            console.log(`[API] 统计数据已缓存，有效期: ${(cacheExpiry / 3600000).toFixed(1)}小时`);
            
            resolve(formattedData);
          } else {
            console.warn('[API] 返回数据格式不符合预期，不是数组:', data);
            resolve({
              success: false,
              message: "返回数据格式不符合预期",
              data: {
                summary: {
                  reportHours: 0,
                  overtimeHours: 0,
                  confirmedHours: 0,
                  unauditedHours: 0
                },
                chartData: [],
                taskDetails: []
              }
            });
          }
        } else {
          console.warn('[API] 未获取到统计数据或数据为空');
          resolve({
            success: false,
            message: "未获取到数据",
            data: {
              summary: {
                reportHours: 0,
                overtimeHours: 0,
                confirmedHours: 0,
                unauditedHours: 0
              },
              chartData: [],
              taskDetails: []
            }
          });
        }
      })
      .catch(error => {
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        
        // 详细记录错误信息
        let errorDetails = '';
        
        if (error.response) {
          // 服务器返回了错误响应
          errorDetails = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取统计数据失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
        } else if (error.request) {
          // 请求已发送但没有收到响应
          if (error.code === 'ECONNABORTED') {
            errorDetails = `请求超时(${timeout}ms)`;
            console.error(`[API] 获取统计数据超时，耗时: ${requestDuration}ms`);
          } else {
            errorDetails = `无响应: ${error.message}`;
            console.error(`[API] 获取统计数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
          }
        } else {
          // 请求设置出错
          errorDetails = `请求错误: ${error.message}`;
          console.error(`[API] 获取统计数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
        }
        
        // 如果还可以重试，则重试请求
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`[API] 准备第${retryCount}次重试获取统计数据...`);
          
          // 延迟后重试，每次重试延迟时间增加
          const retryDelay = 1000 * retryCount; // 1秒 * 重试次数
          setTimeout(() => {
            executeQuery().then(resolve).catch(reject);
          }, retryDelay);
        } else {
          // 所有重试失败后，尝试从缓存加载过期数据作为后备
          const expiredCache = localStorage.getItem(cacheKey);
          if (expiredCache) {
            try {
              const cacheData = JSON.parse(expiredCache).data;
              console.warn(`[API] 使用过期缓存作为后备数据`);
              showToast({
                message: '使用离线缓存数据（可能不是最新）',
                type: 'warning',
                duration: 3000
              });
              resolve(cacheData);
            } catch (e) {
              reject(new Error(`获取统计数据失败: ${errorDetails}`));
            }
          } else {
            reject(new Error(`获取统计数据失败: ${errorDetails}`));
          }
        }
      });
    });
  };
  
  // 执行查询
  return executeQuery();
}

/**
 * 格式化API返回的统计数据为组件所需格式
 * @param {Array} apiData API返回的原始数据
 * @param {Object} params 查询参数，用于确定数据处理方式
 * @returns {Object} 格式化后的统计数据
 */
function formatStatisticsData(apiData, params) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化统计数据: 无数据可格式化');
    return {
      success: false,
      message: "无统计数据返回",
      data: {
        summary: {
          reportHours: 0,
          overtimeHours: 0,
          confirmedHours: 0,
          unauditedHours: 0
        },
        chartData: [],
        taskDetails: []
      }
    };
  }
  
  console.log('[API] 开始格式化统计数据, 行数:', apiData.length);
  
  // 添加状态分布统计
  const statusDistribution = {};
  apiData.forEach((item, index) => {
    let status = '';
    if (Array.isArray(item)) {
      status = item[3] || ''; // FDocumentStatus
    } else if (typeof item === 'object') {
      status = item.FDocumentStatus || '';
    }
    
    if (statusDistribution[status]) {
      statusDistribution[status]++;
    } else {
      statusDistribution[status] = 1;
    }
    
    // 打印前5条数据的状态信息
    if (index < 5) {
      console.log(`[API] 数据 ${index + 1}: 状态=${status}, 完整记录=`, Array.isArray(item) ? item : JSON.stringify(item));
    }
  });
  
  console.log('[API] 状态分布统计:', statusDistribution);
  console.log('[API] 状态为\'B\'的记录数量:', statusDistribution['B'] || 0);
  
  try {
    // 统计数据汇总
    let totalReportHours = 0;
    let totalOvertimeHours = 0;
    let totalConfirmedHours = 0; // 已审核工时
    let totalUnauditedHours = 0; // 未审核工时
    let totalPlannedHours = 0; // 总计划工时
    
    // 任务明细数据
    const taskDetails = [];
    
    // 图表数据收集（按日期分组）
    const chartDataMap = new Map();
    
    // 项目工时汇总数据收集
    const projectSummaryMap = new Map();
    
    // 日期格式化函数
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      
      // 处理不同的日期格式
      let dateObj;
      if (typeof dateStr === 'string') {
        // 如果包含 'T'，说明是 ISO 格式
        if (dateStr.includes('T')) {
          dateObj = new Date(dateStr);
        } else {
          // 假设是 YYYY-MM-DD 格式
          dateObj = new Date(dateStr + 'T00:00:00');
        }
      } else {
        dateObj = new Date(dateStr);
      }
      
      if (isNaN(dateObj.getTime())) {
        console.warn('[API] 无效日期格式:', dateStr);
        return '';
      }
      
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    };
    
    // 遍历API数据进行格式化
    apiData.forEach((item, index) => {
      try {
        let rowData = {};
        
        // 检查数据格式，可能是数组或对象
        if (Array.isArray(item)) {
          // 数组格式的数据映射
          rowData = {
            billno: item[0] || '',                    // FBillno: 任务/项目ID
            projectName: item[1] || '',               // FProj.FName: 项目名称
            taskName: item[2] || '',                  // FTask.FName: 任务名称
            documentStatus: item[3] || '',            // FDocumentStatus: 项目状态
            planStartDate: item[4] || '',             // FPlanStartDate: 计划开始时间
            planEndDate: item[5] || '',               // FPlanEndDate: 计划结束时间
            planWorkHours: item[6] || 0,              // FPlanWorkHours: 计划工时
            manHour: item[7] || 0,                    // FManHour: 汇报工时
            overTime: item[8] || 0,                   // FOverTime: 加班工时
            finishRate: item[9] || '',                // FFinishRate: 完成进度
            report: item[10] || '',                   // FReport: 工作内容
            isFinish: item[11] || false,              // FisFinish: 是否完成
            postName: item[12] || '',                 // FPOST.FName: 岗位
            hbDate: item[13] || '',                   // FHBDate: 汇报日期
            confirmTime: item[14] || 0,               // FConfirmTime: 确认工时
            otherTaskName: item[15] || '',            // FOtherTaskName: 其他任务名称
            otherTaskType: item[16] || '',            // FOtherTaskType: 其他任务类型
            tmpTaskTypeName: item[17] || '',          // FTmpTaskTypeid.Fname: 临时任务类型名称
            burnOffTypeName: item[18] || '',          // FBurnOffTypeid.Fname: 耗费类型名称
            fsumtime: item[19] || 0,                  // FSUMTIME: 累计工时
            userId: item[20] || '',                   // FUserId: 用户ID
            submitDate: item[21] || '',               // FSubmitDate: 提交日期
            taskId: item[22] || '',                   // FTask.FID: 任务ID
            projectId: item[23] || ''                 // FProj.FID: 项目ID
          };
        } else if (typeof item === 'object') {
          // 对象格式的数据映射
          rowData = {
            billno: item.FBillno || '',
            projectName: item['FProj.FName'] || item.FProj?.FName || '',
            taskName: item['FTask.FName'] || item.FTask?.FName || '',
            documentStatus: item.FDocumentStatus || '',
            planStartDate: item.FPlanStartDate || '',
            planEndDate: item.FPlanEndDate || '',
            planWorkHours: item.FPlanWorkHours || 0,
            manHour: item.FManHour || 0,
            overTime: item.FOverTime || 0,
            finishRate: item.FFinishRate || '',
            report: item.FReport || '',
            isFinish: item.FisFinish || false,
            postName: item['FPOST.FName'] || item.FPOST?.FName || '',
            hbDate: item.FHBDate || '',
            confirmTime: item.FConfirmTime || 0,
            otherTaskName: item.FOtherTaskName || '',
            otherTaskType: item.FOtherTaskType || '',
            tmpTaskTypeName: item['FTmpTaskTypeid.Fname'] || '',
            burnOffTypeName: item['FBurnOffTypeid.Fname'] || '',
            fsumtime: item.FSUMTIME || 0,
            userId: item.FUserId || '',
            submitDate: item.FSubmitDate || '',
            taskId: item['FTask.FID'] || item.FTask?.FID || '',
            projectId: item['FProj.FID'] || item.FProj?.FID || ''
          };
        }
        
        // 数据类型转换和验证
        const reportHours = parseFloat(rowData.manHour) || 0;
        const overtimeHours = parseFloat(rowData.overTime) || 0;
        const confirmHours = parseFloat(rowData.confirmTime) || 0;
        const plannedHours = parseFloat(rowData.planWorkHours) || 0;
        const progress = parseFloat(rowData.finishRate) || 0;
        const reportDate = formatDate(rowData.hbDate);
        const documentStatus = rowData.documentStatus;
        
        // 打印项目ID和任务ID到控制台（按用户要求）
        console.log(`[API] 统计数据 - 项目ID (FProj.FID): ${rowData.projectId || '无'}`);
        console.log(`[API] 统计数据 - 任务ID (FTask.FID): ${rowData.taskId || '无'}`);

        // 判断任务类型
        let taskType = 'normal'; // 默认为普通任务
        let displayTaskName = rowData.taskName; // 默认使用原任务名称
        let displayProjectName = rowData.projectName; // 默认使用原项目名称

        if (rowData.otherTaskType) {
          const otherTypeNum = parseInt(rowData.otherTaskType);
          if (otherTypeNum === 1) {
            taskType = 'temp'; // 临时任务
            displayTaskName = rowData.otherTaskName || rowData.taskName || '临时任务';
            displayProjectName = '临时任务';
          } else if (otherTypeNum === 2) {
            taskType = 'timecost'; // 耗费任务
            displayTaskName = rowData.otherTaskName || rowData.taskName || '耗费任务';
            displayProjectName = '耗费';
          }
        }
        
        // 累计统计数据
        totalReportHours += reportHours;
        totalOvertimeHours += overtimeHours;
        totalConfirmedHours += confirmHours;
        totalPlannedHours += plannedHours;
        
        // 根据状态分类统计未审核工时
        // A=暂存, B=已提交(未审核), C=已审核
        if (documentStatus === 'B') {
          // 未审核的工时：只统计已提交状态下的汇报工时和加班工时的和
          const unauditedSum = reportHours + overtimeHours;
          totalUnauditedHours += unauditedSum;
          console.log(`[API] 未审核工时统计: 项目=${rowData.projectName}, 状态=${documentStatus}, 汇报工时=${reportHours}h, 加班工时=${overtimeHours}h, 小计=${unauditedSum}h`);
        } else {
          // 记录非'B'状态的数据，帮助调试
          if (index < 3) { // 只记录前3条非'B'状态的数据，避免日志过多
            console.log(`[API] 非未审核状态记录: 项目=${rowData.projectName}, 状态=${documentStatus}, 汇报工时=${reportHours}h, 加班工时=${overtimeHours}h`);
          }
        }
        
        // 收集图表数据（按日期分组）
        if (reportDate) {
          if (!chartDataMap.has(reportDate)) {
            chartDataMap.set(reportDate, {
              date: reportDate,
              reportHours: 0,
              overtimeHours: 0,
              totalHours: 0
            });
          }
          const chartItem = chartDataMap.get(reportDate);
          chartItem.reportHours += reportHours;
          chartItem.overtimeHours += overtimeHours;
          chartItem.totalHours += reportHours + overtimeHours;
        }
        
        // 收集任务明细数据
        taskDetails.push({
          taskName: displayTaskName || '未知任务',
          projectName: displayProjectName || '未知项目',
          originalTaskName: rowData.taskName || '', // 保存原始任务名称
          originalProjectName: rowData.projectName || '', // 保存原始项目名称
          status: documentStatus || 'A',
          workDate: reportDate,
          submitDate: formatDate(rowData.submitDate),
          reportHours: reportHours,
          overtimeHours: overtimeHours,
          confirmHours: confirmHours,
          progress: progress,
          position: rowData.postName || '',
          content: rowData.report || '',
          isFinish: rowData.isFinish === true || rowData.isFinish === 'true' || rowData.isFinish === 1,
          billno: rowData.billno || '',
          plannedHours: plannedHours,
          planStartDate: formatDate(rowData.planStartDate),
          planEndDate: formatDate(rowData.planEndDate),
          // 任务类型相关字段
          taskType: taskType,
          otherTaskName: rowData.otherTaskName || '',
          otherTaskType: rowData.otherTaskType || '',
          tmpTaskTypeName: rowData.tmpTaskTypeName || '',
          burnOffTypeName: rowData.burnOffTypeName || '',
          // 为向后兼容保留的字段
          isTempTask: taskType === 'temp',
          isTimeCost: taskType === 'timecost',
          // 添加项目ID和任务ID字段（按用户要求存储但不展示）
          projectId: rowData.projectId || '',
          taskId: rowData.taskId || ''
        });
        
        // 收集项目工时汇总数据
        const projectName = displayProjectName || '未知项目';
        if (!projectSummaryMap.has(projectName)) {
          projectSummaryMap.set(projectName, {
            projectName: projectName,
            projectId: rowData.projectId || '', // 添加项目ID
            reportHours: 0,
            overtimeHours: 0,
            confirmHours: 0,
            plannedHours: 0,
            taskCount: 0
          });
        }
        const projectSummary = projectSummaryMap.get(projectName);
        projectSummary.reportHours += reportHours;
        projectSummary.overtimeHours += overtimeHours;
        projectSummary.confirmHours += confirmHours;
        projectSummary.plannedHours += plannedHours;
        projectSummary.taskCount += 1;
        
        // 如果当前记录的projectId不为空且项目汇总中还没有projectId，则更新
        if (rowData.projectId && !projectSummary.projectId) {
          projectSummary.projectId = rowData.projectId;
        }
      } catch (error) {
        console.warn(`[API] 处理第${index + 1}条统计数据时出错:`, error.message, item);
      }
    });
    
    // 生成图表数据
    const chartData = Array.from(chartDataMap.values())
      .sort((a, b) => new Date(a.date) - new Date(b.date)) // 按日期排序
      .map(item => {
        // 计算高度百分比（基于当日总工时，最大值设为12小时=100%）
        const maxHours = 12;
        const heightPercent = Math.min((item.totalHours / maxHours) * 100, 100);
        
        // 根据时间模式生成标签
        let label = '';
        if (params.timeMode === 'day') {
          // 日模式显示具体日期
          const date = new Date(item.date);
          label = `${date.getMonth() + 1}/${date.getDate()}`;
        } else if (params.timeMode === 'week') {
          // 周模式显示周几
          const date = new Date(item.date);
          const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
          label = weekdays[date.getDay()];
        } else {
          // 月模式显示日期
          label = item.date.split('-')[2]; // 取日
        }
        
        return {
          label: label,
          value: item.totalHours,
          heightPercent: heightPercent,
          reportHours: item.reportHours,
          overtimeHours: item.overtimeHours,
          date: item.date
        };
      });
    
    // 格式化汇总数据
    const summary = {
      reportHours: +totalReportHours.toFixed(1),
      overtimeHours: +totalOvertimeHours.toFixed(1),
      confirmedHours: +totalConfirmedHours.toFixed(1),
      unauditedHours: +totalUnauditedHours.toFixed(1),
      FPlanWorkHours: +totalPlannedHours.toFixed(1)
    };
    
    // 生成项目工时汇总数据
    const projectSummaries = Array.from(projectSummaryMap.values())
      .map(project => {
        const projectData = {
          projectName: project.projectName,
          projectId: project.projectId, // 确保包含项目ID
          reportHours: +project.reportHours.toFixed(1),
          overtimeHours: +project.overtimeHours.toFixed(1),
          confirmHours: +project.confirmHours.toFixed(1),
          FPlanWorkHours: +project.plannedHours.toFixed(1),
          taskCount: project.taskCount,
          totalHours: +(project.reportHours + project.overtimeHours).toFixed(1)
        };
        
        console.log(`[API] 项目汇总数据: ${projectData.projectName}, projectId: ${projectData.projectId || '无'}`);
        return projectData;
      })
      .sort((a, b) => b.totalHours - a.totalHours); // 按总工时降序排列
    
    console.log(`[API] 统计数据格式化完成: 汇报工时=${summary.reportHours}h, 加班工时=${summary.overtimeHours}h, 确认工时=${summary.confirmedHours}h, 未审核工时=${summary.unauditedHours}h`);
    console.log(`[API] 图表数据点数: ${chartData.length}, 任务明细数: ${taskDetails.length}, 项目汇总数: ${projectSummaries.length}`);
    console.log(`[API] 最终状态分布统计:`, statusDistribution);
    console.log(`[API] 未审核工时详细信息: 总计=${totalUnauditedHours}h, 来源记录数=${statusDistribution['B'] || 0}条`);
    
    // 如果未审核工时为0但有状态为'B'的记录，说明计算逻辑可能有问题
    if (totalUnauditedHours === 0 && (statusDistribution['B'] || 0) > 0) {
      console.warn(`[API] 警告: 有${statusDistribution['B']}条状态为'B'的记录，但未审核工时为0，请检查数据格式`);
    }
    
    return {
      success: true,
      message: "统计数据获取成功",
      data: {
        summary: summary,
        chartData: chartData,
        taskDetails: taskDetails.slice(0, 50), // 限制任务明细数量，避免UI过载
        projectSummaries: projectSummaries
      }
    };
    
  } catch (error) {
    console.error('[API] 格式化统计数据时出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化统计数据错误: ${error.message}`,
      data: {
        summary: {
          reportHours: 0,
          overtimeHours: 0,
          confirmedHours: 0,
          unauditedHours: 0
        },
        chartData: [],
        taskDetails: []
      }
    };
  }
}

/**
 * 删除工时报告数据
 * @param {string} billNumber 要删除的单据号
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @returns {Promise} 返回包含删除结果的Promise
 */
export function deleteWorkReportData(billNumber, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  
  if (!billNumber) {
    return Promise.reject(new Error('单据号不能为空'));
  }
  
  // 构建删除请求的数据
  const deleteData = {
    "FormId": "PBEW_GSHB",
    "data": {
      "Numbers": [billNumber]
    }
  };
  
  console.log(`[API] 开始删除工时报告，单据号: ${billNumber}`);
  console.log('[API] 删除请求数据:', JSON.stringify(deleteData, null, 2));
  
  // 记录请求开始时间
  const requestStartTime = Date.now();
  
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete.common.kdsvc',
      data: deleteData,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },
      timeout: timeout
    })
    .then(response => {
      // 记录请求完成时间和耗时
      const requestEndTime = Date.now();
      const requestDuration = requestEndTime - requestStartTime;
      console.log(`[API] 删除工时报告请求完成，耗时: ${requestDuration}ms`);
      
      const data = response.data;
      console.log('[API] 删除响应数据:', JSON.stringify(data, null, 2));
      
      // 解析删除响应
      if (data && data.Result) {
        const result = data.Result;
        
        // 检查删除是否成功
        if (result.ResponseStatus && result.ResponseStatus.IsSuccess) {
          console.log(`[API] 工时报告删除成功，单据号: ${billNumber}`);
          resolve({
            success: true,
            message: '删除成功',
            data: {
              billNumber: billNumber,
              deletedCount: result.ResponseStatus.SuccessEntitys?.length || 1
            }
          });
        } else {
          // 删除失败
          const errorMsg = result.ResponseStatus?.Errors?.[0]?.Message || 
                          result.ResponseStatus?.Message || 
                          '删除失败，未知错误';
          console.error(`[API] 工时报告删除失败，单据号: ${billNumber}, 错误: ${errorMsg}`);
          resolve({
            success: false,
            message: errorMsg,
            data: {
              billNumber: billNumber
            }
          });
        }
      } else {
        console.error(`[API] 删除响应格式异常:`, data);
        resolve({
          success: false,
          message: '删除响应格式异常',
          data: {
            billNumber: billNumber
          }
        });
      }
    })
    .catch(error => {
      const requestEndTime = Date.now();
      const requestDuration = requestEndTime - requestStartTime;
      
      // 详细记录错误信息
      let errorDetails = '';
      
      if (error.response) {
        // 服务器返回了错误响应
        errorDetails = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
        console.error(`[API] 删除工时报告失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
      } else if (error.request) {
        // 请求已发送但没有收到响应
        if (error.code === 'ECONNABORTED') {
          errorDetails = `请求超时(${timeout}ms)`;
          console.error(`[API] 删除工时报告超时，耗时: ${requestDuration}ms`);
        } else {
          errorDetails = `无响应: ${error.message}`;
          console.error(`[API] 删除工时报告未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
        }
      } else {
        // 请求设置出错
        errorDetails = `请求错误: ${error.message}`;
        console.error(`[API] 删除工时报告请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
      }
      
      reject(new Error(`删除工时报告失败: ${errorDetails}`));
    });
  });
}

/**
 * 获取用户的工时状态数据，用于日历显示对号和红点
 * @param {string} userAccount 用户账号 (FNumber)
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @returns {Promise} 返回包含工时状态数据的Promise
 */
export function getCalendarWorkHourStatus(userAccount, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  
  if (!userAccount) {
    return Promise.reject(new Error('用户账号不能为空'));
  }
  
  // 获取今天的日期字符串 (格式: YYYY-MM-DD)
  const today = new Date();
  const todayStr = today.getFullYear() + '-' + 
                  String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                  String(today.getDate()).padStart(2, '0');
  
  // 构建请求参数，添加日期过滤条件
  const queryData = {
    data: {
      "FormId": "PBEW_GSHB",
      "FieldKeys": "FDocumentStatus,FManHour,FHBDate",
      "FilterString": `FReportEmp.FNumber='${userAccount}' AND FHBDate<='${todayStr}'`,
      "OrderString": "",
      "TopRowCount": 0,
      "StartRow": 0,
      "Limit": 2000,
      "SubSystemId": ""
    }
  };
  
  console.log(`[API] 开始获取日历工时状态数据，用户账号: ${userAccount}，截止日期: ${todayStr}`);
  console.log('[API] 请求参数:', JSON.stringify(queryData, null, 2));
  
  // 记录请求开始时间
  const requestStartTime = Date.now();
  
  return new Promise((resolve) => {
    axios({
      method: 'post',
      url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
      data: queryData,
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },
      timeout: timeout
    })
    .then(response => {
      // 记录请求完成时间和耗时
      const requestEndTime = Date.now();
      const requestDuration = requestEndTime - requestStartTime;
      console.log(`[API] 获取日历工时状态数据成功，耗时: ${requestDuration}ms`);
      
      const data = response.data;
      console.log('[API] 原始响应数据:', JSON.stringify(data, null, 2));
      console.log('[API] 响应数据类型:', typeof data, Array.isArray(data) ? '是数组' : '不是数组', '长度:', Array.isArray(data) ? data.length : '未知');
      
      if (Array.isArray(data)) {
        // 按日期分组聚合工时数据
        const dateWorkHourMap = {};
        
        data.forEach(record => {
          if (Array.isArray(record) && record.length >= 3) {
            const status = record[0]; // FDocumentStatus
            const workHour = parseFloat(record[1]) || 0; // FManHour
            const workDate = record[2]; // FHBDate
            
            // 提取日期部分（去除时间）
            const dateKey = workDate ? workDate.split('T')[0] : null;
            
            if (dateKey) {
              if (!dateWorkHourMap[dateKey]) {
                dateWorkHourMap[dateKey] = {
                  totalHours: 0,
                  records: []
                };
              }
              
              dateWorkHourMap[dateKey].totalHours += workHour;
              dateWorkHourMap[dateKey].records.push({
                status,
                workHour,
                workDate
              });
            }
          }
        });
        
        console.log(`[API] 日历工时状态数据处理完成，共处理 ${Object.keys(dateWorkHourMap).length} 个日期`);
        
        resolve({
          success: true,
          message: '获取日历工时状态数据成功',
          data: {
            dateWorkHourMap,
            rawData: data
          }
        });
      } else {
        console.warn('[API] 获取日历工时状态数据 - 响应格式不符合预期:', data);
        resolve({
          success: false,
          message: '返回数据格式不符合预期',
          data: {
            dateWorkHourMap: {},
            rawData: data
          }
        });
      }
    })
    .catch(error => {
      // 记录请求完成时间和耗时
      const requestEndTime = Date.now();
      const requestDuration = requestEndTime - requestStartTime;
      
      let errorMessage = '';
      
      if (error.response) {
        // 服务器返回了错误响应
        errorMessage = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
        console.error(`[API] 获取日历工时状态数据失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
      } else if (error.request) {
        // 请求已发送但没有收到响应
        if (error.code === 'ECONNABORTED') {
          errorMessage = `请求超时(${timeout}ms)`;
          console.error(`[API] 获取日历工时状态数据超时，耗时: ${requestDuration}ms`);
        } else {
          errorMessage = `无响应: ${error.message}`;
          console.error(`[API] 获取日历工时状态数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
        }
      } else {
        // 请求设置出错
        errorMessage = `请求错误: ${error.message}`;
        console.error(`[API] 获取日历工时状态数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
      }
      
      // 对于API调用失败，我们仍然resolve而不是reject，以保持与其他API方法的一致性
      resolve({
        success: false,
        message: errorMessage,
        data: {
          dateWorkHourMap: {},
          rawData: null
        }
      });
    });
  });
}

/**
 * 撤销工时报告数据 - 使用CancelAssign接口
 * @param {Object} params 撤销参数
 * @param {Array<string>} params.numbers 单据编号数组 (fbillno数组)
 * @param {string} params.userId 用户ID
 * @returns {Promise} 返回包含操作结果的Promise
 */
export function undoWorkReportData(params = {}) {
  const { numbers, userId } = params;
  
  if (!numbers || !Array.isArray(numbers) || numbers.length === 0) {
    return Promise.reject(new Error('单据编号不能为空'));
  }
  
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  // 构建撤销请求数据
  const requestData = {
    FormId: "PBEW_GSHB",
    data: {
      Numbers: numbers,
      UserId: userId
    }
  };
  
  console.log('[API] 撤销工时报告 - 请求参数:', {
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAssign.common.kdsvc',
    requestData,
    originalParams: params
  });
  
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAssign.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json'
    }
  })
    .then(response => {
      console.log('[API] 撤销工时报告 - 响应数据:', response.data);
      
      if (response.data) {
        // 检查响应是否表示成功
        const result = response.data;
        
        // 如果API返回了错误信息，视为失败
        if (result.Result && result.Result.ResponseStatus && result.Result.ResponseStatus.IsSuccess === false) {
          const errorMessage = result.Result.ResponseStatus.Errors?.[0]?.Message || '撤销失败';
          console.error('[API] 撤销工时报告失败:', errorMessage);
          return {
            success: false,
            message: errorMessage,
            data: result
          };
        }
        
        // 其他情况视为成功
        console.log('[API] 撤销工时报告成功');
        return {
          success: true,
          message: '撤销成功',
          data: result
        };
      } else {
        console.error('[API] 撤销工时报告 - 响应数据为空');
        return {
          success: false,
          message: '撤销失败：服务器响应异常',
          data: null
        };
      }
    })
    .catch(error => {
      console.error('[API] 撤销工时报告 - 网络或服务器错误:', error);
      
      let errorMessage = '撤销失败';
      if (error.response) {
        errorMessage = `撤销失败：${error.response.status} - ${error.response.statusText}`;
        if (error.response.data && error.response.data.message) {
          errorMessage += ` (${error.response.data.message})`;
        }
      } else if (error.message) {
        errorMessage = `撤销失败：${error.message}`;
      }
      
      return {
        success: false,
        message: errorMessage,
        error: error
      };
    });
}

/**
 * 获取用户已报工时数据（用于已报字段的数据获取）
 * @param {string|number} taskOrProjectId 任务ID或项目ID，优先获取任务，如果没有任务则获取项目ID
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @param {number} options.retries 失败重试次数，默认2次
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认1小时
 * @returns {Promise} 返回包含已报工时数据的Promise
 */
export function getUserDueProjectTasks(taskOrProjectId, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  const maxRetries = options.retries || 2; // 默认重试2次（从1次增加到2次）
  const forceRefresh = options.forceRefresh || false; // 默认不强制刷新
  const cacheExpiry = options.cacheExpiry || 1 * 60 * 60 * 1000; // 默认1小时

  let retryCount = 0;
  const cacheKey = `user_due_project_tasks_${taskOrProjectId}`;

  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的已报工时数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除已报工时缓存`);
  }

  // 创建执行查询的函数
  const executeQuery = () => {
    return new Promise((resolve, reject) => {
      // 构建请求参数
      const requestData = {
        parameter: {
          data: {
            TP: taskOrProjectId
          }
        }
      };

      // 记录请求开始时间
      const requestStartTime = Date.now();
      console.log(`[API] 开始获取已报工时数据，任务/项目ID: ${taskOrProjectId}`);
      console.log(`[API] 已报工时请求参数:`, JSON.stringify(requestData, null, 2));

      axios({
        method: 'post',
        url: 'http://140.249.162.74:81/k3cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.GetUserDueProjectTasks,JR.K3.PLM.WorkhourReport.common.kdsvc',
        data: requestData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: timeout
      })
      .then(response => {
        // 记录请求完成时间和耗时
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        console.log(`[API] 获取已报工时数据成功，耗时: ${requestDuration}ms`);

        const data = response.data;

        // 打印API原始响应数据
        console.log('[API] 已报工时原始响应数据:', JSON.stringify(data, null, 2));
        console.log('[API] 已报工时响应数据类型:', typeof data, Array.isArray(data) ? '是数组' : '不是数组', '长度:', Array.isArray(data) ? data.length : '未知');

        if (Array.isArray(data) && data.length > 0) {
          // 打印第一条数据作为示例
          console.log('[API] 已报工时第一条数据示例:', Array.isArray(data[0]) ? data[0] : JSON.stringify(data[0], null, 2));
        }

        if (data) {
          // 检查数据有效性，处理嵌套的data结构
          let apiData;
          if (data.data && Array.isArray(data.data)) {
            // 新的API响应格式: {data: [...]}
            apiData = data.data;
            console.log('[API] 检测到嵌套data结构，已正确提取数据数组');
            console.log('[API] 提取的apiData:', JSON.stringify(apiData, null, 2));
          } else if (Array.isArray(data)) {
            // 旧的API响应格式: [...]
            apiData = data;
            console.log('[API] 直接使用响应中的数据数组');
            console.log('[API] 直接使用的apiData:', JSON.stringify(apiData, null, 2));
          } else {
            // 未知格式或空数据
            apiData = [];
            console.warn('[API] 无法识别的数据格式或空数据:', data);
          }

          if (apiData.length > 0) {
            console.log('[API] 准备调用formatUserDueProjectTasksData，传入数据长度:', apiData.length);
            // 将API返回的数据转换为前端组件需要的格式
            const formattedData = formatUserDueProjectTasksData(apiData);
            console.log('[API] formatUserDueProjectTasksData返回结果:', JSON.stringify(formattedData, null, 2));

            // 缓存成功获取的数据
            setCache(cacheKey, formattedData, cacheExpiry);
            console.log(`[API] 已报工时数据已缓存，有效期: ${(cacheExpiry / 3600000).toFixed(1)}小时`);

            resolve(formattedData);
          } else {
            console.warn('[API] 已报工时返回数据为空数组');
            resolve({
              success: false,
              message: "返回数据为空",
              data: {
                reportedHours: 0,
                tasks: []
              }
            });
          }
        } else {
          console.warn('[API] 未获取到已报工时数据或数据为空');
          resolve({
            success: false,
            message: "未获取到数据",
            data: {
              reportedHours: 0,
              tasks: []
            }
          });
        }
      })
      .catch(error => {
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;

        // 详细记录错误信息
        let errorDetails = '';
        let shouldRetry = false;

        if (error.response) {
          // 服务器返回了错误响应
          const status = error.response.status;
          errorDetails = `服务器错误(${status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取已报工时数据失败，HTTP状态: ${status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
          
          // 只有5xx错误才重试
          shouldRetry = status >= 500 && status < 600;
        } else if (error.request) {
          // 请求已发送但没有收到响应
          if (error.code === 'ECONNABORTED') {
            errorDetails = `请求超时(${timeout}ms)`;
            console.error(`[API] 获取已报工时数据超时，耗时: ${requestDuration}ms`);
            shouldRetry = true; // 超时错误可以重试
          } else {
            errorDetails = `无响应: ${error.message}`;
            console.error(`[API] 获取已报工时数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
            shouldRetry = true; // 网络错误可以重试
          }
        } else {
          // 请求设置出错
          errorDetails = `请求错误: ${error.message}`;
          console.error(`[API] 获取已报工时数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
          shouldRetry = false; // 请求设置错误不重试
        }

        // 如果还可以重试且应该重试，则重试请求
        if (retryCount < maxRetries && shouldRetry) {
          retryCount++;
          console.log(`[API] 准备第${retryCount}次重试获取已报工时数据...`);

          // 延迟后重试，每次重试延迟时间增加
          const retryDelay = 1000 * retryCount; // 1秒 * 重试次数
          setTimeout(() => {
            executeQuery().then(resolve).catch(reject);
          }, retryDelay);
        } else {
          // 所有重试失败后，尝试从缓存加载过期数据作为后备
          const expiredCache = localStorage.getItem(cacheKey);
          if (expiredCache) {
            try {
              const cacheData = JSON.parse(expiredCache).data;
              console.warn(`[API] 使用过期缓存作为后备数据`);
              resolve(cacheData);
            } catch (e) {
              reject(new Error(`获取已报工时失败: ${errorDetails}`));
            }
          } else {
            reject(new Error(`获取已报工时失败: ${errorDetails}`));
          }
        }
      });
    });
  };

  // 执行查询
  return executeQuery();
}

/**
 * 格式化API返回的已报工时数据为组件所需格式
 * @param {Array} apiData API返回的原始数据
 * @returns {Object} 格式化后的数据，包含已报工时总数和任务详情
 */
function formatUserDueProjectTasksData(apiData) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化已报工时数据: 无数据可格式化');
    return {
      success: false,
      message: "无已报工时数据返回",
      data: {
        reportedHours: 0,
        tasks: []
      }
    };
  }

  console.log('[API] 开始格式化已报工时数据, 行数:', apiData.length);
  console.log('[API] 原始apiData:', JSON.stringify(apiData, null, 2));

  try {
    let totalReportedHours = 0;

    // 增强的API响应格式处理
    console.log('[API] 检查第一条数据:', apiData[0]);
    console.log('[API] 已报工时API返回的完整数据结构:', JSON.stringify(apiData, null, 2));

    // 检查所有可能的字段名，Property0 是最常见的字段
    const possibleFields = ['Property0', 'Property', 'reportedHours', 'FReportedHours', 'ReportedHours'];
    let foundField = null;
    let fieldValue = null;

    // 遍历所有可能的字段名
    for (const field of possibleFields) {
      if (apiData[0] && apiData[0][field] !== undefined) {
        foundField = field;
        fieldValue = apiData[0][field];
        console.log(`[API] ✓ 找到已报工时字段: ${field}, 值: ${fieldValue}, 类型: ${typeof fieldValue}`);
        break;
      }
    }

    if (!foundField) {
      console.warn('[API] ⚠️ 未找到预期的已报工时字段，可用字段:', Object.keys(apiData[0] || {}));
    }

    if (foundField && fieldValue !== null && fieldValue !== undefined) {
      // 确保转换为数字
      totalReportedHours = Number(fieldValue) || 0;
      console.log(`[API] 从字段 ${foundField} 获取已报工时: ${totalReportedHours}`);

      // 创建一个虚拟任务记录用于显示
      const tasks = [{
        id: `reported-task-${Date.now()}`,
        taskId: '',
        projectId: '',
        taskName: '已报工时汇总',
        projectName: '系统汇总',
        reportedHours: totalReportedHours,
        reportDate: new Date().toISOString().split('T')[0],
        status: 'C'
      }];

      console.log(`[API] 已报工时数据格式化完成，总已报工时: ${totalReportedHours.toFixed(2)}h`);
      console.log('[API] 返回的格式化数据:', {
        success: true,
        message: "已报工时数据获取成功",
        data: {
          reportedHours: totalReportedHours,
          tasks: tasks
        }
      });

      // 返回格式化后的数据
      return {
        success: true,
        message: "已报工时数据获取成功",
        data: {
          reportedHours: totalReportedHours,
          tasks: tasks
        }
      };
    } else {
      console.log('[API] 未找到预期的字段，尝试其他格式处理');
      console.log('[API] 第一条数据的所有字段:', Object.keys(apiData[0] || {}));
    }

    // 如果不是新格式，尝试按旧格式处理
    const tasks = apiData.map((item, index) => {
      // 检查数据格式，可能是数组或对象
      const rowData = Array.isArray(item) ? {
        // 根据实际API返回的字段结构进行映射
        taskId: item[0] || '',
        projectId: item[1] || '',
        taskName: item[2] || '',
        projectName: item[3] || '',
        reportedHours: parseFloat(item[4]) || 0,
        reportDate: item[5] || '',
        status: item[6] || ''
      } : {
        // 对象格式的字段映射
        taskId: item.taskId || item.FTaskId || '',
        projectId: item.projectId || item.FProjectId || '',
        taskName: item.taskName || item.FTaskName || '',
        projectName: item.projectName || item.FProjectName || '',
        reportedHours: parseFloat(item.reportedHours || item.FReportedHours || 0),
        reportDate: item.reportDate || item.FReportDate || '',
        status: item.status || item.FStatus || ''
      };

      // 累计已报工时
      totalReportedHours += rowData.reportedHours;

      // 处理日期格式，转换为YYYY-MM-DD
      const formatDate = (dateStr) => {
        if (!dateStr) return '';
        try {
          const date = new Date(dateStr);
          return date.toISOString().split('T')[0];
        } catch (e) {
          return dateStr;
        }
      };

      // 生成唯一ID
      const uniqueId = `reported-task-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`;

      console.log(`[API] 已报工时任务 ${index + 1}: 任务ID=${rowData.taskId}, 项目ID=${rowData.projectId}, 已报工时=${rowData.reportedHours}h`);

      return {
        id: uniqueId,
        taskId: rowData.taskId,
        projectId: rowData.projectId,
        taskName: rowData.taskName,
        projectName: rowData.projectName,
        reportedHours: rowData.reportedHours,
        reportDate: formatDate(rowData.reportDate),
        status: rowData.status
      };
    });

    console.log(`[API] 已报工时数据格式化完成，共 ${tasks.length} 条记录，总已报工时: ${totalReportedHours.toFixed(2)}h`);

    // 返回格式化后的数据
    return {
      success: true,
      message: "已报工时数据获取成功",
      data: {
        reportedHours: totalReportedHours,
        tasks: tasks
      }
    };
  } catch (error) {
    console.error('[API] 格式化已报工时数据时出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化已报工时数据错误: ${error.message}`,
      data: {
        reportedHours: 0,
        tasks: []
      }
    };
  }
}

/**
 * 获取任务详情专用的已报工时数据（新API接口）
 * @param {string|number} taskOrProjectId 任务ID或项目ID，优先传任务ID，如果没有则传项目ID
 * @param {Object} options 可选配置项
 * @param {number} options.timeout 请求超时时间(毫秒)，默认30秒
 * @param {number} options.retries 失败重试次数，默认1次
 * @returns {Promise} 返回包含已报工时数据的Promise
 */
export function getDueProjectTasks(taskOrProjectId, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  const maxRetries = options.retries || 1; // 默认重试1次

  let retryCount = 0;

  // 创建执行查询的函数
  const executeQuery = () => {
    return new Promise((resolve, reject) => {
      // 构建请求参数
      const requestData = {
        parameter: {
          data: {
            TP: taskOrProjectId
          }
        }
      };

      // 记录请求开始时间
      const requestStartTime = Date.now();
      console.log(`[API] 开始获取任务详情已报工时数据，任务/项目ID: ${taskOrProjectId}`);
      console.log(`[API] 任务详情已报工时请求参数:`, JSON.stringify(requestData, null, 2));

      axios({
        method: 'post',
        url: 'http://140.249.162.74:81/K3Cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.GetDueProjectTasks,JR.K3.PLM.WorkhourReport.common.kdsvc',
        data: requestData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: timeout
      })
      .then(response => {
        // 记录请求完成时间和耗时
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        console.log(`[API] 获取任务详情已报工时数据成功，耗时: ${requestDuration}ms`);

        const data = response.data;

        // 打印API原始响应数据
        console.log('[API] 任务详情已报工时原始响应数据:', JSON.stringify(data, null, 2));
        console.log('[API] 任务详情已报工时响应数据类型:', typeof data, Array.isArray(data) ? '是数组' : '不是数组', '长度:', Array.isArray(data) ? data.length : '未知');

        if (Array.isArray(data) && data.length > 0) {
          // 打印第一条数据作为示例
          console.log('[API] 任务详情已报工时第一条数据示例:', Array.isArray(data[0]) ? data[0] : JSON.stringify(data[0], null, 2));
        }

        if (data) {
          // 检查数据有效性，处理嵌套的data结构
          let apiData;
          if (data.data && Array.isArray(data.data)) {
            // 新的API响应格式: {data: [...]}
            apiData = data.data;
            console.log('[API] 检测到嵌套data结构，已正确提取数据数组');
            console.log('[API] 提取的apiData:', JSON.stringify(apiData, null, 2));
          } else if (Array.isArray(data)) {
            // 旧的API响应格式: [...]
            apiData = data;
            console.log('[API] 直接使用响应中的数据数组');
            console.log('[API] 直接使用的apiData:', JSON.stringify(apiData, null, 2));
          } else {
            // 未知格式或空数据
            apiData = [];
            console.warn('[API] 无法识别的数据格式或空数据:', data);
          }

          if (apiData.length > 0) {
            console.log('[API] 准备调用formatDueProjectTasksData，传入数据长度:', apiData.length);
            // 将API返回的数据转换为前端组件需要的格式
            const formattedData = formatDueProjectTasksData(apiData);
            console.log('[API] formatDueProjectTasksData返回结果:', JSON.stringify(formattedData, null, 2));

            resolve(formattedData);
          } else {
            console.warn('[API] 任务详情已报工时返回数据为空数组');
            resolve({
              success: false,
              message: "返回数据为空",
              data: {
                reportedHours: 0,
                tasks: []
              }
            });
          }
        } else {
          console.warn('[API] 未获取到任务详情已报工时数据或数据为空');
          resolve({
            success: false,
            message: "未获取到数据",
            data: {
              reportedHours: 0,
              tasks: []
            }
          });
        }
      })
      .catch(error => {
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;

        // 详细记录错误信息
        let errorDetails = '';
        let shouldRetry = false;

        if (error.response) {
          // 服务器返回了错误响应
          const status = error.response.status;
          errorDetails = `服务器错误(${status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取任务详情已报工时数据失败，HTTP状态: ${status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);

          // 只有5xx错误才重试
          shouldRetry = status >= 500 && status < 600;
        } else if (error.request) {
          // 请求已发送但没有收到响应
          if (error.code === 'ECONNABORTED') {
            errorDetails = `请求超时(${timeout}ms)`;
            console.error(`[API] 获取任务详情已报工时数据超时，耗时: ${requestDuration}ms`);
            shouldRetry = true; // 超时错误可以重试
          } else {
            errorDetails = `无响应: ${error.message}`;
            console.error(`[API] 获取任务详情已报工时数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
            shouldRetry = true; // 网络错误可以重试
          }
        } else {
          // 请求设置出错
          errorDetails = `请求错误: ${error.message}`;
          console.error(`[API] 获取任务详情已报工时数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
          shouldRetry = false; // 请求设置错误不重试
        }

        // 如果还可以重试且应该重试，则重试请求
        if (retryCount < maxRetries && shouldRetry) {
          retryCount++;
          console.log(`[API] 准备第${retryCount}次重试获取任务详情已报工时数据...`);

          // 延迟后重试，每次重试延迟时间增加
          const retryDelay = 1000 * retryCount; // 1秒 * 重试次数
          setTimeout(() => {
            executeQuery().then(resolve).catch(reject);
          }, retryDelay);
        } else {
          reject(new Error(`获取任务详情已报工时失败: ${errorDetails}`));
        }
      });
    });
  };

  // 执行查询
  return executeQuery();
}

/**
 * 格式化任务详情专用API返回的已报工时数据为组件所需格式
 * @param {Array} apiData API返回的原始数据
 * @returns {Object} 格式化后的数据，包含已报工时总数和任务详情
 */
function formatDueProjectTasksData(apiData) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化任务详情已报工时数据: 无数据可格式化');
    return {
      success: false,
      message: "无已报工时数据返回",
      data: {
        reportedHours: 0,
        tasks: []
      }
    };
  }

  console.log('[API] 开始格式化任务详情已报工时数据, 行数:', apiData.length);
  console.log('[API] 原始apiData:', JSON.stringify(apiData, null, 2));

  try {
    let totalReportedHours = 0;

    // 增强的API响应格式处理
    console.log('[API] 任务详情已报工时 - 检查第一条数据:', apiData[0]);
    console.log('[API] 任务详情已报工时API返回的完整数据结构:', JSON.stringify(apiData, null, 2));

    // 检查所有可能的字段名，Property0 是最常见的字段
    const possibleFields = ['Property0', 'Property', 'reportedHours', 'FReportedHours', 'ReportedHours', 'FSUMTIME'];
    let foundField = null;
    let fieldValue = null;

    // 遍历所有可能的字段名
    for (const field of possibleFields) {
      if (apiData[0] && apiData[0][field] !== undefined) {
        foundField = field;
        fieldValue = apiData[0][field];
        console.log(`[API] ✓ 任务详情找到已报工时字段: ${field}, 值: ${fieldValue}, 类型: ${typeof fieldValue}`);
        break;
      }
    }

    if (!foundField) {
      console.warn('[API] ⚠️ 任务详情未找到预期的已报工时字段，可用字段:', Object.keys(apiData[0] || {}));
    }

    if (foundField && fieldValue !== null && fieldValue !== undefined) {
      // 确保转换为数字
      totalReportedHours = Number(fieldValue) || 0;
      console.log(`[API] 从字段 ${foundField} 获取任务详情已报工时: ${totalReportedHours}`);

      console.log(`[API] 任务详情已报工时数据格式化完成，总已报工时: ${totalReportedHours.toFixed(2)}h`);

      // 返回格式化后的数据
      return {
        success: true,
        message: "任务详情已报工时数据获取成功",
        data: {
          reportedHours: totalReportedHours,
          tasks: []
        }
      };
    } else {
      console.log('[API] 未找到预期的字段，尝试其他格式处理');
      console.log('[API] 第一条数据的所有字段:', Object.keys(apiData[0] || {}));

      // 如果不是新格式，返回0
      console.warn('[API] 任务详情已报工时数据格式无法识别');
      return {
        success: false,
        message: "数据格式无法识别",
        data: {
          reportedHours: 0,
          tasks: []
        }
      };
    }
  } catch (error) {
    console.error('[API] 格式化任务详情已报工时数据时出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化任务详情已报工时数据错误: ${error.message}`,
      data: {
        reportedHours: 0,
        tasks: []
      }
    };
  }
}

/**
 * 获取今日任务数据
 * @param {string} userName 用户名（userIdName）
 * @param {string} targetDate 目标日期，格式为YYYY-MM-DD，默认为今天
 * @param {Object} options 可选配置项
 * @returns {Promise} 返回包含今日任务数据的Promise
 */
export function getTodayTasksData(userName, targetDate = null, options = {}) {
  const timeout = options.timeout || 30000; // 默认30秒超时
  const maxRetries = options.retries || 1; // 默认重试1次
  
  // 如果没有提供目标日期，使用今天
  if (!targetDate) {
    const today = new Date();
    targetDate = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  }
  
  // 构建今天的开始和结束时间（格式：2023-06-05T23:59:00）
  const dateBeg = `${targetDate}T00:00:00`;
  const dateEnd = `${targetDate}T23:59:00`;
  
  let retryCount = 0;
  
  console.log(`[API] 开始获取今日任务数据，用户: ${userName}, 日期: ${targetDate}`);
  
  // 创建执行查询的函数
  const executeQuery = () => {
    return new Promise((resolve, reject) => {
      // 构建请求参数
      const queryData = {
        parameter: {
          data: {
            userName: userName,
            dateBeg: dateBeg,
            dateEnd: dateEnd
          }
        }
      };
      
      // 记录请求开始时间
      const requestStartTime = Date.now();
      console.log(`[API] 今日任务请求参数:`, JSON.stringify(queryData, null, 2));
      
      axios({
        method: 'post',
        url: 'http://140.249.162.74:81/K3Cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.GetUserDueProjectTask,JR.K3.PLM.WorkhourReport.common.kdsvc',
        data: queryData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: timeout
      })
      .then(response => {
        // 记录请求完成时间和耗时
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        console.log(`[API] 获取今日任务数据成功，耗时: ${requestDuration}ms`);
        
        const data = response.data;
        
        // 打印API原始响应数据
        console.log('[API] 今日任务原始响应数据:', JSON.stringify(data, null, 2));
        
        if (data && data.data && Array.isArray(data.data)) {
          // 将API返回的数据转换为前端组件需要的格式
          const formattedData = formatTodayTasksData(data.data, targetDate);
          
          console.log(`[API] 今日任务数据格式化完成，任务数量: ${formattedData.data.tasks.length}`);
          resolve(formattedData);
        } else {
          console.warn('[API] 今日任务返回数据格式不符合预期:', data);
          resolve({
            success: false,
            message: "今日任务数据格式不符合预期",
            data: {
              tasks: []
            }
          });
        }
      })
      .catch(error => {
        const requestEndTime = Date.now();
        const requestDuration = requestEndTime - requestStartTime;
        
        // 详细记录错误信息
        let errorDetails = '';
        
        if (error.response) {
          errorDetails = `服务器错误(${error.response.status}): ${error.response.data?.Message || error.message}`;
          console.error(`[API] 获取今日任务数据失败，HTTP状态: ${error.response.status}, 消息: ${error.response.data?.Message || error.message}, 耗时: ${requestDuration}ms`);
        } else if (error.request) {
          if (error.code === 'ECONNABORTED') {
            errorDetails = `请求超时(${timeout}ms)`;
            console.error(`[API] 获取今日任务数据超时，耗时: ${requestDuration}ms`);
          } else {
            errorDetails = `无响应: ${error.message}`;
            console.error(`[API] 获取今日任务数据未收到响应，错误: ${error.message}, 耗时: ${requestDuration}ms`);
          }
        } else {
          errorDetails = `请求错误: ${error.message}`;
          console.error(`[API] 获取今日任务数据请求错误，消息: ${error.message}, 耗时: ${requestDuration}ms`);
        }
        
        // 如果还可以重试，则重试请求
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`[API] 准备第${retryCount}次重试获取今日任务数据...`);
          
          // 延迟后重试
          const retryDelay = 1000 * retryCount;
          setTimeout(() => {
            executeQuery().then(resolve).catch(reject);
          }, retryDelay);
        } else {
          reject(new Error(`获取今日任务失败: ${errorDetails}`));
        }
      });
    });
  };
  
  // 执行查询
  return executeQuery();
}

/**
 * 格式化今日任务API返回的数据为组件所需格式
 * @param {Array} apiData 今日任务API返回的原始数据
 * @param {string} targetDate 目标日期
 * @returns {Object} 格式化后的数据，包含任务列表
 */
function formatTodayTasksData(apiData, targetDate) {
  if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
    console.log('[API] 格式化今日任务数据: 无数据可格式化');
    return {
      success: false,
      message: "今日无任务数据",
      data: {
        tasks: []
      }
    };
  }
  
  console.log('[API] 开始格式化今日任务数据, 任务数:', apiData.length);
  
  try {
    const tasks = apiData.map((item, index) => {
      // 生成唯一ID
      const uniqueId = `today-task-${Date.now()}-${index}`;
      
      // 数据字段映射
      const projectName = item.fprojectname || '';
      const projectCode = item.fprojectcode || '';
      const taskName = item.ftaskname || '';
      const taskCode = item.ftaskcode || '';
      const progress = parseFloat(item.FTSKPERCENTCOMPLETE) || 0;
      
      console.log(`[API] 处理今日任务 ${index + 1}: 项目="${projectName}", 任务="${taskName}", 进度=${progress}%`);

      // 调试：检查FID和FProject字段
      console.log(`[API] 今日任务 ${index + 1} 原始ID字段:`, {
        FID: item.FID,
        FProject: item.FProject,
        fid: item.fid,
        fproject: item.fproject,
        FPROJECTID: item.FPROJECTID,
        FTASKID: item.FTASKID,
        allKeys: Object.keys(item).filter(key => key.toLowerCase().includes('id') || key.toLowerCase().includes('project') || key.toLowerCase().includes('fid'))
      });
      
      return {
        id: uniqueId,
        project: projectCode,
        projectName: projectName,
        taskName: taskName,
        taskCode: taskCode,
        taskContent: '', // 工作内容需要用户填写
        regularHours: 0, // 汇报工时需要用户填写
        overtimeHours: 0, // 加班工时需要用户填写
        progress: progress,
        date: targetDate,
        status: 'a', // 默认状态为暂存
        statusDisplay: '暂存',
        isProjectLevel: !taskName, // 如果没有任务名称，则视为项目级别
        plannedHours: parseFloat(item.FPLANWORKHOURS) || 0,
        startDate: item.FTSKPLANNEDST ? item.FTSKPLANNEDST.split('T')[0] : '',
        endDate: item.FTSKPLANNEDED ? item.FTSKPLANNEDED.split('T')[0] : '',
        plannedStartDate: item.FTSKPLANNEDST ? item.FTSKPLANNEDST.split('T')[0] : '',
        plannedEndDate: item.FTSKPLANNEDED ? item.FTSKPLANNEDED.split('T')[0] : '',
        position: '', // 岗位需要用户选择
        positionName: '', // 岗位名称需要用户选择
        // 添加用于已报工时查询的关键字段（尝试多种可能的字段名）
        ftaskId: item.FTASKID || item.FID || item.ftaskid || item.fid || 0, // 任务ID，用于已报工时查询
        fprojectId: item.FPROJECTID || item.FProject || item.fprojectid || item.fproject || '', // 项目ID，用于已报工时查询
        // 保存原始数据以备后用
        originalData: item
      };
    });
    
    console.log(`[API] 今日任务数据格式化完成，共 ${tasks.length} 条任务记录`);
    
    return {
      success: true,
      message: `成功获取${tasks.length}个今日任务`,
      data: {
        tasks: tasks
      }
    };
  } catch (error) {
    console.error('[API] 格式化今日任务数据时出错:', error.message, error.stack);
    return {
      success: false,
      message: `格式化今日任务数据错误: ${error.message}`,
      data: {
        tasks: []
      }
    };
  }
}

/**
 * 获取耗费类型数据
 * @param {Object} options 可选配置项
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认24小时
 * @returns {Promise} 返回包含耗费类型数据的Promise
 */
export function getBurnOffTypeData(options = {}) {
  const forceRefresh = options.forceRefresh || false;
  const cacheExpiry = options.cacheExpiry || 24 * 60 * 60 * 1000; // 默认24小时
  
  // 缓存键
  const cacheKey = 'burnoff_type_data';
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的耗费类型数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除耗费类型数据缓存`);
  }
  
  // 构建请求参数
  const requestData = {
    data: {
      FormId: "PBEW_BurnOffType",
      FieldKeys: "FNumber,fname",
      FilterString: [],
      OrderString: "",
      TopRowCount: 0,
      StartRow: 0,
      Limit: 2000,
      SubSystemId: ""
    }
  };
  
  console.log(`[API] 开始获取耗费类型数据`);
  const requestStartTime = Date.now();
  
  // 发送API请求
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    // 记录请求完成时间和耗时
    const requestEndTime = Date.now();
    const requestDuration = requestEndTime - requestStartTime;
    console.log(`[API] 获取耗费类型数据成功，耗时: ${requestDuration}ms`);
    
    const data = response.data;
    
    // 打印API原始响应数据
    console.log('[API] 耗费类型数据原始响应:', JSON.stringify(data, null, 2));
    
    // 处理API返回的数据
    let burnOffTypeOptions = [];
    
    if (Array.isArray(data) && data.length > 0) {
      // 处理返回的数组数据，格式：[["001","一般会议"],["002","节假日"],...]
      burnOffTypeOptions = data.map(item => {
        if (Array.isArray(item) && item.length >= 2) {
          return {
            text: item[1] || '未知类型',     // fname - 显示名称
            value: item[0] || '',           // FNumber - 编码值
            code: item[0] || '',            // 明确存储编码
            name: item[1] || '未知类型'     // 明确存储名称
          };
        }
        return null;
      }).filter(Boolean); // 过滤掉null值
    }
    
    // 如果没有获取到耗费类型数据，使用默认列表
    if (burnOffTypeOptions.length === 0) {
      console.log('[API] 未获取到耗费类型数据，使用默认列表');
      burnOffTypeOptions = [
        { text: '一般会议', value: '001', code: '001', name: '一般会议' },
        { text: '节假日', value: '002', code: '002', name: '节假日' },
        { text: '请假', value: '003', code: '003', name: '请假' },
        { text: '出差', value: '004', code: '004', name: '出差' },
        { text: '学习', value: '005', code: '005', name: '学习' },
        { text: '其他', value: '006', code: '006', name: '其他' }
      ];
    }
    
    // 打印处理后的耗费类型选项
    console.log(`[API] 处理后的耗费类型选项数量: ${burnOffTypeOptions.length}`);
    console.log('[API] 耗费类型选项示例:', burnOffTypeOptions.slice(0, 3));
    
    // 缓存结果
    setCache(cacheKey, { success: true, data: burnOffTypeOptions }, cacheExpiry);
    
    return {
      success: true,
      data: burnOffTypeOptions
    };
  })
  .catch(error => {
    const requestEndTime = Date.now();
    const requestDuration = requestEndTime - requestStartTime;
    console.error(`[API] 获取耗费类型数据失败，耗时: ${requestDuration}ms`, error);
    
    // 使用默认耗费类型列表作为备份
    const defaultBurnOffTypeOptions = [
      { text: '一般会议', value: '001', code: '001', name: '一般会议' },
      { text: '节假日', value: '002', code: '002', name: '节假日' },
      { text: '请假', value: '003', code: '003', name: '请假' },
      { text: '出差', value: '004', code: '004', name: '出差' },
      { text: '学习', value: '005', code: '005', name: '学习' },
      { text: '其他', value: '006', code: '006', name: '其他' }
    ];
    
    return {
      success: false,
      message: error.message || '获取耗费类型数据失败',
      data: defaultBurnOffTypeOptions
    };
  });
}

/**
 * 获取临时任务类型数据
 * @param {Object} options 可选配置项
 * @param {boolean} options.forceRefresh 是否强制刷新缓存，默认false
 * @param {number} options.cacheExpiry 缓存有效期(毫秒)，默认24小时
 * @returns {Promise} 返回包含临时任务类型数据的Promise
 */
export function getTmpTaskTypeData(options = {}) {
  const forceRefresh = options.forceRefresh || false;
  const cacheExpiry = options.cacheExpiry || 24 * 60 * 60 * 1000; // 默认24小时
  
  // 缓存键
  const cacheKey = 'tmp_task_type_data';
  
  // 优先检查缓存，除非强制刷新
  if (!forceRefresh) {
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      console.log(`[API] 使用缓存的临时任务类型数据，缓存年龄: ${(cachedData.age / 1000).toFixed(2)}秒`);
      return Promise.resolve(cachedData.data);
    }
  } else if (forceRefresh) {
    // 如果强制刷新，移除现有缓存
    removeCache(cacheKey);
    console.log(`[API] 强制刷新，已移除临时任务类型数据缓存`);
  }
  
  // 构建请求参数
  const requestData = {
    data: {
      FormId: "PBEW_TmpTaskType",
      FieldKeys: "FNumber,fname",
      FilterString: [],
      OrderString: "",
      TopRowCount: 0,
      StartRow: 0,
      Limit: 2000,
      SubSystemId: ""
    }
  };
  
  console.log(`[API] 开始获取临时任务类型数据`);
  const requestStartTime = Date.now();
  
  // 发送API请求
  return axios({
    method: 'post',
    url: 'http://140.249.162.74:81/K3Cloudxm/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc',
    data: requestData,
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    timeout: 30000 // 30秒超时
  })
  .then(response => {
    // 记录请求完成时间和耗时
    const requestEndTime = Date.now();
    const requestDuration = requestEndTime - requestStartTime;
    console.log(`[API] 获取临时任务类型数据成功，耗时: ${requestDuration}ms`);
    
    const data = response.data;
    
    // 打印API原始响应数据
    console.log('[API] 临时任务类型数据原始响应:', JSON.stringify(data, null, 2));
    
    // 处理API返回的数据
    let tmpTaskTypeOptions = [];
    
    if (Array.isArray(data) && data.length > 0) {
      // 处理返回的数组数据，格式：[["YFRW","研发任务"],["SCZC","生产支持"],...]
      tmpTaskTypeOptions = data.map(item => {
        if (Array.isArray(item) && item.length >= 2) {
          return {
            text: item[1] || '未知类型',     // fname - 显示名称
            value: item[0] || '',           // FNumber - 编码值
            code: item[0] || '',            // 明确存储编码
            name: item[1] || '未知类型'     // 明确存储名称
          };
        }
        return null;
      }).filter(Boolean); // 过滤掉null值
    }
    
    // 如果没有获取到临时任务类型数据，使用默认列表
    if (tmpTaskTypeOptions.length === 0) {
      console.log('[API] 未获取到临时任务类型数据，使用默认列表');
      tmpTaskTypeOptions = [
        { text: '研发任务', value: 'YFRW', code: 'YFRW', name: '研发任务' },
        { text: '生产支持', value: 'SCZC', code: 'SCZC', name: '生产支持' },
        { text: '销售支持', value: 'XSZC', code: 'XSZC', name: '销售支持' }
      ];
    }
    
    // 打印处理后的临时任务类型选项
    console.log(`[API] 处理后的临时任务类型选项数量: ${tmpTaskTypeOptions.length}`);
    console.log('[API] 临时任务类型选项示例:', tmpTaskTypeOptions.slice(0, 3));
    
    // 缓存结果
    setCache(cacheKey, { success: true, data: tmpTaskTypeOptions }, cacheExpiry);
    
    return {
      success: true,
      data: tmpTaskTypeOptions
    };
  })
  .catch(error => {
    const requestEndTime = Date.now();
    const requestDuration = requestEndTime - requestStartTime;
    console.error(`[API] 获取临时任务类型数据失败，耗时: ${requestDuration}ms`, error);
    
    // 使用默认临时任务类型列表作为备份
    const defaultTmpTaskTypeOptions = [
      { text: '研发任务', value: 'YFRW', code: 'YFRW', name: '研发任务' },
      { text: '生产支持', value: 'SCZC', code: 'SCZC', name: '生产支持' },
      { text: '销售支持', value: 'XSZC', code: 'XSZC', name: '销售支持' }
    ];
    
    return {
      success: false,
      message: error.message || '获取临时任务类型数据失败',
      data: defaultTmpTaskTypeOptions
    };
  });
}