<template>
  <div id="app">
    <!-- <img alt="Vue logo" src="./assets/logo.png">
    <HelloWorld msg="Welcome to Your Vue.js App"/> -->
    <router-view></router-view>
  </div>
</template>

<script>
// import HelloWorld from './components/HelloWorld.vue'

export default {
  name: 'App',
  components: {
    // HelloWorld
  }
}
</script>

<style>
/* #app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
} */

/* 全局样式设置 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 防止滚动条导致页面跳动 */
html {
  overflow-y: scroll;
}

/* 全屏容器 */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 移动设备优化 */
@media (max-width: 768px) {
  body {
    font-size: 15px;
    touch-action: manipulation;
  }
  
  /* 优化触摸反馈 */
  .active-touch {
    opacity: 0.7 !important;
    transition: opacity 0.15s !important;
    transform: scale(0.97) !important;
  }
  
  /* 优化表单元素 */
  input, button, select, textarea {
    font-size: 16px !important; /* 防止iOS缩放 */
  }
}

/* iOS优化 */
.ios-device {
  -webkit-tap-highlight-color: transparent;
}

/* iOS高度修复 */
@supports (-webkit-touch-callout: none) {
  .full-height {
    height: -webkit-fill-available;
  }
}

/* 避免用户选择文本 */
.no-select {
  user-select: none;
  -webkit-user-select: none;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: #e0e0e0;
  }
}

/* 过渡动画类 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 对于不喜欢动画的用户 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* 全局样式设置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 兼容性修复 */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  display: none;
}

/* 优化按钮点击状态 */
button, 
[role="button"],
.el-button {
  cursor: pointer;
  touch-action: manipulation;
}
</style>
