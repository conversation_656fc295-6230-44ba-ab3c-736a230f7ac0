<template>
  <div class="feature-grid">
    <div class="feature-tabs">
      <van-tabs v-model="activeTabIndex" animated swipeable>
        <van-tab title="协同办公">
          <div class="grid-container">
            <div class="grid-item" v-for="feature in tabs[0].features" :key="feature.id" @click="navigateTo(feature.path)">
              <van-icon :name="feature.icon" size="28" class="grid-icon" />
              <div class="grid-text">{{ feature.name }}</div>
            </div>
          </div>
        </van-tab>
        <van-tab title="企业管理">
          <div class="grid-container">
            <div class="grid-item" v-for="feature in tabs[1].features" :key="feature.id" @click="navigateTo(feature.path)">
              <van-icon :name="feature.icon" size="28" class="grid-icon" />
              <div class="grid-text">{{ feature.name }}</div>
            </div>
          </div>
        </van-tab>
        <van-tab title="自建应用">
          <div class="grid-container">
            <div class="grid-item" v-for="feature in tabs[2].features" :key="feature.id" @click="navigateTo(feature.path)">
              <van-icon :name="feature.icon" size="28" class="grid-icon" />
              <div class="grid-text">{{ feature.name }}</div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    
    <div class="quick-entry">
      <div class="entry-header">
        <van-icon name="arrow" color="#2d8cf0" />
        <span>快捷入口</span>
      </div>
      <div class="grid-container">
        <div class="grid-item" v-for="feature in popularFeatures" :key="feature.id" @click="navigateTo(feature.path)">
          <van-icon :name="feature.icon" size="28" class="grid-icon" />
          <div class="grid-text">{{ feature.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';

export default {
  name: 'FeatureGrid',
  data() {
    return {
      activeTabIndex: 0
    };
  },
  computed: {
    ...mapState({
      appFeatures: state => state.appFeatures
    }),
    ...mapGetters({
      tabs: 'featuresByTab'
    }),
    popularFeatures() {
      // 返回前4个功能作为快捷入口
      return this.appFeatures.slice(0, 4);
    }
  },
  watch: {
    activeTabIndex(newVal) {
      this.setActiveTab(newVal);
    }
  },
  methods: {
    ...mapMutations({
      setActiveTab: 'SET_ACTIVE_TAB'
    }),
    navigateTo(path) {
      this.$router.push(path).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.feature-grid {
  padding: 10px 0;
  background-color: #f5f6fa;
  
  .feature-tabs {
    background-color: #fff;
    margin-bottom: 15px;
    
    ::v-deep .van-tabs__wrap {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
    }
    
    ::v-deep .van-tabs__line {
      background-color: #2d8cf0;
      height: 3px;
    }
  }
  
  .quick-entry {
    background-color: #fff;
    padding: 15px;
    
    .entry-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-size: 17px;
      font-weight: bold;
      
      .van-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }
  }
  
  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 10px 0;
    gap: 5px;
  }
  
  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px;
    cursor: pointer;
    
    &:active {
      opacity: 0.7;
    }
    
    .grid-icon {
      margin-bottom: 10px;
      color: #2d8cf0;
      background-color: #ecf5ff;
      padding: 12px;
      border-radius: 50%;
      box-shadow: 0 2px 5px rgba(45, 140, 240, 0.2);
    }
    
    .grid-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}

// 响应式设计 - 桌面端适配
@media screen and (min-width: 768px) {
  .feature-grid {
    max-width: 768px;
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
    
    .feature-tabs {
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 15px;
    }
    
    .quick-entry {
      border-radius: 10px;
      overflow: hidden;
    }
    
    .grid-container {
      padding: 15px 0;
    }
    
    .grid-item {
      padding: 15px;
      
      .grid-text {
        font-size: 15px;
      }
    }
  }
}
</style> 