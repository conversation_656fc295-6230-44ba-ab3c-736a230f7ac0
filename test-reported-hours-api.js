// 测试已报工时API的简单脚本
// 这个文件用于验证API调用是否正常工作

import { getUserDueProjectTasks } from './src/api/report.js';

// 测试函数
async function testReportedHoursAPI() {
  console.log('开始测试已报工时API...');
  
  try {
    // 使用您提供的测试ID: 10000
    const testId = 10000;
    console.log(`测试任务/项目ID: ${testId}`);
    
    const response = await getUserDueProjectTasks(testId);
    
    console.log('API响应结果:');
    console.log('- 成功状态:', response.success);
    console.log('- 消息:', response.message);
    
    if (response.success && response.data) {
      console.log('- 总已报工时:', response.data.reportedHours, '小时');
      console.log('- 任务数量:', response.data.tasks.length);
      
      if (response.data.tasks.length > 0) {
        console.log('- 第一个任务详情:');
        const firstTask = response.data.tasks[0];
        console.log('  * 任务ID:', firstTask.taskId);
        console.log('  * 项目ID:', firstTask.projectId);
        console.log('  * 任务名称:', firstTask.taskName);
        console.log('  * 项目名称:', firstTask.projectName);
        console.log('  * 已报工时:', firstTask.reportedHours, '小时');
        console.log('  * 报告日期:', firstTask.reportDate);
        console.log('  * 状态:', firstTask.status);
      }
    } else {
      console.log('API调用失败或无数据');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js环境
  testReportedHoursAPI();
} else {
  // 浏览器环境，将测试函数暴露到全局
  window.testReportedHoursAPI = testReportedHoursAPI;
  console.log('测试函数已暴露到全局: window.testReportedHoursAPI()');
}

export { testReportedHoursAPI };
