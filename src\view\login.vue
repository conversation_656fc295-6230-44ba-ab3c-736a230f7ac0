<template>
  <!-- 登录页面主容器，根据设备类型添加不同的类名 -->
  <div class="login-container" :class="deviceClass">
    <!-- 背景浮动元素，仅在桌面端显示 -->
    <div class="floating-elements" v-if="!isMobile">
      <div class="floating-element" v-for="i in 5" :key="i"></div>
    </div>
    
    <!-- 移动端波浪背景元素 -->
    <div class="wave-container" v-if="isMobile">
      <div class="wave wave-1"></div>
      <div class="wave wave-2"></div>
      <div class="wave wave-3"></div>
    </div>
    
    <!-- 主内容区 -->
    <div class="login-content">
      <!-- PC端：左侧品牌区 + 右侧表单区 -->
      <template v-if="!isMobile">
        <div class="brand-area">
          <div class="brand-content">
            <img src="../assets/logo.png" alt="logo" class="logo">
            <h1 class="system-name">企业管理云服务专家</h1>
            <p class="slogan"> </p>
          </div>
          <div class="copyright">
            © {{ currentYear }} 金蝶软件（中国）有限公司 版权所有
          </div>
        </div>
        
        <div class="form-area">
          <div class="form-container">
            <h2 class="welcome-text">欢迎登录</h2>
            <login-form 
              @login="handleLogin" 
              @forget-password="handleForgetPassword"
              @keyboard-change="handleKeyboardChange"
              ref="loginForm"
              :loading="loading"
            />
          </div>
        </div>
      </template>
      
      <!-- 移动端：垂直布局 -->
      <template v-else>
        <div class="mobile-header">
          <img src="../assets/logo.png" alt="logo" class="logo">
          <div class="header-text">
            <h1 class="system-name">企业管理云服务专家</h1>
            <p class="slogan" v-if="screenWidth > 320"> </p>
          </div>
        </div>
        
        <div class="mobile-form-container">
          <div class="form-card">
            <h2 class="welcome-text">登录</h2>
            <login-form 
              @login="handleLogin" 
              @forget-password="handleForgetPassword"
              @keyboard-change="handleKeyboardChange"
              :is-mobile="true"
              ref="loginForm"
              :loading="loading"
            />
          </div>
          
          <div class="bottom-content">
            <div class="mobile-copyright">
              © {{ currentYear }} 金蝶软件（中国）有限公司
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
// 导入登录表单组件
import LoginForm from '../components/LoginForm.vue'
import { verifyLogin } from '../api/auth'
import { refreshSessionId, getSessionStatus } from '../utils/session'

export default {
  name: 'LoginPage',
  components: {
    LoginForm
  },
  data() {
    return {
      isMobile: false,
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      isKeyboardOpen: false,
      loading: false,
      sessionId: null, // 存储KDSVCSessionId，但不再影响界面显示
      loginStatus: {
        authorized: false,
        userName: '',
        errorMessage: ''
      }
    }
  },
  computed: {
    // 当前年份
    currentYear() {
      return new Date().getFullYear()
    },
    // 设备类型类名
    deviceClass() {
      return {
        'mobile-device': this.isMobile,
        'desktop-device': !this.isMobile,
        'keyboard-open': this.isKeyboardOpen,
        'is-ios': this.isIOS,
        'is-android': this.isAndroid
      }
    },
    // 是否为iOS设备
    isIOS() {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
    },
    // 是否为安卓设备
    isAndroid() {
      return /Android/.test(navigator.userAgent)
    }
  },
  created() {
    this.checkDeviceType()
    window.addEventListener('resize', this.handleResize)
  },
  mounted() {
    // 在视图加载完成后应用进入动画
    this.$nextTick(() => {
      document.body.classList.add('login-page-loaded')
    })
    
    // 页面加载时自动获取SessionId，但不显示登录成功
    this.getKingdeeSession()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    document.body.classList.remove('login-page-loaded')
  },
  methods: {
    // 自动获取金蝶SessionId
    getKingdeeSession() {
      console.log('正在获取金蝶SessionId...')
      
      // 检查会话状态
      const sessionStatus = getSessionStatus();
      if (sessionStatus.valid) {
        console.log('已有有效的金蝶SessionId，剩余有效期:', sessionStatus.expiresInFormatted);
        this.sessionId = sessionStatus.sessionId;
        return;
      }
      
      // 不显示loading状态，后台静默获取
      refreshSessionId()
        .then(sessionId => {
          // 保存会话ID
          this.sessionId = sessionId;
          console.log('成功获取金蝶SessionId:', sessionId);
        })
        .catch(error => {
          console.error('获取金蝶SessionId失败:', error.message);
          // 如果获取失败，可以稍后重试
          setTimeout(() => {
            this.getKingdeeSession();
          }, 3000);
        });
    },
    
    // 跳转到主页
    goToMain() {
      this.$router.push('/main')
    },
    
    // 处理窗口大小改变
    handleResize() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
      this.checkDeviceType()
    },
    // 检查设备类型
    checkDeviceType() {
      // 基于屏幕宽度判断设备类型
      this.isMobile = this.screenWidth <= 768
      
      // 根据设备类型添加body类
      document.body.classList.toggle('mobile-mode', this.isMobile)
      document.body.classList.toggle('desktop-mode', !this.isMobile)
      
      // 如果是移动设备，检测操作系统并添加特定类
      if (this.isMobile) {
        document.body.classList.toggle('ios-device', this.isIOS)
        document.body.classList.toggle('android-device', this.isAndroid)
      }
    },
    // 处理键盘状态变化
    handleKeyboardChange(isVisible) {
      this.isKeyboardOpen = isVisible
    },
    // 处理登录
    handleLogin(formData) {
      this.loading = true
      
      // 使用表单数据
      const { username, password, remember } = formData
      console.log(`用户登录尝试: ${username}, 记住密码: ${remember}`)
      
      // 每次登录时强制刷新SessionId，确保获取最新的登录凭证
      refreshSessionId(true)
        .then(sessionId => {
          this.sessionId = sessionId;
          console.log('登录时已强制刷新SessionId:', sessionId);
          
          // 调用验证函数验证用户凭据
          return verifyLogin(username, password);
        })
        .then(result => {
          if (result.success) {
            // 登录成功
            const userName = result.userData.FEmpName || username
            const userId = result.userData.Fid || ''
            const userIdName = result.userData.FUserIdName || ''
            const userEntityId = result.userData.FUserId || ''
            console.log('登录成功，用户数据:', result.userData)
            
            // 更新登录状态
            this.loginStatus.authorized = true
            this.loginStatus.userName = userName
            
            // 保存用户信息到本地存储
            localStorage.setItem('work_report_user', JSON.stringify({
              username: username,
              userName: userName,
              userId: userId, // 保存Fid用于修改密码
              userIdName: userIdName, // 保存FUserId.FName
              userEntityId: userEntityId, // 保存FUserId
              lastLogin: new Date().toISOString()
            }))
            
            // 重置登录表单状态
            if (this.$refs.loginForm) {
              this.$refs.loginForm.reset()
            }
            
            // 登录成功后直接跳转到主页面
            this.goToMain()
          } else {
            // 登录失败
            this.$message.error(result.message || '用户名或密码错误')
          }
        })
        .catch(error => {
          this.$message.error(`登录验证失败: ${error.message}`)
          console.error('登录验证错误:', error)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 处理忘记密码
    handleForgetPassword() {
      this.$dialog.alert({
        title: '忘记密码',
        message: '请联系管理员'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$kingdee-primary: #276ff5; // 金蝶主蓝色
$kingdee-secondary: #276ff5; // 浅蓝色
$kingdee-dark-blue: #276ff5; // 深蓝色
$kingdee-light-blue: #40a9ff; // 亮蓝色
$kingdee-accent: #36cfc9; // 蓝绿色强调色
$kingdee-gradient: linear-gradient(135deg, $kingdee-primary 0%, $kingdee-secondary 100%); 
$kingdee-gradient-vibrant: linear-gradient(135deg, $kingdee-dark-blue 0%, $kingdee-primary 50%, $kingdee-light-blue 100%);
$kingdee-gradient-accent: linear-gradient(135deg, $kingdee-primary 0%, $kingdee-accent 100%);

// 断点变量
$breakpoint-xs: 375px;  // 超小屏幕（小型手机）
$breakpoint-sm: 576px;  // 小屏幕（手机）
$breakpoint-md: 768px;  // 中等屏幕（平板）
$breakpoint-lg: 992px;  // 大屏幕（小桌面）
$breakpoint-xl: 1200px; // 超大屏幕（大桌面）

// 根元素基础样式 - 锁定为浅色模式，不受系统深色模式影响
:root {
  --login-background: #f5f7fa;
  --login-text-color: #333;
  --login-text-light: #666;
  --login-border-radius: 12px;
  --login-shadow: 0 10px 40px -10px rgba(0,0,0,0.1);
  --login-content-bg: #fff;
  --input-border: #dcdfe6;
  --input-focus: #{$kingdee-primary};
  --transition-standard: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  --login-form-width: 360px;
  --card-shadow: 0 10px 30px -5px rgba(14, 92, 179, 0.15);
  --header-height: 140px;
  --button-shadow: 0 6px 16px -8px rgba(14, 92, 179, 0.5);
  
  /* 强制覆盖系统深色模式设置 - 登录页面始终保持浅色 */
  color-scheme: light !important;
}

/* 强制登录页面元素为浅色模式 */
.login-container {
  color-scheme: light !important;
  background-color: var(--login-background) !important;
  color: var(--login-text-color) !important;
}

// 登录页布局容器
.login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--login-background);
  background-image: linear-gradient(45deg, rgba(245,247,250,0.6) 0%, rgba(228,233,242,0.6) 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
  
  // 移动设备优化
  &.mobile-device {
    padding: 0;
    align-items: stretch;
    background-image: linear-gradient(135deg, rgba($kingdee-dark-blue, 0.02) 0%, rgba($kingdee-primary, 0.05) 50%, rgba($kingdee-light-blue, 0.02) 100%);
    
    // iOS优化
    &.is-ios {
      min-height: -webkit-fill-available; // 解决iOS高度问题
      
      @supports (padding-top: env(safe-area-inset-top)) {
        padding-top: env(safe-area-inset-top, 0);
        padding-bottom: env(safe-area-inset-bottom, 0);
      }
    }
    
    // 键盘打开状态
    &.keyboard-open {
      .login-content {
        margin-bottom: 250px;
      }
      
      .mobile-header {
        padding: 10px 15px;
        height: auto;
        min-height: 60px;
        transform: translateY(-10px);
        
        .logo {
          width: 40px;
          height: 40px;
        }
        
        .system-name {
          font-size: 16px;
          margin: 0;
        }
        
        .slogan {
          display: none;
        }
      }
      
      .mobile-copyright {
        display: none;
      }

      .wave-container {
        opacity: 0.3;
        transform: translateY(-20px);
      }
    }
  }
  
  // 桌面设备样式
  &.desktop-device {
    .login-content {
      height: auto;
      min-height: 500px;
      max-height: 90vh;
      box-shadow: var(--login-shadow);
    }
  }
}

// 主内容区容器
.login-content {
  width: 100%;
  max-width: 900px;
  display: flex;
  border-radius: var(--login-border-radius);
  overflow: hidden;
  background-color: var(--login-content-bg);
  transition: var(--transition-standard);
  
  // 移动端样式
  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    max-width: 500px;
    border-radius: 0;
    box-shadow: none;
    height: 100vh;
  }
  
  // 超小屏幕优化
  @media (max-width: $breakpoint-xs) {
    max-width: 100%;
  }
}

// 品牌区样式
.brand-area {
  flex: 0 0 40%;
  background: white;
  color: $kingdee-primary;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 30px;
  position: relative;
  overflow: hidden;
  
  // 移动设备不单独显示品牌区
  @media (max-width: $breakpoint-md) {
    display: none;
  }
  
  // 品牌背景浮动元素
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle, rgba($kingdee-primary, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.6;
    z-index: 0;
  }
  
  .brand-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    z-index: 1;
    text-align: center;
    
    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
      filter: drop-shadow(0 4px 6px rgba($kingdee-primary, 0.2));
      transition: var(--transition-standard);
    }
    
    .system-name {
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 10px;
      color: $kingdee-primary;
      text-shadow: 0 2px 4px rgba($kingdee-primary, 0.1);
    }
    
    .slogan {
      font-size: 16px;
      opacity: 0.8;
      margin: 0;
      color: $kingdee-secondary;
    }
  }
  
  .copyright {
    font-size: 12px;
    opacity: 0.7;
    text-align: center;
    margin-top: 20px;
    color: $kingdee-secondary;
  }
}

// 表单区样式
.form-area {
  flex: 0 0 60%;
  padding: 40px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .form-container {
    width: 100%;
    max-width: var(--login-form-width);
    
    .welcome-text {
      font-size: 24px;
      color: var(--login-text-color);
      margin-bottom: 30px;
      text-align: center;
      font-weight: 500;
    }
  }
}

// 移动端头部样式
.mobile-header {
  background: white;
  color: $kingdee-primary;
  padding: 25px 20px 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--header-height);
  transition: var(--transition-standard);
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: 0 4px 20px -5px rgba($kingdee-dark-blue, 0.2);
  
  // 头部装饰元素
  &::before, &::after {
    content: '';
    position: absolute;
    background: rgba($kingdee-primary, 0.05);
    border-radius: 50%;
  }
  
  &::before {
    width: 150px;
    height: 150px;
    bottom: -100px;
    right: -50px;
  }
  
  &::after {
    width: 80px;
    height: 80px;
    top: -20px;
    left: 30px;
    opacity: 0.2;
  }
  
  .logo {
    width: 70px;
    height: 70px;
    margin-right: 15px;
    transition: var(--transition-standard);
    filter: drop-shadow(0 4px 6px rgba($kingdee-primary, 0.2));
    animation: pulse 3s infinite ease-in-out;
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
  }
  
  .header-text {
    display: flex;
    flex-direction: column;
    
    .system-name {
      font-size: 22px;
      margin: 0 0 5px;
      font-weight: 600;
      letter-spacing: 0.5px;
      color: $kingdee-primary;
      text-shadow: 0 2px 4px rgba($kingdee-primary, 0.1);
    }
    
    .slogan {
      font-size: 14px;
      margin: 0;
      opacity: 0.8;
      font-weight: 300;
      letter-spacing: 0.5px;
      color: $kingdee-secondary;
    }
  }
  
  @media (max-width: $breakpoint-xs) {
    padding: 20px 15px 30px;
    height: 120px;
    
    .logo {
      width: 60px;
      height: 60px;
    }
    
    .header-text {
      .system-name {
        font-size: 20px;
      }
      
      .slogan {
        font-size: 12px;
      }
    }
  }
}

// 移动端表单容器样式
.mobile-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: var(--login-content-bg);
  position: relative;
  z-index: 2;
  
  // 卡片式表单设计
  .form-card {
    margin: -25px 20px 20px;
    background: var(--login-content-bg);
    border-radius: 16px;
    box-shadow: var(--card-shadow);
    padding: 25px 20px;
    position: relative;
    overflow: hidden;
    
    // 卡片装饰元素
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: $kingdee-primary;
      z-index: 1;
    }
  }
  
  .welcome-text {
    font-size: 22px;
    color: var(--login-text-color);
    margin-bottom: 25px;
    text-align: center;
    font-weight: 600;
    position: relative;
    
    &::after {
      content: '';
      display: block;
      width: 40px;
      height: 3px;
      background: $kingdee-gradient;
      margin: 8px auto 0;
      border-radius: 3px;
    }
  }
  
  // 底部内容区
  .bottom-content {
    padding: 0 20px;
    margin-top: 20px;
  }
  
  @media (max-width: $breakpoint-xs) {
    .form-card {
      margin: -20px 15px 15px;
      padding: 20px 15px;
    }
    
    .welcome-text {
      font-size: 20px;
      margin-bottom: 20px;
      
      &::after {
        width: 30px;
        margin-top: 6px;
      }
    }
    
    .bottom-content {
      padding: 0 15px;
    }
  }
}

// 移动端版权信息
.mobile-copyright {
  text-align: center;
  font-size: 12px;
  color: var(--login-text-light);
  margin-top: 20px;
  padding-top: 15px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba($kingdee-primary, 0.2), transparent);
  }
}

// 浮动背景元素样式
.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  
  .floating-element {
    position: absolute;
    background-color: rgba(14, 92, 179, 0.03);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
    
    &:nth-child(1) {
      width: 150px;
      height: 150px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      width: 200px;
      height: 200px;
      bottom: 10%;
      right: 15%;
      animation-delay: -2s;
      background-color: rgba(24, 144, 255, 0.03);
    }
    
    &:nth-child(3) {
      width: 100px;
      height: 100px;
      top: 40%;
      right: 25%;
      animation-delay: -5s;
    }
    
    &:nth-child(4) {
      width: 180px;
      height: 180px;
      bottom: 30%;
      left: 20%;
      animation-delay: -8s;
      background-color: rgba(24, 144, 255, 0.02);
    }
    
    &:nth-child(5) {
      width: 120px;
      height: 120px;
      top: 20%;
      right: 10%;
      animation-delay: -12s;
    }
  }
}

// 浮动动画
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(10px);
  }
  50% {
    transform: translateY(10px) translateX(-15px);
  }
  75% {
    transform: translateY(15px) translateX(5px);
  }
}

// iOS特定优化
@supports (-webkit-touch-callout: none) {
  body.mobile-mode {
    min-height: -webkit-fill-available;
    
    &.keyboard-visible {
      min-height: 100vh;
    }
  }
}

// 页面加载动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 应用全局加载动画
body.login-page-loaded {
  .login-container {
    .login-content {
      animation: fadeInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    }
    
    // 移动端特定动画
    .mobile-header {
      animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);
    }
    
    .form-card {
      animation: fadeInUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.2s backwards;
    }
    
    .mobile-copyright {
      animation: fadeIn 1s ease 0.6s backwards;
    }
    
    .wave-container {
      animation: fadeIn 2s ease;
    }
  }
}

// 移动端波浪背景样式
.wave-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  transition: var(--transition-standard);
  
  .wave {
    position: absolute;
    width: 200%;
    height: 200px;
    top: -120px;
    left: -50%;
    border-radius: 50%;
    opacity: 0.4;
    
    &.wave-1 {
      background: $kingdee-gradient-vibrant;
      animation: wave 20s infinite linear;
      z-index: 1;
      opacity: 0.2;
      top: -150px;
    }
    
    &.wave-2 {
      background: $kingdee-gradient;
      animation: wave 15s infinite linear reverse;
      z-index: 2;
      opacity: 0.15;
      top: -130px;
      height: 180px;
    }
    
    &.wave-3 {
      background: $kingdee-gradient-accent;
      animation: wave 25s infinite linear;
      z-index: 3;
      opacity: 0.1;
      top: -140px;
      height: 160px;
    }
  }
}

// 波浪动画
@keyframes wave {
  0% {
    transform: translateX(0) translateY(0) rotate(0);
  }
  50% {
    transform: translateX(-25%) translateY(10px) rotate(3deg);
  }
  100% {
    transform: translateX(0) translateY(0) rotate(0);
  }
}
</style>
