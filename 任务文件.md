# 上下文
文件名：添加驳回按钮任务.md
创建于：2024-12-19
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
在审批界面加一个驳回按钮

# 项目概述
这是一个基于Vue.js的工时报告系统，包含工时汇报、审批和统计功能。当前审批界面只有"审核通过"按钮，需要添加驳回功能以完善审批流程。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
通过代码分析发现：

1. **审批界面结构**：
   - 主要文件：`src/view/WorkReportApproval.vue`
   - 当前只有"审核通过"按钮（第119-121行）
   - 已有驳回相关的状态处理逻辑（'rejected'状态）
   - 常用审批意见中包含驳回相关内容（第273-275行）

2. **API支持**：
   - `src/api/approval.js`中已有`updateApprovalStatus`函数
   - 支持状态'D'表示驳回（第107行注释）
   - 已有`workflowAudit`函数用于工作流审核

3. **现有功能**：
   - 审批意见弹窗已存在
   - 状态管理已完善
   - 常用意见模板已包含驳回选项

4. **缺失功能**：
   - 界面上缺少驳回按钮
   - 缺少驳回操作的处理方法
   - 需要区分驳回和审核通过的处理逻辑

# 提议的解决方案 (由 INNOVATE 模式填充)

# 实施计划 (由 PLAN 模式生成)

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)

# 任务进度 (由 EXECUTE 模式在每步完成后追加)

*   2024-12-19
    *   步骤：修改驳回功能，支持直接驳回待审批状态的项目
    *   修改：
        - 第113行：在待审批状态的item-footer中添加驳回按钮
        - 第47行：修改复选框显示条件，支持待审批和已确认状态
        - 第325-334行：修改selectedItems和selectedConfirmedItems计算属性，支持多种状态选择
        - 第515行：修改toggleSelectAll方法，支持待审批和已确认状态
        - 第340-345行：修改isAllSelected计算属性，支持多种状态的全选逻辑
        - 第36行：更新批量操作提示文本，改为更通用的描述
    *   更改摘要：修改了驳回功能，现在可以直接对待审批状态的项目进行驳回，无需先确认工时
    *   原因：用户反馈驳回不用走确认工时这一步
    *   阻碍：无
    *   状态：待确认

# 最终审查 (由 REVIEW 模式填充) 