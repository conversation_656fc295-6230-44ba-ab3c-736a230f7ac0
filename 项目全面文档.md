# 工时汇报系统 - 全面项目文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [功能模块](#功能模块)
- [技术实现](#技术实现)
- [API接口](#api接口)
- [数据流程](#数据流程)
- [部署指南](#部署指南)
- [开发规范](#开发规范)
- [维护指南](#维护指南)
- [常见问题](#常见问题)

---

## 🎯 项目概述

### 项目简介
工时汇报系统是一个基于Vue.js开发的企业级移动端工时管理应用，集成金蝶ERP系统，为企业提供完整的工时汇报、审批和统计解决方案。

### 核心特性
- 📱 **移动端优先**：响应式设计，支持多设备访问
- 🔄 **实时同步**：与金蝶ERP系统深度集成
- 📊 **可视化统计**：多维度数据展示和分析
- ✅ **智能审批**：批量操作，提升审批效率
- 🎨 **直观界面**：日历视图，状态一目了然

### 项目信息
- **项目名称**：work-hour-report
- **版本**：0.1.0
- **开发框架**：Vue 2.6.14
- **UI组件库**：Vant 2.13.7 + Element UI 2.15.14
- **状态管理**：Vuex 3.6.2
- **路由管理**：Vue Router 3.6.5

---

## 🏗️ 技术架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
├─────────────────────────────────────────────────────────────┤
│  Vue.js 2.x + Vant UI + Element UI + Vue Router + Vuex     │
├─────────────────────────────────────────────────────────────┤
│                        业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│     工时汇报    │    审批管理    │    统计分析    │    用户管理     │
├─────────────────────────────────────────────────────────────┤
│                        数据访问层                              │
├─────────────────────────────────────────────────────────────┤
│              Axios HTTP Client + API 封装                   │
├─────────────────────────────────────────────────────────────┤
│                        后端服务层                              │
├─────────────────────────────────────────────────────────────┤
│                      金蝶 ERP 系统                            │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈详情

#### 前端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| Vue.js | 2.6.14 | 核心框架 |
| Vant | 2.13.7 | 移动端UI组件库 |
| Element UI | 2.15.14 | PC端UI组件库 |
| Vue Router | 3.6.5 | 路由管理 |
| Vuex | 3.6.2 | 状态管理 |
| Axios | 1.9.0 | HTTP客户端 |
| Swiper | 5.4.5 | 轮播组件 |
| Sass | 1.88.0 | CSS预处理器 |

#### 构建工具
| 工具 | 版本 | 用途 |
|------|------|------|
| Vue CLI | 5.0.0 | 项目脚手架 |
| Babel | 7.12.16 | JavaScript编译 |
| ESLint | 7.32.0 | 代码规范检查 |
| PostCSS | - | CSS后处理 |
| postcss-px-to-viewport | 1.1.1 | 移动端适配 |

#### 生产环境
| 技术 | 版本 | 用途 |
|------|------|------|
| Express | 5.1.0 | 静态文件服务 |
| PM2 | - | 进程管理 |
| Node.js | 14+ | 运行环境 |

---

## 🔧 功能模块

### 1. 用户认证模块
- **登录功能**：支持用户名/密码登录
- **会话管理**：自动检测SessionId有效性
- **权限控制**：基于角色的访问控制
- **自动刷新**：SessionId过期自动续期

### 2. 工时汇报模块
- **日历视图**：周/月/年三种模式
- **任务管理**：项目任务选择和创建
- **工时录入**：汇报工时和加班工时
- **内容编辑**：工作内容详细描述
- **进度跟踪**：任务完成进度管理
- **状态流转**：暂存→提交→审核流程

### 3. 审批管理模块
- **待审批列表**：分页展示待处理项目
- **批量操作**：支持批量审核和驳回
- **审批意见**：预设和自定义审批意见
- **工时确认**：确认实际工作工时
- **状态筛选**：按状态和时间范围筛选

### 4. 统计分析模块
- **多维统计**：日/周/月统计视图
- **数据可视化**：图表展示工时分布
- **项目分析**：按项目统计工时情况
- **个人报表**：个人工时汇总报告
- **导出功能**：支持数据导出

### 5. 系统管理模块
- **用户信息**：个人信息管理
- **系统设置**：应用配置管理
- **缓存管理**：数据缓存策略
- **错误处理**：统一错误处理机制

---

## 💻 技术实现

### 项目结构
```
work-hour-report/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口封装
│   │   ├── auth.js        # 认证相关API
│   │   ├── report.js      # 工时汇报API
│   │   ├── approval.js    # 审批管理API
│   │   └── config.js      # API配置
│   ├── assets/            # 静态资源
│   │   ├── images/        # 图片资源
│   │   └── kingdee-theme.scss # 主题样式
│   ├── components/        # 公共组件
│   │   ├── BackButton.vue # 返回按钮
│   │   ├── MobileHeader.vue # 移动端头部
│   │   ├── FeatureGrid.vue # 功能网格
│   │   └── ...
│   ├── mixins/            # 混入
│   │   └── refreshMixin.js # 刷新混入
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── store/             # 状态管理
│   │   ├── index.js       # Vuex store
│   │   └── modules/       # 模块化store
│   ├── utils/             # 工具函数
│   │   ├── cache.js       # 缓存管理
│   │   └── session.js     # 会话管理
│   ├── view/              # 页面组件
│   │   ├── login.vue      # 登录页
│   │   ├── Main.vue       # 主页
│   │   ├── WorkReport.vue # 工时汇报
│   │   ├── WorkReportApproval.vue # 审批管理
│   │   └── WorkReportStatistics.vue # 统计分析
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── package.json           # 项目配置
├── vue.config.js          # Vue CLI配置
├── server.js              # 生产服务器
├── ecosystem.config.js    # PM2配置
└── DEPLOY.md             # 部署文档
```

### 核心技术实现

#### 1. 会话管理机制
```javascript
// src/utils/session.js
export function ensureValidSession() {
  return new Promise((resolve, reject) => {
    if (isSessionValid()) {
      resolve(getSessionId());
    } else {
      refreshSessionId()
        .then(sessionId => resolve(sessionId))
        .catch(error => reject(error));
    }
  });
}
```

#### 2. 缓存策略
```javascript
// src/utils/cache.js
export class CacheManager {
  constructor(defaultTTL = 5 * 60 * 1000) { // 5分钟默认TTL
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
  }
  
  set(key, value, ttl = this.defaultTTL) {
    const expireTime = Date.now() + ttl;
    this.cache.set(key, { value, expireTime });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expireTime) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
}
```

#### 3. 响应式设计
```scss
// postcss.config.js 配置
module.exports = {
  plugins: {
    'postcss-px-to-viewport': {
      viewportWidth: 375,
      viewportHeight: 667,
      unitPrecision: 3,
      viewportUnit: 'vw',
      selectorBlackList: ['.ignore'],
      minPixelValue: 1,
      mediaQuery: false
    }
  }
}
```

#### 4. 状态管理
```javascript
// src/store/index.js
export default new Vuex.Store({
  state: {
    userInfo: null,
    isLoggedIn: !!localStorage.getItem('UserToken'),
    fbillno: null
  },
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      state.isLoggedIn = true;
    },
    SET_FBILLNO(state, fbillno) {
      state.fbillno = fbillno;
    }
  },
  actions: {
    async login({ commit }, credentials) {
      // 登录逻辑
    }
  }
});
```

---

## 🔌 API接口

### 接口基础配置
```javascript
// src/api/config.js
const API_BASE_URL = '/api';
const KINGDEE_API_BASE = '/Kingdee.BOS.WebApi.ServicesStub';

// 请求拦截器
axios.interceptors.request.use(config => {
  const sessionId = getSessionId();
  if (sessionId) {
    config.headers['SessionId'] = sessionId;
  }
  return config;
});
```

### 主要接口列表

#### 认证相关
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户登录 | POST | `/api/login` | 用户身份验证 |
| 获取SessionId | POST | `/Kingdee.BOS.WebApi.ServicesStub/AuthService.ValidateUser.common.kdsvc` | 获取金蝶SessionId |
| 刷新SessionId | POST | `/Kingdee.BOS.WebApi.ServicesStub/AuthService.RefreshSessionId.common.kdsvc` | 刷新会话 |

#### 工时汇报
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取项目列表 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.GetProjectList.common.kdsvc` | 获取可选项目 |
| 获取任务列表 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.GetTaskList.common.kdsvc` | 获取项目任务 |
| 保存工时 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.SaveWorkHour.common.kdsvc` | 保存工时记录 |
| 提交工时 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.SubmitWorkHour.common.kdsvc` | 提交审批 |

#### 审批管理
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取待审批列表 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.GetApprovalList.common.kdsvc` | 获取审批列表 |
| 审批通过 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.ApproveWorkHour.common.kdsvc` | 审批通过 |
| 审批驳回 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.RejectWorkHour.common.kdsvc` | 审批驳回 |

#### 统计分析
| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取统计数据 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.GetStatistics.common.kdsvc` | 获取统计报表 |
| 获取日历状态 | POST | `/Kingdee.BOS.WebApi.ServicesStub/App7Service.GetCalendarStatus.common.kdsvc` | 获取日历工时状态 |

---

## 📊 数据流程

### 工时汇报流程
```mermaid
graph TD
    A[用户登录] --> B[选择日期]
    B --> C[选择项目/任务]
    C --> D[填写工作内容]
    D --> E[录入工时]
    E --> F[保存暂存]
    F --> G[提交审批]
    G --> H[等待审批]
    H --> I{审批结果}
    I -->|通过| J[审批完成]
    I -->|驳回| K[重新修改]
    K --> F
```

### 数据状态流转
```
暂存(A) → 已提交(B) → 已审核(C)
   ↑                      ↓
   └── 重新审核(D) ←────────┘
```

### 权限控制矩阵
| 角色 | 汇报 | 审批 | 统计 | 管理 |
|------|------|------|------|------|
| 普通用户 | ✅ | ❌ | 个人 | ❌ |
| 项目经理 | ✅ | ✅ | 项目 | ❌ |
| 部门经理 | ✅ | ✅ | 部门 | ❌ |
| 系统管理员 | ✅ | ✅ | 全部 | ✅ |

---

## 🚀 部署指南

### 环境要求
- **Node.js**: 14.x 或更高版本
- **NPM**: 6.x 或更高版本
- **PM2**: 全局安装的进程管理工具
- **服务器**: Linux/Windows Server
- **端口**: 3000（可配置）

### 开发环境部署

#### 1. 克隆项目
```bash
git clone <repository-url>
cd work-hour-report
```

#### 2. 安装依赖
```bash
npm install
```

#### 3. 配置环境
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

#### 4. 启动开发服务器
```bash
npm run serve
```

访问 `http://localhost:8080` 查看应用。

### 生产环境部署

#### 1. 构建生产版本
```bash
npm run build
```

#### 2. 安装PM2
```bash
npm install -g pm2
```

#### 3. 配置PM2
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'work-hour-report',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

#### 4. 启动应用
```bash
pm2 start ecosystem.config.js
```

#### 5. 设置开机自启
```bash
pm2 startup
pm2 save
```

### Docker部署

#### Dockerfile
```dockerfile
FROM node:14-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["node", "server.js"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  work-hour-report:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

### 反向代理配置

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 📝 开发规范

### 代码规范

#### 1. 命名规范
- **文件命名**: 使用PascalCase（组件）或camelCase（工具函数）
- **变量命名**: 使用camelCase
- **常量命名**: 使用UPPER_SNAKE_CASE
- **CSS类名**: 使用kebab-case

#### 2. 组件规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

#### 3. API调用规范
```javascript
// 统一错误处理
async function apiCall() {
  try {
    const response = await api.getData();
    return response.data;
  } catch (error) {
    console.error('API调用失败:', error);
    this.$toast(error.message || '操作失败');
    throw error;
  }
}
```

### Git规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(report): 添加工时汇报功能

- 实现日历视图
- 添加任务选择器
- 支持工时录入和提交

Closes #123
```

### 测试规范

#### 单元测试
```javascript
// tests/unit/utils.spec.js
import { formatDate } from '@/utils/date';

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2023-12-19');
    expect(formatDate(date)).toBe('2023-12-19');
  });
});
```

#### E2E测试
```javascript
// tests/e2e/login.spec.js
describe('Login Flow', () => {
  it('should login successfully', () => {
    cy.visit('/login');
    cy.get('[data-cy=username]').type('testuser');
    cy.get('[data-cy=password]').type('password');
    cy.get('[data-cy=login-btn]').click();
    cy.url().should('include', '/main');
  });
});
```

---

## 🔧 维护指南

### 日常维护

#### 1. 日志监控
```bash
# 查看应用日志
pm2 logs work-hour-report

# 查看错误日志
pm2 logs work-hour-report --err

# 实时监控
pm2 monit
```

#### 2. 性能监控
```bash
# 查看应用状态
pm2 status

# 查看详细信息
pm2 show work-hour-report

# 重启应用
pm2 restart work-hour-report
```

#### 3. 数据备份
```bash
# 备份配置文件
cp ecosystem.config.js ecosystem.config.js.bak

# 备份应用代码
tar -czf work-hour-report-$(date +%Y%m%d).tar.gz .
```

### 故障排查

#### 常见问题及解决方案

1. **SessionId过期**
   - 现象：API调用返回401错误
   - 解决：检查session.js中的刷新逻辑

2. **内存泄漏**
   - 现象：应用内存使用持续增长
   - 解决：检查事件监听器是否正确移除

3. **API超时**
   - 现象：请求响应时间过长
   - 解决：检查网络连接和后端服务状态

4. **缓存问题**
   - 现象：数据不更新
   - 解决：清理浏览器缓存或重启应用

### 更新升级

#### 1. 依赖更新
```bash
# 检查过期依赖
npm outdated

# 更新依赖
npm update

# 安全更新
npm audit fix
```

#### 2. 版本发布
```bash
# 更新版本号
npm version patch|minor|major

# 构建新版本
npm run build

# 部署更新
pm2 reload work-hour-report
```

---

## ❓ 常见问题

### Q1: 如何修改API服务器地址？
**A**: 修改 `vue.config.js` 中的代理配置和 `server.js` 中的目标地址。

### Q2: 如何添加新的UI组件？
**A**: 在 `src/components/` 目录下创建新组件，并在需要的地方引入使用。

### Q3: 如何自定义主题颜色？
**A**: 修改 `src/assets/kingdee-theme.scss` 文件中的颜色变量。

### Q4: 如何处理跨域问题？
**A**: 开发环境通过 `vue.config.js` 配置代理，生产环境通过 `server.js` 配置。

### Q5: 如何优化应用性能？
**A**: 
- 使用路由懒加载
- 实现组件级缓存
- 优化图片资源
- 启用Gzip压缩

### Q6: 如何调试移动端问题？
**A**: 
- 使用Chrome DevTools的设备模拟
- 通过vconsole添加移动端调试面板
- 使用真机调试工具

---

## 📞 技术支持

### 开发团队联系方式
- **项目负责人**: [联系信息]
- **技术支持**: [联系信息]
- **问题反馈**: [Issue地址]

### 相关文档
- [Vue.js官方文档](https://vuejs.org/)
- [Vant组件库文档](https://vant-contrib.gitee.io/vant/v2/)
- [金蝶API文档](内部文档链接)

---
