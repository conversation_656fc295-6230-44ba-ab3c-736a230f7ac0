<template>
  <div class="banner-swiper">
    <swiper :options="swiperOptions">
      <swiper-slide v-for="banner in banners" :key="banner.id">
        <div class="banner-item">
          <img :src="banner.image" :alt="banner.title" class="banner-image" />
          <div class="banner-title" v-if="banner.title">{{ banner.title }}</div>
        </div>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { Swiper, SwiperSlide } from 'vue-awesome-swiper';

export default {
  name: 'BannerSwiper',
  components: {
    Swiper,
    SwiperSlide
  },
  computed: {
    ...mapState({
      banners: state => state.banners
    })
  },
  data() {
    return {
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination',
          dynamicBullets: true
        },
        loop: true,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false
        }
      }
    };
  }
};
</script>

<style lang="scss" scoped>
.banner-swiper {
  width: 100%;
  padding: 10px 15px;
  
  .swiper-container {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .banner-item {
    position: relative;
    width: 100%;
    height: 140px;
    
    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .banner-title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
      color: #fff;
      padding: 10px 15px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

// 响应式设计 - 桌面端适配
@media screen and (min-width: 768px) {
  .banner-swiper {
    max-width: 768px;
    margin: 0 auto;
    
    .banner-item {
      height: 180px;
      
      .banner-title {
        font-size: 18px;
      }
    }
  }
}
</style> 