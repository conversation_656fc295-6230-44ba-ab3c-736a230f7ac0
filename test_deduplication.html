<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务去重功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .task-list {
            margin: 10px 0;
        }
        .task-item {
            padding: 8px;
            margin: 5px 0;
            background: #f9f9f9;
            border-left: 3px solid #007bff;
            border-radius: 3px;
        }
        .duplicate {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .unique {
            border-left-color: #28a745;
            background: #f5fff5;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>任务去重功能测试</h1>
        <p>这个页面用于测试WorkReport.vue中的deduplicateTasksByName方法</p>

        <div class="test-section">
            <div class="test-title">测试场景1: 基本重复任务去重</div>
            <button onclick="testBasicDeduplication()">运行测试</button>
            <div id="test1-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景2: 混合任务名称和项目名称</div>
            <button onclick="testMixedNames()">运行测试</button>
            <div id="test2-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景3: 空名称任务处理</div>
            <button onclick="testEmptyNames()">运行测试</button>
            <div id="test3-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景4: ID比较保留最新任务</div>
            <button onclick="testIdComparison()">运行测试</button>
            <div id="test4-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 模拟WorkReport.vue中的去重方法
        function deduplicateTasksByName(tasks) {
            if (!tasks || tasks.length === 0) return [];
            
            const taskMap = new Map();
            const duplicateCount = {};
            
            // 统计重复任务
            tasks.forEach(task => {
                const taskName = task.taskName || task.projectName || '';
                if (taskName && taskName.trim() !== '') {
                    duplicateCount[taskName] = (duplicateCount[taskName] || 0) + 1;
                }
            });
            
            // 记录重复的任务名称
            const duplicateNames = Object.keys(duplicateCount).filter(name => duplicateCount[name] > 1);
            if (duplicateNames.length > 0) {
                console.log(`发现重复任务: ${duplicateNames.join(', ')}`);
            }
            
            // 去重处理：对于相同名称的任务，保留最新的（ID最大的）
            tasks.forEach(task => {
                const taskName = task.taskName || task.projectName || '';
                
                if (!taskName || taskName.trim() === '') {
                    // 如果任务名称为空，直接添加（使用随机key避免覆盖）
                    const randomKey = `empty_${task.id || Date.now()}_${Math.random()}`;
                    taskMap.set(randomKey, task);
                    return;
                }
                
                const existingTask = taskMap.get(taskName);
                if (!existingTask) {
                    // 第一次遇到这个任务名称
                    taskMap.set(taskName, task);
                } else {
                    // 已存在相同名称的任务，比较ID保留最新的
                    const currentTaskId = parseInt(task.id) || 0;
                    const existingTaskId = parseInt(existingTask.id) || 0;
                    
                    if (currentTaskId > existingTaskId) {
                        console.log(`替换重复任务 "${taskName}": ${existingTask.id} -> ${task.id}`);
                        taskMap.set(taskName, task);
                    } else {
                        console.log(`保留原任务 "${taskName}": ${existingTask.id} (跳过 ${task.id})`);
                    }
                }
            });
            
            const uniqueTasks = Array.from(taskMap.values());
            const removedCount = tasks.length - uniqueTasks.length;
            
            if (removedCount > 0) {
                console.log(`任务去重完成: 移除${removedCount}个重复任务，保留${uniqueTasks.length}个唯一任务`);
            }
            
            return uniqueTasks;
        }

        function testBasicDeduplication() {
            console.log('=== 测试场景1: 基本重复任务去重 ===');
            
            const testTasks = [
                { id: '1', taskName: '开发登录功能', projectName: '项目A', date: '2025-01-21' },
                { id: '2', taskName: '开发登录功能', projectName: '项目A', date: '2025-01-21' },
                { id: '3', taskName: '测试用户界面', projectName: '项目B', date: '2025-01-21' },
                { id: '4', taskName: '开发登录功能', projectName: '项目A', date: '2025-01-21' },
                { id: '5', taskName: '编写文档', projectName: '项目C', date: '2025-01-21' }
            ];
            
            const result = deduplicateTasksByName(testTasks);
            
            const resultDiv = document.getElementById('test1-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <strong>原始任务数量:</strong> ${testTasks.length}<br>
                <strong>去重后任务数量:</strong> ${result.length}<br>
                <strong>移除重复任务数量:</strong> ${testTasks.length - result.length}<br>
                <strong>保留的任务:</strong><br>
                ${result.map(task => `- ID: ${task.id}, 任务: ${task.taskName || task.projectName}`).join('<br>')}
            `;
        }

        function testMixedNames() {
            console.log('=== 测试场景2: 混合任务名称和项目名称 ===');
            
            const testTasks = [
                { id: '1', taskName: '开发功能A', projectName: '项目X', date: '2025-01-21' },
                { id: '2', taskName: '', projectName: '开发功能A', date: '2025-01-21' }, // 项目名称与上面任务名称相同
                { id: '3', taskName: '测试功能B', projectName: '项目Y', date: '2025-01-21' },
                { id: '4', taskName: '', projectName: '测试功能B', date: '2025-01-21' }, // 项目名称与上面任务名称相同
            ];
            
            const result = deduplicateTasksByName(testTasks);
            
            const resultDiv = document.getElementById('test2-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <strong>原始任务数量:</strong> ${testTasks.length}<br>
                <strong>去重后任务数量:</strong> ${result.length}<br>
                <strong>保留的任务:</strong><br>
                ${result.map(task => `- ID: ${task.id}, 任务: ${task.taskName || '(空)'}, 项目: ${task.projectName}`).join('<br>')}
            `;
        }

        function testEmptyNames() {
            console.log('=== 测试场景3: 空名称任务处理 ===');
            
            const testTasks = [
                { id: '1', taskName: '', projectName: '', date: '2025-01-21' },
                { id: '2', taskName: '', projectName: '', date: '2025-01-21' },
                { id: '3', taskName: '正常任务', projectName: '项目A', date: '2025-01-21' },
                { id: '4', taskName: '', projectName: '', date: '2025-01-21' }
            ];
            
            const result = deduplicateTasksByName(testTasks);
            
            const resultDiv = document.getElementById('test3-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <strong>原始任务数量:</strong> ${testTasks.length}<br>
                <strong>去重后任务数量:</strong> ${result.length}<br>
                <strong>说明:</strong> 空名称任务应该都被保留（因为无法判断是否重复）<br>
                <strong>保留的任务:</strong><br>
                ${result.map(task => `- ID: ${task.id}, 任务: ${task.taskName || '(空)'}, 项目: ${task.projectName || '(空)'}`).join('<br>')}
            `;
        }

        function testIdComparison() {
            console.log('=== 测试场景4: ID比较保留最新任务 ===');
            
            const testTasks = [
                { id: '100', taskName: '相同任务', projectName: '项目A', date: '2025-01-21' },
                { id: '200', taskName: '相同任务', projectName: '项目A', date: '2025-01-21' },
                { id: '50', taskName: '相同任务', projectName: '项目A', date: '2025-01-21' },
                { id: '300', taskName: '相同任务', projectName: '项目A', date: '2025-01-21' }
            ];
            
            const result = deduplicateTasksByName(testTasks);
            
            const resultDiv = document.getElementById('test4-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `
                <strong>原始任务数量:</strong> ${testTasks.length}<br>
                <strong>去重后任务数量:</strong> ${result.length}<br>
                <strong>应该保留ID最大的任务:</strong> 300<br>
                <strong>实际保留的任务:</strong><br>
                ${result.map(task => `- ID: ${task.id}, 任务: ${task.taskName}`).join('<br>')}
            `;
        }
    </script>
</body>
</html>
