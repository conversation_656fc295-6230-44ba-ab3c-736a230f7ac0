<template>
  <div class="work-content-editor" :class="{ 'disabled': disabled }" @click.stop>
    <!-- 编辑状态 -->
    <div
      v-if="isEditing && !disabled"
      class="editor-container"
    >
      <textarea
        v-model="localContent"
        @blur="handleBlur"
        @keyup.ctrl.enter="handleSave"
        @keyup.esc="handleCancel"
        @input="handleInput"
        ref="textarea"
        :placeholder="placeholder"
        class="content-textarea"
        :style="textareaStyle"
      ></textarea>
    </div>

    <!-- 显示状态 -->
    <div
      v-else
      class="content-display"
      @click="handleEdit"
      :class="{ 'empty-content': !content }"
    >
      {{ content || placeholder }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkContentEditor',
  props: {
    content: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '点击编辑工作内容'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 200
    },
    minHeight: {
      type: Number,
      default: 20
    }
  },
  
  data() {
    return {
      isEditing: false,
      localContent: '',
      originalContent: ''
    }
  },
  
  computed: {
    textareaStyle() {
      return {
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`
      }
    }
  },
  
  watch: {
    content: {
      handler(newVal) {
        this.localContent = newVal || '';
      },
      immediate: true
    }
  },
  
  methods: {
    handleEdit() {
      if (this.disabled) return;
      
      this.originalContent = this.content || '';
      this.localContent = this.content || '';
      this.isEditing = true;
      
      this.$nextTick(() => {
        if (this.$refs.textarea) {
          this.$refs.textarea.focus();
          this.autoResize();
        }
      });
      
      this.$emit('edit-start');
    },
    
    handleBlur() {
      // 直接保存，不使用延迟
      if (this.isEditing) {
        this.handleSave();
      }
    },
    
    handleSave() {
      if (!this.isEditing) return;
      
      const newContent = this.localContent.trim();
      
      // 如果内容有变化，触发更新事件
      if (newContent !== this.originalContent) {
        this.$emit('update:content', newContent);
        this.$emit('content-change', newContent);
      }
      
      this.isEditing = false;
      this.$emit('edit-end', newContent);
    },
    
    handleCancel() {
      this.localContent = this.originalContent;
      this.isEditing = false;
      this.$emit('edit-cancel');
    },
    
    handleInput() {
      this.autoResize();
      this.$emit('input', this.localContent);
    },
    
    autoResize() {
      const textarea = this.$refs.textarea;
      if (!textarea) return;
      
      // 保存当前宽度，防止宽度变化
      const currentWidth = textarea.offsetWidth;
      
      // 重置高度以获取正确的scrollHeight
      textarea.style.height = 'auto';
      
      // 确保宽度保持不变
      textarea.style.width = `${currentWidth}px`;
      
      // 计算新高度
      const scrollHeight = textarea.scrollHeight;
      const newHeight = Math.min(Math.max(scrollHeight, this.minHeight), this.maxHeight);
      
      // 只设置高度，确保宽度不变
      textarea.style.height = `${newHeight}px`;
      
      // 重新设置宽度为100%以保持响应式
      this.$nextTick(() => {
        textarea.style.width = '100%';
      });
    },
    
    // 公开方法，允许父组件触发编辑
    startEdit() {
      this.handleEdit();
    },
    
    // 公开方法，允许父组件停止编辑
    stopEdit() {
      this.handleSave();
    }
  }
}
</script>

<style lang="scss" scoped>
.work-content-editor {
  width: 100%;
  min-height: 20px;
  box-sizing: border-box;
  
  .editor-container {
    width: 100%;
    box-sizing: border-box;
    
    .content-textarea {
      width: 100%;
      max-width: 100%;
      background-color: #fff;
      border: 1px solid #1989fa;
      border-radius: 4px;
      padding: 4px 6px;
      font-size: 12px;
      line-height: 1.3;
      resize: none;
      outline: none;
      font-family: inherit;
      overflow-y: auto;
      overflow-x: hidden;
      box-sizing: border-box;
      word-wrap: break-word;
      word-break: break-all;
      white-space: pre-wrap;
      
      &::placeholder {
        color: #c8c9cc;
        font-size: 12px;
      }
      
      &:focus {
        border-color: #1989fa;
        box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.1);
      }
    }
  }
  
  .content-display {
    width: 100%;
    max-width: 100%;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    transition: background-color 0.2s;
    background-color: #f9f9f9;
    font-size: 12px;
    line-height: 1.3;
    word-wrap: break-word;
    word-break: break-all;
    min-height: 20px;
    display: block;
    box-sizing: border-box;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-height: none;
    height: auto;
    overflow: visible;
    overflow-x: hidden;
    
    &:hover {
      background-color: #f7f8fa;
    }
    
    &:active {
      background-color: #ebedf0;
    }
    
    &.empty-content {
      color: #c8c9cc;
      display: flex;
      align-items: center;
      min-height: 20px;
    }
  }

  // 禁用状态样式
  &.disabled {
    .content-display {
      cursor: not-allowed;
      color: #c8c9cc;
      opacity: 0.6;

      &:hover {
        background-color: #f9f9f9;
      }

      &:active {
        background-color: #f9f9f9;
      }
    }
  }
}
</style> 