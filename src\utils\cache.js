/**
 * 缓存工具类
 * 用于管理工时报告数据的本地缓存
 */

// 缓存前缀，用于区分不同类型的缓存数据
const CACHE_PREFIXES = {
  WORK_REPORT: 'work_report_cache_',
  PROJECTS: 'projects_cache',
  TASKS: 'tasks_cache',
  STATISTICS: 'statistics_cache_'
};

// 默认缓存有效期（毫秒）
const DEFAULT_EXPIRY = 4 * 60 * 60 * 1000; // 4小时（更合理的默认值）

/**
 * 保存数据到缓存
 * @param {string} key 缓存键名
 * @param {any} data 要缓存的数据
 * @param {number} [expiry=DEFAULT_EXPIRY] 缓存有效期（毫秒）
 */
export function setCache(key, data, expiry = DEFAULT_EXPIRY) {
  try {
    const cacheItem = {
      data: data,
      timestamp: Date.now(),
      expiry: expiry
    };
    
    const dataSize = JSON.stringify(data).length;
    console.log(`[Cache] 准备缓存数据: ${key}, 大小: ${(dataSize / 1024).toFixed(2)} KB, 有效期: ${(expiry / 3600000).toFixed(1)}小时`);
    
    localStorage.setItem(key, JSON.stringify(cacheItem));
    console.log(`[Cache] 数据已缓存: ${key}`);
    
    // 如果是工时报告数据，打印任务数量
    if (key.startsWith(CACHE_PREFIXES.WORK_REPORT) && data?.data?.tasks) {
      const tasks = data.data.tasks;
      console.log(`[Cache] 缓存的工时报告包含 ${tasks.length} 条任务数据`);
    }
  } catch (error) {
    console.error(`[Cache] 缓存数据失败: ${error.message}`);
    
    // 如果是存储空间不足错误，尝试清理过期缓存
    if (error.name === 'QuotaExceededError' || error.code === 22) {
      console.warn('[Cache] 存储空间不足，尝试清理过期缓存');
      clearExpiredCache();
      
      // 再次尝试保存
      try {
        const cacheItem = {
          data: data,
          timestamp: Date.now(),
          expiry: expiry
        };
        
        localStorage.setItem(key, JSON.stringify(cacheItem));
        console.log(`[Cache] 清理后重新缓存成功: ${key}`);
      } catch (retryError) {
        console.error(`[Cache] 重新缓存失败: ${retryError.message}`);
      }
    }
  }
}

/**
 * 从缓存中获取数据
 * @param {string} key 缓存键名
 * @returns {Object|null} 返回缓存数据对象{data:缓存数据, age:缓存年龄(毫秒)}，如果缓存不存在或已过期则返回null
 */
export function getCache(key) {
  try {
    console.log(`[Cache] 尝试获取缓存: ${key}`);
    const cacheItemStr = localStorage.getItem(key);
    
    if (!cacheItemStr) {
      console.log(`[Cache] 未找到缓存: ${key}`);
      return null;
    }
    
    const cacheItem = JSON.parse(cacheItemStr);
    const now = Date.now();
    const age = now - cacheItem.timestamp;
    const expiryTime = new Date(cacheItem.timestamp + cacheItem.expiry).toLocaleString();
    
    // 检查缓存是否过期
    if (age > cacheItem.expiry) {
      console.log(`[Cache] 缓存已过期: ${key}, 年龄: ${(age / 1000).toFixed(2)}秒, 过期时间: ${expiryTime}`);
      localStorage.removeItem(key);
      return null;
    }
    
    // 如果是工时报告数据，打印任务数量
    if (key.startsWith(CACHE_PREFIXES.WORK_REPORT) && cacheItem.data?.data?.tasks) {
      const tasks = cacheItem.data.data.tasks;
      console.log(`[Cache] 缓存命中: ${key}, 年龄: ${(age / 1000).toFixed(2)}秒, 包含 ${tasks.length} 条任务数据, 过期时间: ${expiryTime}`);
    } else {
      console.log(`[Cache] 缓存命中: ${key}, 年龄: ${(age / 1000).toFixed(2)}秒, 过期时间: ${expiryTime}`);
    }
    
    return {
      data: cacheItem.data,
      age: age
    };
  } catch (error) {
    console.error(`[Cache] 获取缓存失败: ${error.message}`, error);
    return null;
  }
}

/**
 * 清除指定缓存
 * @param {string} key 缓存键名
 */
export function removeCache(key) {
  try {
    localStorage.removeItem(key);
    console.log(`[Cache] 缓存已移除: ${key}`);
  } catch (error) {
    console.error(`[Cache] 移除缓存失败: ${error.message}`);
  }
}

/**
 * 清除所有工时报告相关缓存
 */
export function clearAllWorkReportCache() {
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出工时报告缓存的键
    const cacheKeys = keys.filter(key => 
      key.startsWith(CACHE_PREFIXES.WORK_REPORT)
    );
    
    // 删除每个缓存
    cacheKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`[Cache] 已清除${cacheKeys.length}个工时报告缓存`);
  } catch (error) {
    console.error(`[Cache] 清除工时报告缓存失败: ${error.message}`);
  }
}

/**
 * 清除所有过期缓存
 */
export function clearExpiredCache() {
  try {
    const now = Date.now();
    const keys = Object.keys(localStorage);
    let clearedCount = 0;
    
    keys.forEach(key => {
      // 仅处理我们的缓存前缀的项
      if (isAppCache(key)) {
        try {
          const cacheItemStr = localStorage.getItem(key);
          if (cacheItemStr) {
            const cacheItem = JSON.parse(cacheItemStr);
            const age = now - cacheItem.timestamp;
            
            if (age > cacheItem.expiry) {
              localStorage.removeItem(key);
              clearedCount++;
            }
          }
        } catch (parseError) {
          // 如果解析失败，也删除此项
          localStorage.removeItem(key);
          clearedCount++;
        }
      }
    });
    
    console.log(`[Cache] 已清除${clearedCount}个过期缓存`);
  } catch (error) {
    console.error(`[Cache] 清除过期缓存失败: ${error.message}`);
  }
}

/**
 * 获取工时报告缓存键名
 * @param {string} date 报告日期 YYYY-MM-DD
 * @param {string} username 用户账号
 * @returns {string} 缓存键名
 */
export function getWorkReportCacheKey(date, username) {
  return `${CACHE_PREFIXES.WORK_REPORT}${date}_${username}`;
}

/**
 * 获取统计数据缓存键名
 * @param {Object} params 统计查询参数
 * @returns {string} 缓存键名
 */
export function getStatisticsCacheKey(params) {
  // 将参数对象转换为字符串，确保缓存键的唯一性
  const paramStr = JSON.stringify(params);
  // 使用简单的哈希方法生成短的标识符
  let hash = 0;
  for (let i = 0; i < paramStr.length; i++) {
    const char = paramStr.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return `${CACHE_PREFIXES.STATISTICS}${Math.abs(hash).toString(36)}`;
}

/**
 * 检查是否为应用缓存
 * @param {string} key 缓存键名
 * @returns {boolean} 是否为应用缓存
 */
function isAppCache(key) {
  return (
    key.startsWith(CACHE_PREFIXES.WORK_REPORT) ||
    key === CACHE_PREFIXES.PROJECTS ||
    key === CACHE_PREFIXES.TASKS ||
    key.startsWith(CACHE_PREFIXES.STATISTICS)
  );
}

/**
 * 获取缓存统计信息
 * @returns {Object} 统计信息对象
 */
export function getCacheStats() {
  try {
    const keys = Object.keys(localStorage);
    const now = Date.now();
    
    // 筛选出应用缓存键
    const appCacheKeys = keys.filter(key => isAppCache(key));
    
    let totalSize = 0;
    let expiredCount = 0;
    let validCount = 0;
    
    appCacheKeys.forEach(key => {
      const value = localStorage.getItem(key);
      totalSize += (key.length + value.length) * 2; // 粗略估计字符串占用的字节数
      
      try {
        const cacheItem = JSON.parse(value);
        const age = now - cacheItem.timestamp;
        
        if (age > cacheItem.expiry) {
          expiredCount++;
        } else {
          validCount++;
        }
      } catch (e) {
        expiredCount++; // 解析失败的视为过期
      }
    });
    
    return {
      totalCount: appCacheKeys.length,
      validCount: validCount,
      expiredCount: expiredCount,
      totalSize: `${(totalSize / 1024).toFixed(2)} KB`,
      estimatedRemaining: `${((5 * 1024 * 1024 - totalSize) / (1024 * 1024)).toFixed(2)} MB` // 假设最大5MB
    };
  } catch (error) {
    console.error(`[Cache] 获取缓存统计失败: ${error.message}`);
    return {
      error: error.message
    };
  }
} 