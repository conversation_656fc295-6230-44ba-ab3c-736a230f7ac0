# 工时系统部署说明

## 环境要求
- Node.js 14.x 或更高版本
- PM2 进程管理工具
- 服务器需要开放 3000 端口（或自定义端口）

## 部署步骤

1. 安装依赖
```bash
npm install
```

2. 构建生产环境代码
```bash
npm run build
```

3. 安装 PM2（如果未安装）
```bash
npm install -g pm2
```

4. 启动服务
```bash
pm2 start ecosystem.config.js
```

## 常用命令

- 查看应用状态：`pm2 status`
- 查看应用日志：`pm2 logs work-hour-report`
- 重启应用：`pm2 restart work-hour-report`
- 停止应用：`pm2 stop work-hour-report`

## 注意事项

1. 确保服务器已安装 Node.js 和 PM2
2. 确保服务器防火墙已开放相应端口
3. 如需修改端口，请同时更新 `ecosystem.config.js` 和 `server.js` 中的端口配置
4. 生产环境 API 地址配置在 `server.js` 中，请根据实际情况修改 