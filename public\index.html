<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <link rel="icon" href="logo.png">
    <title>工时汇报</title>
    
    <!-- 防护脚本：防止第三方脚本引起的jQuery错误 -->
    <script>
      (function() {
        // 如果页面中没有jQuery，但有脚本尝试使用$，则提供一个空的实现避免错误
        if (typeof window.$ === 'undefined' && typeof window.jQuery === 'undefined') {
          window.$ = window.jQuery = function() {
            console.warn('检测到尝试使用jQuery，但项目未包含jQuery库。这可能是由浏览器插件或用户脚本引起的。');
            return {
              ready: function() {},
              on: function() {},
              off: function() {},
              click: function() {},
              each: function() {},
              find: function() { return this; },
              hide: function() { return this; },
              show: function() { return this; },
              addClass: function() { return this; },
              removeClass: function() { return this; },
              css: function() { return this; },
              attr: function() { return this; },
              val: function() { return this; },
              text: function() { return this; },
              html: function() { return this; }
            };
          };
          
          // 添加jQuery常用的静态方法
          window.$.fn = window.$.prototype = window.$();
          window.$.extend = function() {};
          window.$.ready = function() {};
        }
        
        // 捕获未处理的Promise rejection，避免控制台错误
        window.addEventListener('unhandledrejection', function(event) {
          if (event.reason && event.reason.message && event.reason.message.includes('$ is not defined')) {
            console.warn('已拦截jQuery相关错误，这可能是由浏览器插件引起的:', event.reason.message);
            event.preventDefault(); // 阻止错误显示在控制台
          }
        });
        
        // 捕获全局错误
        window.addEventListener('error', function(event) {
          if (event.message && event.message.includes('$ is not defined')) {
            console.warn('已拦截jQuery相关错误，这可能是由浏览器插件引起的:', event.message);
            event.preventDefault(); // 阻止错误显示在控制台
            return true;
          }
        });
      })();
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
