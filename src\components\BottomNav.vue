<template>
  <div class="bottom-nav">
    <van-tabbar v-model="activeIndex" :fixed="true" :safe-area-inset-bottom="true">
      <van-tabbar-item icon="chat">
        <span>消息</span>
      </van-tabbar-item>
      <van-tabbar-item>
        <template #icon>
          <div class="custom-icon">
            <van-icon name="cluster" />
          </div>
        </template>
        <span>金蝶云星空</span>
      </van-tabbar-item>
      <van-tabbar-item icon="friends">
        <span>同事圈</span>
      </van-tabbar-item>
      <van-tabbar-item icon="apps-o">
        <span>应用</span>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
export default {
  name: 'BottomNav',
  data() {
    return {
      activeIndex: 1 // 默认选中"金蝶云星空"
    };
  }
};
</script>

<style lang="scss" scoped>
.bottom-nav {
  .custom-icon {
    font-size: 20px;
    color: #1989fa;
  }
  
  ::v-deep .van-tabbar-item--active {
    color: #1989fa;
  }
}

// 响应式设计 - 桌面端适配
@media screen and (min-width: 768px) {
  .bottom-nav {
    ::v-deep .van-tabbar {
      max-width: 768px;
      margin: 0 auto;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style> 