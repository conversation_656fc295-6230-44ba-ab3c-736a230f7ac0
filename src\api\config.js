import axios from 'axios';

// 全局基础设置
axios.defaults.baseURL = ''; // 使用相对路径，通过代理访问API
axios.defaults.timeout = 30000; // 30秒超时
axios.defaults.withCredentials = false; // 禁用携带cookies，解决CORS问题

// 是否正在刷新SessionId
let isRefreshingSession = false;
// 等待SessionId刷新的请求队列
let pendingRequests = [];

// 从localStorage中获取指定键的值
function getLocalStorageItem(key) {
  return localStorage.getItem(key);
}

// 请求拦截器
axios.interceptors.request.use(
  config => {
    // 获取存储的token
    const token = getLocalStorageItem('UserToken');
    // 获取存储的KDSVCSessionId
    const kdSessionId = getLocalStorageItem('KDSVCSessionId');
    
    // 如果token存在，设置到请求头
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 如果KDSVCSessionId存在，设置到请求头
    if (kdSessionId) {
      config.headers['KDService-SessionId'] = kdSessionId;
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 刷新SessionId的函数
function refreshSessionId() {
  // 避免同时触发多次刷新
  if (isRefreshingSession) {
    return new Promise((resolve, reject) => {
      // 将请求添加到等待队列
      pendingRequests.push({ resolve, reject });
    });
  }
  
  isRefreshingSession = true;
  
  // 调用登录API获取新的SessionId
  return import('./auth')
    .then(authModule => {
      return authModule.apiLogin()
        .then(sessionId => {
          isRefreshingSession = false;
          
          // 处理等待队列中的请求
          pendingRequests.forEach(request => {
            request.resolve(sessionId);
          });
          pendingRequests = [];
          
          return sessionId;
        })
        .catch(error => {
          isRefreshingSession = false;
          
          // 处理等待队列中的请求
          pendingRequests.forEach(request => {
            request.reject(error);
          });
          pendingRequests = [];
          
          return Promise.reject(error);
        });
    });
}

// 响应拦截器
axios.interceptors.response.use(
  response => {
    // 检查响应数据中是否包含会话失效信息
    const responseData = response.data;
    
    // 检查各种可能的响应格式
    let dataToCheck = '';
    if (typeof responseData === 'string') {
      dataToCheck = responseData;
    } else if (typeof responseData === 'object' && responseData !== null) {
      // 将对象转换为字符串进行检查
      dataToCheck = JSON.stringify(responseData);
    }
    
    // 检查是否包含会话失效的文字
    if (dataToCheck.includes('会话信息已丢失，请重新登录') || 
        dataToCheck.includes('会话信息已丢失') ||
        dataToCheck.includes('请重新登录')) {
      
      console.warn('[API] 检测到会话失效信息，即将跳转到登录页面');
      
      // 清除本地存储的用户信息和会话信息
      localStorage.removeItem('UserToken');
      localStorage.removeItem('KDSVCSessionId');
      localStorage.removeItem('KDSessionLastRefresh');
      localStorage.removeItem('work_report_user');
      
      // 动态导入Toast组件来显示提示
      import('vant')
        .then(vant => {
          if (vant.Toast) {
            vant.Toast.fail('会话已失效，请重新登录');
          }
        })
        .catch(() => {
          // 如果Vant不可用，使用原生alert
          alert('会话已失效，请重新登录');
        });
      
      // 延迟跳转，让用户看到提示信息
      setTimeout(() => {
        // 检查当前是否已在登录页面，避免重复跳转
        if (window.location.hash !== '#/login' && !window.location.hash.includes('login')) {
          window.location.href = '/#/login';
        }
      }, 1500);
      
      // 返回一个被拒绝的Promise，阻止后续处理
      return Promise.reject(new Error('会话已失效，请重新登录'));
    }
    
    return response;
  },
  error => {
    // 请求配置
    const config = error.config;
    
    // 防止无限重试
    if (!config || config._retry) {
      return Promise.reject(error);
    }

    // 处理401未授权错误，通常是token过期
    if (error.response && error.response.status === 401) {
      // 清除token
      localStorage.removeItem('UserToken');
      // 可以选择重定向到登录页
      setTimeout(() => {
        window.location.href = '/#/login';
      }, 1500);
      
      return Promise.reject(error);
    }
    
    // 处理SessionId相关错误
    // 金蝶API可能会返回特定错误码或错误消息表示SessionId无效
    const sessionExpired = error.response && 
      ((error.response.status === 400 && error.response.data && error.response.data.includes('会话')) || 
       (error.response.data && error.response.data.Message && error.response.data.Message.includes('登录')) ||
       (error.response.status === 403));
    
    if (sessionExpired) {
      config._retry = true;
      
      // 尝试刷新SessionId
      return refreshSessionId()
        .then(sessionId => {
          // 更新请求头中的SessionId
          config.headers['KDService-SessionId'] = sessionId;
          // 重新发送请求
          return axios(config);
        })
        .catch(refreshError => {
          // 刷新SessionId失败，可能需要重新登录
          console.error('刷新会话失败:', refreshError);
          
          // 清除SessionId
          localStorage.removeItem('KDSVCSessionId');
          
          // 如果刷新失败，则需要重新登录
          setTimeout(() => {
            window.location.href = '/#/login';
          }, 1500);
          
          return Promise.reject(refreshError);
        });
    }
    
    return Promise.reject(error);
  }
);

export default axios; 