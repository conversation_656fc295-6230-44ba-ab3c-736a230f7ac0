<template>
  <div class="back-button" @click="goBack">
    <van-icon name="arrow-left" />
  </div>
</template>

<script>
export default {
  name: 'BackButton',
  methods: {
    goBack() {
      // 直接跳转到main菜单页面
      console.log('[BackButton] 返回主菜单页面');
      this.$router.push('/main');
    }
  }
};
</script>

<style lang="scss" scoped>
.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  
  .van-icon {
    font-size: 18px;
    color: #323233;
  }
  
  &:active {
    opacity: 0.8;
  }
}
</style> 