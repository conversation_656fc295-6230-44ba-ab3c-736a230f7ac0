# 上下文
文件名：会话检查修改.md
创建于：2024-12-20 16:15
创建者：Claude AI Assistant
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
在查询的时候加一个判断，如果查询出来的数据里面含有"会话信息已丢失，请重新登录"这个文字的话就跳转到登录界面

# 项目概述
工时报告系统会话管理优化，统一处理API响应中的会话失效信息，提高用户体验和系统安全性

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 项目中已有基础的会话失效处理机制，位于 `src/api/config.js` 的响应拦截器
- 当前主要检查HTTP状态码和部分错误消息，但没有针对"会话信息已丢失，请重新登录"文字的检查
- 项目使用axios进行API请求，所有请求都会经过响应拦截器
- 需要在响应拦截器中添加统一的文字检查逻辑

# 提议的解决方案 (由 INNOVATE 模式填充)
使用响应拦截器统一检查所有API响应：
1. 在axios响应拦截器的成功回调中添加文字检查
2. 检查响应数据中是否包含"会话信息已丢失，请重新登录"等关键词
3. 如果检测到会话失效信息，清除本地存储并跳转到登录页面
4. 显示用户友好的提示信息

# 实施计划 (由 PLAN 模式生成)
**文件修改：** `src/api/config.js`

实施检查清单：
1. 修改响应拦截器的成功回调，添加会话失效文字检查
2. 添加跳转到登录页面的逻辑
3. 添加用户友好的提示信息
4. 测试检查逻辑是否正常工作

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "修改响应拦截器添加会话检查"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*   [2024-12-20 16:15]
    *   步骤：修改响应拦截器添加会话失效文字检查
    *   修改：src/api/config.js - axios.interceptors.response.use方法
    *   更改摘要：在响应拦截器中添加对"会话信息已丢失，请重新登录"等文字的检查，检测到时清除本地存储并跳转登录页面
    *   原因：执行计划步骤1-3 - 统一处理会话失效，提高用户体验
    *   阻碍：无
    *   状态：待确认

# 最终审查 (由 REVIEW 模式填充)
[待填充] 