@use "sass:color";

/* 金蝶云星空主题样式定义 */

// 主题色变量定义
$kingdee-primary: #276ff5; // 主蓝色
$kingdee-secondary: #276ff5; // 浅蓝色
$kingdee-success: #52c41a; // 成功色
$kingdee-warning: #faad14; // 警告色
$kingdee-danger: #f5222d; // 危险色
$kingdee-info: #276ff5; // 信息色
$kingdee-text-primary: #333333; // 主要文字
$kingdee-text-regular: #666666; // 常规文字
$kingdee-text-secondary: #999999; // 次要文字
$kingdee-border-color: #e8e8e8; // 边框颜色
$kingdee-bg-color: #f5f5f5; // 背景色

// 添加断点变量，便于管理
$breakpoint-xs: 480px;  // 超小屏幕（手机竖屏）
$breakpoint-sm: 768px;  // 小屏幕（手机横屏、小平板）
$breakpoint-md: 992px;  // 中等屏幕（平板）
$breakpoint-lg: 1200px; // 大屏幕（桌面）

/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: $kingdee-text-primary;
  background-color: #fff;
  -webkit-tap-highlight-color: transparent; // 移除移动端点击高亮
  touch-action: manipulation; // 移除点击延迟
  
  // 在移动设备上禁用页面缩放
  @media (max-width: $breakpoint-sm) {
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
  }
}

/* ElementUI组件样式重写 */
.el-button--primary {
  background-color: $kingdee-primary;
  border-color: $kingdee-primary;
  
  &:hover, &:focus {
    background-color: color.adjust($kingdee-primary, $lightness: 10%);
    border-color: color.adjust($kingdee-primary, $lightness: 10%);
  }
  
  &:active {
    background-color: color.adjust($kingdee-primary, $lightness: -10%);
    border-color: color.adjust($kingdee-primary, $lightness: -10%);
  }
}

.el-input__inner:focus {
  border-color: $kingdee-primary;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $kingdee-primary;
  border-color: $kingdee-primary;
}

.el-checkbox__inner:hover {
  border-color: $kingdee-primary;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: $kingdee-primary;
}

/* 移动端优化ElementUI组件 */
@media (max-width: $breakpoint-sm) {
  // 优化消息提示
  .el-message {
    min-width: 80%;
    width: auto;
  }
  
  // 优化对话框
  .el-dialog {
    width: 90% !important;
    margin-top: 10vh !important;
  }
  
  // 优化表单项间距
  .el-form-item {
    margin-bottom: 18px;
  }
  
  // 增大按钮高度
  .el-button {
    padding: 12px 20px;
    height: auto;
  }
  
  // 更明显的触摸状态
  .el-button:active {
    opacity: 0.8;
  }
  
  // 更大的单选/复选框
  .el-checkbox__inner, .el-radio__inner {
    width: 18px;
    height: 18px;
  }
}

/* 工时报告系统特定样式 */
.page-container {
  padding: 20px;
  
  // 移动端页面容器适配
  @media (max-width: $breakpoint-sm) {
    padding: 15px 10px;
  }
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  color: $kingdee-text-primary;
  margin-bottom: 20px;
  border-left: 4px solid $kingdee-primary;
  padding-left: 10px;
  
  // 移动端标题适配
  @media (max-width: $breakpoint-sm) {
    font-size: 18px;
    margin-bottom: 15px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: $kingdee-text-primary;
  margin: 15px 0;
  
  // 移动端副标题适配
  @media (max-width: $breakpoint-sm) {
    font-size: 15px;
  }
}

.kingdee-card {
  border-radius: 4px;
  border: 1px solid $kingdee-border-color;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
  
  // 移动端卡片适配
  @media (max-width: $breakpoint-sm) {
    padding: 15px 12px;
    margin-bottom: 15px;
  }
}

/* 响应式工具类 */
.hidden-xs {
  @media (max-width: 768px) {
    display: none !important;
  }
}

.visible-xs {
  @media (min-width: 769px) {
    display: none !important;
  }
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 移动端适配样式 */
@media (max-width: $breakpoint-sm) {
  // 移动端文本选择优化
  * {
    user-select: none;
  }
  
  // 允许输入框文本选择
  input, textarea {
    user-select: text;
  }
  
  // 移动端滚动优化
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
  }
  
  // 移动端点击区域优化
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
} 